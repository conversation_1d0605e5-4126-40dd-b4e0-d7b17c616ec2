import React, { useState, useEffect } from 'react'
import { useParams, useNavigate } from 'react-router-dom'
import { motion } from 'framer-motion'
import { useAuth } from '../../contexts/AuthContext'
import { useLanguage } from '../../contexts/LanguageContext'
import {
  ArrowLeft,
  Edit,
  Users,
  Calendar,
  Clock,
  FileText,
  Star,
  CheckCircle,
  AlertCircle,
  Download,
  Eye,
  MessageSquare,
  Award,
  Target,
  TrendingUp,
  User,
  Upload,
  Settings
} from 'lucide-react'
import Button from '../../components/ui/Button'
import { toast } from 'react-hot-toast'

const TaskDetails = () => {
  const { taskId } = useParams()
  const navigate = useNavigate()
  const { user } = useAuth()
  const { t } = useLanguage()
  
  const [loading, setLoading] = useState(true)
  const [task, setTask] = useState(null)
  const [submissions, setSubmissions] = useState([])
  const [activeTab, setActiveTab] = useState('overview')
  const [gradeModal, setGradeModal] = useState({ show: false, submission: null })
  const [newGrade, setNewGrade] = useState({ score: '', feedback: '' })

  useEffect(() => {
    loadTaskData()
  }, [taskId])

  const loadTaskData = async () => {
    try {
      setLoading(true)
      
      // تحميل بيانات المهمة
      let tasks = JSON.parse(localStorage.getItem('app_tasks') || '[]')
      if (tasks.length === 0) {
        tasks = JSON.parse(localStorage.getItem('tasks_data') || '[]')
      }
      
      const foundTask = tasks.find(t =>
        t.id === parseInt(taskId) ||
        t.id === taskId ||
        t.id.toString() === taskId.toString() ||
        (typeof t.id === 'string' && t.id.includes(taskId)) ||
        (typeof taskId === 'string' && taskId.includes(t.id))
      )
      
      if (!foundTask) {
        toast.error('المهمة غير موجودة')
        navigate('/admin/tasks')
        return
      }
      
      setTask(foundTask)

      // تحميل التسليمات
      const allSubmissions = JSON.parse(localStorage.getItem('task_submissions') || '[]')
      const taskSubmissions = allSubmissions.filter(sub => 
        sub.taskId === parseInt(taskId) || 
        sub.taskId === taskId || 
        sub.taskId.toString() === taskId.toString()
      )
      setSubmissions(taskSubmissions)

    } catch (error) {
      console.error('خطأ في تحميل بيانات المهمة:', error)
      toast.error('فشل في تحميل بيانات المهمة')
    } finally {
      setLoading(false)
    }
  }

  const handleGradeSubmission = async () => {
    if (!newGrade.score || isNaN(newGrade.score) || newGrade.score < 0 || newGrade.score > 100) {
      toast.error('يرجى إدخال درجة صحيحة بين 0 و 100')
      return
    }

    try {
      const score = parseInt(newGrade.score)
      const feedback = newGrade.feedback || ''

      // تحديث التسليم
      const allSubmissions = JSON.parse(localStorage.getItem('task_submissions') || '[]')
      const updatedSubmissions = allSubmissions.map(sub => {
        if (sub.id === gradeModal.submission.id) {
          return {
            ...sub,
            graded: true,
            grade: score,
            feedback: feedback,
            gradedAt: new Date().toISOString(),
            gradedBy: user?.name || 'Admin'
          }
        }
        return sub
      })

      localStorage.setItem('task_submissions', JSON.stringify(updatedSubmissions))

      // تحديث الحالة المحلية
      setSubmissions(prev => prev.map(sub => 
        sub.id === gradeModal.submission.id 
          ? { 
              ...sub, 
              graded: true, 
              grade: score, 
              feedback: feedback,
              gradedAt: new Date().toISOString(),
              gradedBy: user?.name || 'Admin'
            }
          : sub
      ))
      
      setGradeModal({ show: false, submission: null })
      setNewGrade({ score: '', feedback: '' })
      toast.success('تم حفظ التقييم بنجاح!')

    } catch (error) {
      console.error('خطأ في حفظ التقييم:', error)
      toast.error('فشل في حفظ التقييم')
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
        <span className="ml-2 text-gray-600 dark:text-gray-400">{t('common.loading')}...</span>
      </div>
    )
  }

  if (!task) {
    return (
      <div className="text-center py-12">
        <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">المهمة غير موجودة</h3>
        <Button
          variant="primary"
          onClick={() => navigate('/admin/tasks')}
          className="mt-4"
        >
          العودة للمهام
        </Button>
      </div>
    )
  }

  // حساب الإحصائيات
  const assignedCount = task.assignedUsers?.length || 0
  const submittedCount = submissions.length
  const gradedSubmissions = submissions.filter(sub => sub.graded === true)
  const gradedCount = gradedSubmissions.length
  const pendingGrading = submittedCount - gradedCount

  const completionRate = assignedCount > 0 ? Math.round((submittedCount / assignedCount) * 100) : 0

  // إصلاح حساب متوسط الدرجات - تحويل إلى نسبة مئوية
  const averageGrade = gradedSubmissions.length > 0 ?
    (() => {
      // الحصول على إعدادات التقييم
      const gradingSettings = JSON.parse(localStorage.getItem('global_grading_settings') || '{}')
      const maxGrade = gradingSettings.maxGrade || 100

      // حساب المتوسط
      const totalGrades = gradedSubmissions.reduce((sum, sub) => sum + (sub.grade || 0), 0)
      const avgRawGrade = totalGrades / gradedSubmissions.length

      // تحويل إلى نسبة مئوية
      const avgPercentage = (avgRawGrade / maxGrade) * 100

      return Math.round(avgPercentage)
    })() : 0

  const tabs = [
    { id: 'overview', name: 'نظرة عامة', icon: Eye },
    { id: 'submissions', name: 'التسليمات', icon: Upload },
    { id: 'grades', name: 'الدرجات', icon: Award }
  ]

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4 rtl:space-x-reverse">
          <Button
            variant="outline"
            onClick={() => navigate('/admin/tasks')}
            icon={ArrowLeft}
            className="rtl:rotate-180"
          >
            العودة
          </Button>
          <div>
            <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100">
              {task.title}
            </h1>
            <p className="text-sm text-gray-500 dark:text-gray-400">
              {task.course} • {task.trainer}
            </p>
          </div>
        </div>

      </div>

      {/* Task Info Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-6">
        <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
          <div className="flex items-center">
            <div className="p-3 rounded-lg bg-blue-500">
              <Users className="h-6 w-6 text-white" />
            </div>
            <div className="ml-4 rtl:ml-0 rtl:mr-4">
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">المكلفين</p>
              <p className="text-2xl font-bold text-gray-900 dark:text-gray-100">{assignedCount}</p>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
          <div className="flex items-center">
            <div className="p-3 rounded-lg bg-green-500">
              <CheckCircle className="h-6 w-6 text-white" />
            </div>
            <div className="ml-4 rtl:ml-0 rtl:mr-4">
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">المستلمة</p>
              <p className="text-2xl font-bold text-gray-900 dark:text-gray-100">{submittedCount}</p>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
          <div className="flex items-center">
            <div className="p-3 rounded-lg bg-purple-500">
              <Award className="h-6 w-6 text-white" />
            </div>
            <div className="ml-4 rtl:ml-0 rtl:mr-4">
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">المقيمة</p>
              <p className="text-2xl font-bold text-gray-900 dark:text-gray-100">{gradedCount}</p>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
          <div className="flex items-center">
            <div className="p-3 rounded-lg bg-orange-500">
              <AlertCircle className="h-6 w-6 text-white" />
            </div>
            <div className="ml-4 rtl:ml-0 rtl:mr-4">
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">في انتظار التقييم</p>
              <p className="text-2xl font-bold text-gray-900 dark:text-gray-100">{pendingGrading}</p>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
          <div className="flex items-center">
            <div className="p-3 rounded-lg bg-indigo-500">
              <Star className="h-6 w-6 text-white" />
            </div>
            <div className="ml-4 rtl:ml-0 rtl:mr-4">
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">متوسط الدرجات</p>
              <p className="text-2xl font-bold text-gray-900 dark:text-gray-100">{averageGrade}%</p>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
          <div className="flex items-center">
            <div className="p-3 rounded-lg bg-teal-500">
              <TrendingUp className="h-6 w-6 text-white" />
            </div>
            <div className="ml-4 rtl:ml-0 rtl:mr-4">
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">معدل الإنجاز</p>
              <p className="text-2xl font-bold text-gray-900 dark:text-gray-100">{completionRate}%</p>
            </div>
          </div>
        </div>
      </div>

      {/* Tabs */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm">
        <div className="border-b border-gray-200 dark:border-gray-700">
          <nav className="-mb-px flex space-x-8 rtl:space-x-reverse px-6">
            {tabs.map((tab) => {
              const Icon = tab.icon
              return (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`py-4 px-1 border-b-2 font-medium text-sm flex items-center space-x-2 rtl:space-x-reverse ${
                    activeTab === tab.id
                      ? 'border-primary-500 text-primary-600 dark:text-primary-400'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300'
                  }`}
                >
                  <Icon className="w-4 h-4" />
                  <span>{tab.name}</span>
                </button>
              )
            })}
          </nav>
        </div>

        <div className="p-6">
          {/* Overview Tab */}
          {activeTab === 'overview' && (
            <div className="space-y-6">
              <div>
                <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">{t('tasks.taskDetails') || 'تفاصيل المهمة'}</h3>
                <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                    <div>
                      <p className="text-sm font-medium text-gray-600 dark:text-gray-400">{t('tasks.creationDate') || 'تاريخ الإنشاء'}</p>
                      <p className="text-gray-900 dark:text-gray-100">
                        {task.createdAt ? new Date(task.createdAt).toLocaleDateString('ar-SA') : t('common.notSpecified') || 'غير محدد'}
                      </p>
                    </div>
                    <div>
                      <p className="text-sm font-medium text-gray-600 dark:text-gray-400">{t('tasks.dueDate') || 'تاريخ الاستحقاق'}</p>
                      <p className="text-gray-900 dark:text-gray-100">
                        {task.dueDate ? new Date(task.dueDate).toLocaleDateString('ar-SA') : t('common.notSpecified') || 'غير محدد'}
                      </p>
                    </div>
                  </div>

                  {task.description && (
                    <div className="mb-4">
                      <p className="text-sm font-medium text-gray-600 dark:text-gray-400 mb-2">الوصف</p>
                      <p className="text-gray-900 dark:text-gray-100">{task.description}</p>
                    </div>
                  )}
                </div>
              </div>
            </div>
          )}

          {/* Submissions Tab */}
          {activeTab === 'submissions' && (
            <div className="space-y-6">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">التسليمات</h3>
              {submissions.length > 0 ? (
                <div className="space-y-4">
                  {submissions.map(submission => (
                    <div key={submission.id} className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                      <div className="flex justify-between items-start">
                        <div className="flex-1">
                          <h4 className="font-medium text-gray-900 dark:text-gray-100 mb-2">
                            {submission.studentName || submission.traineeName}
                          </h4>
                          <p className="text-sm text-gray-600 dark:text-gray-400 mb-3">
                            {submission.content || 'لا يوجد محتوى'}
                          </p>
                          <div className="flex items-center space-x-4 rtl:space-x-reverse text-sm text-gray-500 dark:text-gray-400">
                            <span className="flex items-center">
                              <Calendar className="w-4 h-4 mr-1 rtl:mr-0 rtl:ml-1" />
                              {new Date(submission.submittedAt).toLocaleDateString('ar-SA')}
                            </span>
                          </div>
                        </div>
                        <div className="flex items-center space-x-2 rtl:space-x-reverse">
                          {submission.graded ? (
                            <div className="text-center">
                              <div className="text-lg font-bold text-green-600 dark:text-green-400">
                                {submission.grade}%
                              </div>
                              <div className="text-xs text-gray-500 dark:text-gray-400">مقيم</div>
                            </div>
                          ) : (
                            <Button
                              variant="primary"
                              size="sm"
                              onClick={() => setGradeModal({ show: true, submission })}
                              icon={Star}
                            >
                              تقييم
                            </Button>
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-12">
                  <FileText className="mx-auto h-12 w-12 text-gray-400" />
                  <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-gray-100">لا توجد تسليمات</h3>
                  <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                    لم يقم أي متدرب بتسليم هذه المهمة بعد
                  </p>
                </div>
              )}
            </div>
          )}

          {/* Grades Tab */}
          {activeTab === 'grades' && (
            <div className="space-y-6">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">الدرجات والتقييمات</h3>
              {gradedSubmissions.length > 0 ? (
                <div className="space-y-4">
                  {gradedSubmissions.map(submission => (
                    <div key={submission.id} className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                      <div className="flex justify-between items-start">
                        <div className="flex-1">
                          <h4 className="font-medium text-gray-900 dark:text-gray-100 mb-2">
                            {submission.studentName || submission.traineeName}
                          </h4>
                          <div className="flex items-center space-x-4 rtl:space-x-reverse text-sm text-gray-500 dark:text-gray-400">
                            <span className="flex items-center">
                              <Calendar className="w-4 h-4 mr-1 rtl:mr-0 rtl:ml-1" />
                              {new Date(submission.gradedAt || submission.submittedAt).toLocaleDateString('ar-SA')}
                            </span>
                          </div>
                          {submission.feedback && (
                            <div className="mt-3">
                              <p className="text-sm text-gray-600 dark:text-gray-400">
                                <strong>التعليق:</strong> {submission.feedback}
                              </p>
                            </div>
                          )}
                        </div>
                        <div className="text-center">
                          <div className={`text-2xl font-bold ${
                            submission.grade >= 90 ? 'text-green-600 dark:text-green-400' :
                            submission.grade >= 80 ? 'text-blue-600 dark:text-blue-400' :
                            submission.grade >= 70 ? 'text-yellow-600 dark:text-yellow-400' :
                            'text-red-600 dark:text-red-400'
                          }`}>
                            {submission.grade}%
                          </div>
                          <div className="text-xs text-gray-500 dark:text-gray-400">
                            {submission.grade >= 90 ? 'ممتاز' :
                             submission.grade >= 80 ? 'جيد جداً' :
                             submission.grade >= 70 ? 'جيد' :
                             submission.grade >= 60 ? 'مقبول' : 'ضعيف'}
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-12">
                  <Star className="mx-auto h-12 w-12 text-gray-400" />
                  <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-gray-100">لا توجد تقييمات</h3>
                  <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                    لم يتم تقييم أي تسليم لهذه المهمة بعد
                  </p>
                </div>
              )}
            </div>
          )}
        </div>
      </div>

      {/* Grade Modal */}
      {gradeModal.show && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white dark:bg-gray-800 rounded-lg p-6 w-full max-w-md mx-4">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">
              تقييم تسليم: {gradeModal.submission?.studentName || gradeModal.submission?.traineeName}
            </h3>

            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  الدرجة (0-100) *
                </label>
                <input
                  type="number"
                  min="0"
                  max="100"
                  value={newGrade.score}
                  onChange={(e) => setNewGrade(prev => ({ ...prev, score: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent dark:bg-gray-700 dark:text-gray-100"
                  placeholder="85"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  التعليق (اختياري)
                </label>
                <textarea
                  value={newGrade.feedback}
                  onChange={(e) => setNewGrade(prev => ({ ...prev, feedback: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent dark:bg-gray-700 dark:text-gray-100"
                  placeholder="أدخل تعليقك على التسليم"
                  rows="3"
                />
              </div>
            </div>

            <div className="flex justify-end space-x-3 rtl:space-x-reverse mt-6">
              <Button
                variant="outline"
                onClick={() => {
                  setGradeModal({ show: false, submission: null })
                  setNewGrade({ score: '', feedback: '' })
                }}
              >
                إلغاء
              </Button>
              <Button
                variant="primary"
                onClick={handleGradeSubmission}
                icon={Award}
                disabled={!newGrade.score || isNaN(newGrade.score) || newGrade.score < 0 || newGrade.score > 100}
              >
                حفظ التقييم
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default TaskDetails
