import React, { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { toast } from 'react-hot-toast'
import {
  Plus,
  Calendar,
  Users,
  Clock,
  BookOpen,
  Edit,
  Eye,
  Star,
  Award,
  FileText,
  Video,
  Download,
  Upload,
  Settings,
  Target,
  TrendingUp
} from 'lucide-react'
import Button from '../../components/ui/Button'
import CreateWorkshopModal from '../../components/modals/CreateWorkshopModal'
import { useAuth } from '../../contexts/AuthContext'
import { useLanguage } from '../../contexts/LanguageContext'
import dataStorage from '../../utils/dataStorage'

const TrainerWorkshops = () => {
  const { user } = useAuth()
  const { t } = useLanguage()
  const [workshops, setWorkshops] = useState([])
  const [showCreateModal, setShowCreateModal] = useState(false)
  const [selectedWorkshop, setSelectedWorkshop] = useState(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    loadTrainerWorkshops()
  }, [user])

  const loadTrainerWorkshops = () => {
    try {
      setLoading(true)

      // تحميل ورش العمل من جميع المصادر
      const workshopsData = JSON.parse(localStorage.getItem('workshops_data') || '[]')
      const appWorkshops = JSON.parse(localStorage.getItem('app_workshops') || '[]')
      const dataStorageWorkshops = dataStorage.loadWorkshops()

      // دمج ورش العمل من المصادر المختلفة
      const allWorkshops = [...workshopsData, ...appWorkshops, ...dataStorageWorkshops]

      // إزالة التكرار بناءً على ID
      const uniqueWorkshops = allWorkshops.filter((workshop, index, self) =>
        index === self.findIndex(w => w.id === workshop.id)
      )

      console.log('📋 Loading workshops for trainer:', {
        workshopsData: workshopsData.length,
        appWorkshops: appWorkshops.length,
        dataStorageWorkshops: dataStorageWorkshops.length,
        uniqueWorkshops: uniqueWorkshops.length,
        trainer: user.name
      })

      // تصفية ورش العمل الخاصة بالمدرب الحالي
      const trainerWorkshops = uniqueWorkshops.filter(workshop =>
        workshop.trainer === user.name || workshop.trainerId === user.id
      )

      setWorkshops(trainerWorkshops)
    } catch (error) {
      console.error('Error loading trainer workshops:', error)
      toast.error('Failed to load workshops')
    } finally {
      setLoading(false)
    }
  }

  const handleWorkshopCreated = (newWorkshop) => {
    loadTrainerWorkshops()
    setShowCreateModal(false)
    toast.success('Workshop created successfully')
  }

  const getWorkshopStats = () => {
    return {
      total: workshops.length,
      published: workshops.filter(w => w.status === 'published').length,
      draft: workshops.filter(w => w.status === 'draft').length,
      completed: workshops.filter(w => w.status === 'completed').length,
      totalParticipants: workshops.reduce((sum, w) => sum + (w.selectedTrainees?.length || 0), 0)
    }
  }

  const stats = getWorkshopStats()

  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
        <span className="ml-2 text-gray-600 dark:text-gray-400">{t('common.loading')}...</span>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100">
            🎯 {t('workshops.myWorkshops')}
          </h1>
          <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
            {t('workshops.manageWorkshopsContent')}
          </p>
        </div>
        <div className="mt-4 sm:mt-0">
          <Button
            variant="primary"
            onClick={() => setShowCreateModal(true)}
            icon={Plus}
          >
            {t('workshops.createNewWorkshop')}
          </Button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
        <div className="bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
          <div className="flex items-center">
            <BookOpen className="w-8 h-8 text-blue-600" />
            <div className="ml-3">
              <p className="text-sm text-gray-600 dark:text-gray-400">{t('workshops.totalWorkshops')}</p>
              <p className="text-lg font-semibold text-gray-900 dark:text-gray-100">{stats.total}</p>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
          <div className="flex items-center">
            <Eye className="w-8 h-8 text-green-600" />
            <div className="ml-3">
              <p className="text-sm text-gray-600 dark:text-gray-400">{t('workshops.published')}</p>
              <p className="text-lg font-semibold text-gray-900 dark:text-gray-100">{stats.published}</p>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
          <div className="flex items-center">
            <Edit className="w-8 h-8 text-yellow-600" />
            <div className="ml-3">
              <p className="text-sm text-gray-600 dark:text-gray-400">{t('workshops.draft')}</p>
              <p className="text-lg font-semibold text-gray-900 dark:text-gray-100">{stats.draft}</p>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
          <div className="flex items-center">
            <Award className="w-8 h-8 text-purple-600" />
            <div className="ml-3">
              <p className="text-sm text-gray-600 dark:text-gray-400">{t('workshops.completed')}</p>
              <p className="text-lg font-semibold text-gray-900 dark:text-gray-100">{stats.completed}</p>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
          <div className="flex items-center">
            <Users className="w-8 h-8 text-indigo-600" />
            <div className="ml-3">
              <p className="text-sm text-gray-600 dark:text-gray-400">{t('workshops.participants')}</p>
              <p className="text-lg font-semibold text-gray-900 dark:text-gray-100">{stats.totalParticipants}</p>
            </div>
          </div>
        </div>
      </div>

      {/* Workshops Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {workshops.map((workshop) => (
          <WorkshopCard
            key={workshop.id}
            workshop={workshop}
            onEdit={() => {
              setSelectedWorkshop(workshop)
              setShowCreateModal(true)
            }}
            onView={() => {
              // Navigate to workshop details
              navigate(`/trainer/workshop/${workshop.id}`)
            }}
          />
        ))}
      </div>

      {/* Empty State */}
      {workshops.length === 0 && (
        <div className="text-center py-12">
          <BookOpen className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-gray-100">
            لا توجد ورش عمل
          </h3>
          <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
            ابدأ بإنشاء ورشة العمل الأولى
          </p>
          <div className="mt-6">
            <Button
              variant="primary"
              onClick={() => setShowCreateModal(true)}
              icon={Plus}
            >
              إنشاء ورشة عمل جديدة
            </Button>
          </div>
        </div>
      )}

      {/* Create Workshop Modal */}
      <CreateWorkshopModal
        isOpen={showCreateModal}
        onClose={() => {
          setShowCreateModal(false)
          setSelectedWorkshop(null)
        }}
        onWorkshopCreated={handleWorkshopCreated}
        editWorkshop={selectedWorkshop}
      />
    </div>
  )
}

// Workshop Card Component
const WorkshopCard = ({ workshop, onEdit, onView }) => {
  const getStatusColor = (status) => {
    switch (status) {
      case 'published': return 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-300'
      case 'draft': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-300'
      case 'completed': return 'bg-purple-100 text-purple-800 dark:bg-purple-900/20 dark:text-purple-300'
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-300'
    }
  }

  const getStatusText = (status) => {
    switch (status) {
      case 'published': return 'منشورة'
      case 'draft': return 'مسودة'
      case 'completed': return 'مكتملة'
      default: return status
    }
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      whileHover={{ y: -5 }}
      className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden hover:shadow-md transition-all duration-200"
    >
      <div className="p-6">
        <div className="flex items-start justify-between mb-4">
          <div className="flex-1">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2">
              {workshop.title}
            </h3>
            <p className="text-sm text-gray-600 dark:text-gray-400 line-clamp-2">
              {workshop.description}
            </p>
          </div>
          <span className={`px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(workshop.status)}`}>
            {getStatusText(workshop.status)}
          </span>
        </div>

        <div className="space-y-2 mb-4">
          <div className="flex items-center text-sm text-gray-500 dark:text-gray-400">
            <Calendar className="w-4 h-4 mr-2" />
            <span>{workshop.startDate}</span>
          </div>
          <div className="flex items-center text-sm text-gray-500 dark:text-gray-400">
            <Clock className="w-4 h-4 mr-2" />
            <span>{workshop.startTime} - {workshop.duration} دقيقة</span>
          </div>
          <div className="flex items-center text-sm text-gray-500 dark:text-gray-400">
            <Users className="w-4 h-4 mr-2" />
            <span>{workshop.selectedTrainees?.length || 0} مشارك</span>
          </div>
        </div>

        <div className="flex space-x-2 rtl:space-x-reverse">
          <Button
            variant="primary"
            size="sm"
            onClick={onView}
            icon={Eye}
            className="flex-1"
          >
            عرض التفاصيل
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={onEdit}
            icon={Edit}
          >
            تعديل
          </Button>
        </div>
      </div>
    </motion.div>
  )
}

export default TrainerWorkshops
