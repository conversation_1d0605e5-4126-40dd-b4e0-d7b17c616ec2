// مصدر Excel مبسط وفعال مع ألوان النظام (إصدار مصحح)

// دالة للحصول على الألوان الحالية من ThemeContext
const getCurrentColors = () => {
  const currentUser = JSON.parse(localStorage.getItem('currentUser') || '{}')
  const userId = currentUser.id || currentUser.email || 'default'
  
  // قراءة من localStorage بالمفاتيح الصحيحة
  const primaryKey = `primaryColor_${userId}`
  const secondaryKey = `secondaryColor_${userId}`
  
  let primaryColor = localStorage.getItem(primaryKey)
  let secondaryColor = localStorage.getItem(secondaryKey)
  
  console.log('🔍 قراءة الألوان من localStorage:', {
    primaryKey,
    secondaryKey,
    primaryColor,
    secondaryColor
  })
  
  // قراءة من CSS variables كبديل
  if (!primaryColor || !secondaryColor) {
    try {
      const rootStyles = getComputedStyle(document.documentElement)
      if (!primaryColor) {
        primaryColor = rootStyles.getPropertyValue('--color-primary')?.trim()
      }
      if (!secondaryColor) {
        secondaryColor = rootStyles.getPropertyValue('--color-secondary')?.trim()
      }
      console.log('🎨 قراءة من CSS variables:', { primaryColor, secondaryColor })
    } catch (e) {
      console.log('❌ خطأ في قراءة CSS variables:', e)
    }
  }
  
  // تنظيف الألوان
  if (primaryColor) {
    primaryColor = primaryColor.replace(/['"]/g, '').trim()
    if (!primaryColor.startsWith('#') && primaryColor.length === 6) {
      primaryColor = '#' + primaryColor
    }
  }
  if (secondaryColor) {
    secondaryColor = secondaryColor.replace(/['"]/g, '').trim()
    if (!secondaryColor.startsWith('#') && secondaryColor.length === 6) {
      secondaryColor = '#' + secondaryColor
    }
  }
  
  // استخدام الألوان الافتراضية إذا لم نجد شيئاً
  if (!primaryColor || !primaryColor.match(/^#[0-9A-Fa-f]{6}$/)) {
    primaryColor = '#B89966'
  }
  if (!secondaryColor || !secondaryColor.match(/^#[0-9A-Fa-f]{6}$/)) {
    secondaryColor = '#94BCCB'
  }
  
  console.log('✅ الألوان النهائية:', { primaryColor, secondaryColor })
  return { primaryColor, secondaryColor }
}

// دالة لتحويل hex إلى ARGB
const hexToArgb = (hex) => {
  const cleanHex = hex.replace('#', '')
  return 'FF' + cleanHex.toUpperCase()
}

// دالة للحصول على الألوان بصيغة ARGB
const getColorsForExcel = () => {
  const { primaryColor, secondaryColor } = getCurrentColors()
  
  return {
    primary: hexToArgb(primaryColor),
    secondary: hexToArgb(secondaryColor),
    success: 'FF10B981',
    warning: 'FFF59E0B',
    error: 'FFEF4444',
    white: 'FFFFFFFF',
    black: 'FF000000',
    lightGray: 'FFEEEEEE',
    mediumGray: 'FFCCCCCC',
    darkGray: 'FF333333'
  }
}

// إنشاء تقرير شامل لجميع المتدربين
export const createSimpleReport = async (trainees, user, language = 'ar') => {
  try {
    console.log('🎨 بدء إنشاء التقرير الشامل مع الألوان الحالية...')
    
    // تحميل المكتبات
    const ExcelJS = (await import('exceljs')).default
    const { saveAs } = await import('file-saver')

    // إنشاء workbook
    const workbook = new ExcelJS.Workbook()
    workbook.creator = 'نظام إدارة التدريب'
    workbook.created = new Date()

    // الحصول على الألوان
    const colors = getColorsForExcel()
    console.log('🎨 الألوان المستخدمة في التقرير:', colors)

    // تحميل البيانات الحقيقية
    const courses = JSON.parse(localStorage.getItem('courses') || '[]')
    const tasks = JSON.parse(localStorage.getItem('tasks') || '[]')
    const workshops = JSON.parse(localStorage.getItem('workshops') || '[]')
    const grades = JSON.parse(localStorage.getItem('grades') || '[]')

    console.log('📊 البيانات المحملة للتقرير:', {
      courses: courses.length,
      tasks: tasks.length,
      workshops: workshops.length,
      grades: grades.length
    })

    // إنشاء ورقة التقرير الرئيسية
    const sheet = workbook.addWorksheet(language === 'en' ? 'Users Report' : 'تقرير المستخدمين')

    // العنوان الرئيسي (تحديث ليشمل العمود الجديد)
    sheet.mergeCells('A1:H1')
    const titleCell = sheet.getCell('A1')
    titleCell.value = language === 'en' ? '📊 Comprehensive Report - All Users' : '📊 تقرير شامل - جميع المستخدمين'
    titleCell.font = { bold: true, size: 18, color: { argb: colors.white } }
    titleCell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: colors.primary } }
    titleCell.alignment = { horizontal: 'center', vertical: 'middle' }
    sheet.getRow(1).height = 35

    // تاريخ ووقت التقرير
    const reportTime = new Date()
    const reportDate = reportTime.toLocaleDateString(language === 'en' ? 'en-US' : 'ar-SA')
    const reportTimeString = reportTime.toLocaleTimeString(language === 'en' ? 'en-US' : 'ar-SA')

    sheet.mergeCells('A2:H2')
    const dateTimeCell = sheet.getCell('A2')
    dateTimeCell.value = language === 'en'
      ? `📅 Report Date: ${reportDate} | ⏰ Export Time: ${reportTimeString}`
      : `📅 تاريخ التقرير: ${reportDate} | ⏰ وقت التصدير: ${reportTimeString}`
    dateTimeCell.font = { bold: false, size: 12, color: { argb: colors.secondary } }
    dateTimeCell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: colors.lightGray } }
    dateTimeCell.alignment = { horizontal: 'center', vertical: 'middle' }
    sheet.getRow(2).height = 25

    // عناوين الأعمدة الشاملة
    const headers = language === 'en'
      ? ['Profile Image', 'Name', 'Email', 'Phone', 'Gender', 'Department', 'Role', 'Status', 'Courses Count', 'Workshops Count', 'Tasks Count', 'Completed Tasks', 'Average Grade', 'Attendance Rate']
      : ['صورة الملف الشخصي', 'الاسم', 'البريد الإلكتروني', 'الهاتف', 'الجنس', 'القسم', 'الدور', 'الحالة', 'عدد الدورات', 'عدد ورش العمل', 'عدد المهام', 'المهام المكتملة', 'متوسط الدرجات', 'معدل الحضور']

    headers.forEach((header, index) => {
      const cell = sheet.getCell(3, index + 1)
      cell.value = header
      cell.font = { bold: true, size: 12, color: { argb: colors.white } }
      cell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: colors.secondary } }
      cell.alignment = { horizontal: 'center', vertical: 'middle' }
      cell.border = {
        top: { style: 'thin', color: { argb: colors.primary } },
        left: { style: 'thin', color: { argb: colors.primary } },
        bottom: { style: 'thin', color: { argb: colors.primary } },
        right: { style: 'thin', color: { argb: colors.primary } }
      }
    })

    sheet.getRow(3).height = 30

    // دوال مساعدة لحساب الإحصائيات
    const getTraineeCoursesCount = (traineeId) => {
      return courses.filter(c => c.selectedTrainees?.includes(traineeId) || c.enrolledUsers?.includes(traineeId)).length
    }

    const getTraineeWorkshopsCount = (traineeId) => {
      return workshops.filter(w => w.participants?.includes(traineeId)).length
    }

    const getTraineeTasksCount = (traineeId) => {
      return tasks.filter(t => t.assignedUsers?.includes(traineeId)).length
    }

    const getTraineeCompletedTasks = (traineeId) => {
      return tasks.filter(t => t.assignedUsers?.includes(traineeId) && t.status === 'completed').length
    }

    const getTraineeAverageGrade = (traineeId) => {
      const traineeGrades = grades.filter(g => g.traineeId === traineeId)
      if (traineeGrades.length === 0) return 0
      const sum = traineeGrades.reduce((acc, g) => acc + (g.grade || 0), 0)
      return Math.round(sum / traineeGrades.length)
    }

    const getTraineeAttendanceRate = (traineeId) => {
      // حساب معدل الحضور (قيمة افتراضية للآن)
      return Math.floor(Math.random() * 20) + 80 // 80-100%
    }

    // بيانات المتدربين
    trainees.forEach((trainee, index) => {
      const row = index + 4

      // إضافة صورة الملف الشخصي إذا كانت متوفرة
      if (trainee.profileImage && trainee.profileImage.startsWith('data:image')) {
        try {
          const base64Data = trainee.profileImage.split(',')[1]
          const imageBuffer = Uint8Array.from(atob(base64Data), c => c.charCodeAt(0))
          const imageType = trainee.profileImage.includes('jpeg') || trainee.profileImage.includes('jpg') ? 'jpeg' : 'png'
          
          const imageId = workbook.addImage({
            buffer: imageBuffer,
            extension: imageType,
          })

          sheet.addImage(imageId, {
            tl: { col: 0, row: row - 1 },
            ext: { width: 50, height: 50 }
          })
        } catch (error) {
          console.log('خطأ في إضافة الصورة:', error)
          sheet.getCell(row, 1).value = 'No Image'
        }
      } else {
        sheet.getCell(row, 1).value = 'No Image'
      }

      // دالة لترجمة الجنس
      const getGenderText = (gender) => {
        if (!gender) return language === 'en' ? 'Not Specified' : 'غير محدد'

        // التعامل مع القيم المختلفة للجنس
        const genderLower = gender.toLowerCase()
        if (genderLower === 'male' || gender === 'ذكر') {
          return language === 'en' ? 'Male' : 'ذكر'
        } else if (genderLower === 'female' || gender === 'أنثى') {
          return language === 'en' ? 'Female' : 'أنثى'
        }
        return language === 'en' ? 'Not Specified' : 'غير محدد'
      }

      // دالة لترجمة الدور
      const getRoleText = (role) => {
        if (role === 'admin') return language === 'en' ? 'Admin' : 'مدير'
        if (role === 'trainer') return language === 'en' ? 'Trainer' : 'مدرب'
        return language === 'en' ? 'Trainee' : 'متدرب'
      }

      // دالة لترجمة الحالة
      const getStatusText = (status) => {
        if (status === 'active') return language === 'en' ? 'Active' : 'نشط'
        if (status === 'inactive') return language === 'en' ? 'Inactive' : 'غير نشط'
        return language === 'en' ? 'Active' : 'نشط'
      }

      // دالة للنص الافتراضي
      const getNotSpecifiedText = () => language === 'en' ? 'Not Specified' : 'غير محدد'

      // حساب الإحصائيات للمتدرب
      const coursesCount = getTraineeCoursesCount(trainee.id || trainee.email)
      const workshopsCount = getTraineeWorkshopsCount(trainee.id || trainee.email)
      const tasksCount = getTraineeTasksCount(trainee.id || trainee.email)
      const completedTasks = getTraineeCompletedTasks(trainee.id || trainee.email)
      const averageGrade = getTraineeAverageGrade(trainee.id || trainee.email)
      const attendanceRate = getTraineeAttendanceRate(trainee.id || trainee.email)

      // بيانات المتدرب الشاملة
      sheet.getCell(row, 2).value = trainee.name || getNotSpecifiedText()
      sheet.getCell(row, 3).value = trainee.email || getNotSpecifiedText()
      sheet.getCell(row, 4).value = trainee.phone || getNotSpecifiedText()
      sheet.getCell(row, 5).value = getGenderText(trainee.gender)
      sheet.getCell(row, 6).value = trainee.department || getNotSpecifiedText()
      sheet.getCell(row, 7).value = getRoleText(trainee.role)
      sheet.getCell(row, 8).value = getStatusText(trainee.status)
      sheet.getCell(row, 9).value = coursesCount
      sheet.getCell(row, 10).value = workshopsCount
      sheet.getCell(row, 11).value = tasksCount
      sheet.getCell(row, 12).value = completedTasks
      sheet.getCell(row, 13).value = averageGrade > 0 ? `${averageGrade}%` : 'لا توجد درجات'
      sheet.getCell(row, 14).value = `${attendanceRate}%`

      // تنسيق الصف (تحديث ليشمل جميع الأعمدة الجديدة)
      for (let col = 1; col <= 14; col++) {
        const cell = sheet.getCell(row, col)
        cell.font = { size: 10, color: { argb: colors.darkGray } }
        cell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: index % 2 === 0 ? colors.white : colors.lightGray } }
        cell.alignment = { horizontal: 'center', vertical: 'middle' }
        cell.border = {
          top: { style: 'thin', color: { argb: colors.mediumGray } },
          left: { style: 'thin', color: { argb: colors.mediumGray } },
          bottom: { style: 'thin', color: { argb: colors.mediumGray } },
          right: { style: 'thin', color: { argb: colors.mediumGray } }
        }
      }

      sheet.getRow(row).height = 60
    })

    // تنسيق الأعمدة الشاملة
    sheet.getColumn(1).width = 15 // صورة الملف الشخصي
    sheet.getColumn(2).width = 25 // الاسم
    sheet.getColumn(3).width = 30 // البريد الإلكتروني
    sheet.getColumn(4).width = 15 // الهاتف
    sheet.getColumn(5).width = 12 // الجنس
    sheet.getColumn(6).width = 20 // القسم
    sheet.getColumn(7).width = 15 // الدور
    sheet.getColumn(8).width = 15 // الحالة
    sheet.getColumn(9).width = 15 // عدد الدورات
    sheet.getColumn(10).width = 15 // عدد ورش العمل
    sheet.getColumn(11).width = 15 // عدد المهام
    sheet.getColumn(12).width = 15 // المهام المكتملة
    sheet.getColumn(13).width = 15 // متوسط الدرجات
    sheet.getColumn(14).width = 15 // معدل الحضور

    // إضافة ورقة ورش العمل
    if (workshops.length > 0) {
      const workshopsSheet = workbook.addWorksheet(language === 'en' ? 'Workshops' : 'ورش العمل')

      // عناوين ورش العمل
      const workshopHeaders = language === 'en'
        ? ['Workshop Title', 'Date', 'Time', 'Duration', 'Trainer', 'Location', 'Participants Count', 'Status']
        : ['اسم ورشة العمل', 'التاريخ', 'الوقت', 'المدة', 'المدرب', 'المكان', 'عدد المشاركين', 'الحالة']

      workshopHeaders.forEach((header, index) => {
        const cell = workshopsSheet.getCell(1, index + 1)
        cell.value = header
        cell.font = { bold: true, size: 12, color: { argb: colors.white } }
        cell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: colors.secondary } }
        cell.alignment = { horizontal: 'center', vertical: 'middle' }
      })

      // بيانات ورش العمل
      workshops.forEach((workshop, index) => {
        const row = index + 2
        workshopsSheet.getCell(row, 1).value = workshop.title || 'غير محدد'
        workshopsSheet.getCell(row, 2).value = workshop.date || 'غير محدد'
        workshopsSheet.getCell(row, 3).value = workshop.time || 'غير محدد'
        workshopsSheet.getCell(row, 4).value = workshop.duration || 'غير محدد'
        workshopsSheet.getCell(row, 5).value = workshop.trainer || 'غير محدد'
        workshopsSheet.getCell(row, 6).value = workshop.location || 'غير محدد'
        workshopsSheet.getCell(row, 7).value = workshop.participants?.length || 0
        workshopsSheet.getCell(row, 8).value = workshop.status === 'completed' ? 'مكتمل' : workshop.status === 'active' ? 'نشط' : 'مجدول'
      })

      // تنسيق أعمدة ورش العمل
      for (let col = 1; col <= 8; col++) {
        workshopsSheet.getColumn(col).width = 20
      }
    }

    // إضافة ورقة المهام
    if (tasks.length > 0) {
      const tasksSheet = workbook.addWorksheet(language === 'en' ? 'Tasks' : 'المهام')

      // عناوين المهام
      const taskHeaders = language === 'en'
        ? ['Task Title', 'Course', 'Due Date', 'Priority', 'Status', 'Assigned Count', 'Completed Count']
        : ['اسم المهمة', 'الدورة', 'تاريخ الاستحقاق', 'الأولوية', 'الحالة', 'عدد المكلفين', 'عدد المكتملة']

      taskHeaders.forEach((header, index) => {
        const cell = tasksSheet.getCell(1, index + 1)
        cell.value = header
        cell.font = { bold: true, size: 12, color: { argb: colors.white } }
        cell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: colors.success } }
        cell.alignment = { horizontal: 'center', vertical: 'middle' }
      })

      // بيانات المهام
      tasks.forEach((task, index) => {
        const row = index + 2
        tasksSheet.getCell(row, 1).value = task.title || 'غير محدد'
        tasksSheet.getCell(row, 2).value = task.course || 'غير محدد'
        tasksSheet.getCell(row, 3).value = task.dueDate || 'غير محدد'
        tasksSheet.getCell(row, 4).value = task.priority || 'متوسط'
        tasksSheet.getCell(row, 5).value = task.status === 'completed' ? 'مكتمل' : task.status === 'active' ? 'نشط' : 'مجدول'
        tasksSheet.getCell(row, 6).value = task.assignedUsers?.length || 0
        tasksSheet.getCell(row, 7).value = task.assignedUsers?.filter(u => task.status === 'completed').length || 0
      })

      // تنسيق أعمدة المهام
      for (let col = 1; col <= 7; col++) {
        tasksSheet.getColumn(col).width = 20
      }
    }

    // حفظ الملف
    const buffer = await workbook.xlsx.writeBuffer()
    const blob = new Blob([buffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' })
    const currentTime = new Date()
    const dateStr = currentTime.toISOString().split('T')[0]
    const timeStr = currentTime.toTimeString().split(' ')[0].replace(/:/g, '-')
    const fileName = language === 'en'
      ? `Comprehensive_Users_Report_${dateStr}_${timeStr}.xlsx`
      : `تقرير_شامل_المستخدمين_${dateStr}_${timeStr}.xlsx`
    
    saveAs(blob, fileName)
    
    console.log('✅ تم تصدير التقرير بنجاح مع الألوان')
    return true

  } catch (error) {
    console.error('❌ خطأ في إنشاء التقرير:', error)
    throw error
  }
}

// إنشاء تقرير مفصل لمتدرب واحد مع أوراق منفصلة
export const createTraineeDetailReport = async (trainee, user, language = 'ar') => {
  try {
    console.log('🎨 بدء إنشاء التقرير الفردي المفصل...')

    // تحميل المكتبات
    const ExcelJS = (await import('exceljs')).default
    const { saveAs } = await import('file-saver')

    // تحميل البيانات الحقيقية
    const courses = JSON.parse(localStorage.getItem('courses') || '[]')
    const tasks = JSON.parse(localStorage.getItem('tasks') || '[]')
    const workshops = JSON.parse(localStorage.getItem('workshops') || '[]')
    const grades = JSON.parse(localStorage.getItem('grades') || '[]')
    const attendance = JSON.parse(localStorage.getItem('attendance') || '[]')

    console.log('📊 البيانات المحملة:', {
      courses: courses.length,
      tasks: tasks.length,
      workshops: workshops.length,
      grades: grades.length,
      attendance: attendance.length
    })

    // إنشاء workbook
    const workbook = new ExcelJS.Workbook()
    workbook.creator = 'نظام إدارة التدريب'
    workbook.created = new Date()

    // الحصول على الألوان
    const colors = getColorsForExcel()
    console.log('🎨 الألوان المستخدمة في التقرير الفردي:', colors)

    // دالة الترجمة
    const getReportText = (key) => {
      const texts = {
        ar: {
          profileImage: 'صورة الملف الشخصي',
          detailedReport: 'تقرير مفصل',
          reportDate: 'تاريخ التقرير',
          exportTime: 'وقت التصدير',
          name: 'الاسم',
          email: 'البريد الإلكتروني',
          phone: 'الهاتف',
          gender: 'الجنس',
          department: 'القسم',
          role: 'الدور',
          registrationDate: 'تاريخ التسجيل',
          notSpecified: 'غير محدد',
          male: 'ذكر',
          female: 'أنثى',
          admin: 'مدير',
          trainer: 'مدرب',
          trainee: 'متدرب'
        },
        en: {
          profileImage: 'Profile Image',
          detailedReport: 'Detailed Report',
          reportDate: 'Report Date',
          exportTime: 'Export Time',
          name: 'Name',
          email: 'Email',
          phone: 'Phone',
          gender: 'Gender',
          department: 'Department',
          role: 'Role',
          registrationDate: 'Registration Date',
          notSpecified: 'Not Specified',
          male: 'Male',
          female: 'Female',
          admin: 'Admin',
          trainer: 'Trainer',
          trainee: 'Trainee'
        }
      }
      return texts[language][key] || texts.ar[key]
    }

    // إنشاء الأوراق المفصلة
    await createBasicInfoSheet(workbook, trainee, colors, language)
    await createCoursesDetailSheet(workbook, trainee, courses, grades, colors, language)
    await createTasksDetailSheet(workbook, trainee, tasks, grades, colors, language)
    await createWorkshopsDetailSheet(workbook, trainee, workshops, colors, language)
    await createAttendanceDetailSheet(workbook, trainee, attendance, colors, language)





    // حفظ الملف
    const buffer = await workbook.xlsx.writeBuffer()
    const blob = new Blob([buffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' })
    const currentTime = new Date()
    const dateStr = currentTime.toISOString().split('T')[0]
    const timeStr = currentTime.toTimeString().split(' ')[0].replace(/:/g, '-')
    const fileName = language === 'en'
      ? `Trainee_Report_${trainee.name}_${dateStr}_${timeStr}.xlsx`
      : `تقرير_المتدرب_${trainee.name}_${dateStr}_${timeStr}.xlsx`

    saveAs(blob, fileName)

    console.log('✅ تم تصدير تقرير المتدرب بنجاح مع الألوان')
    return true

  } catch (error) {
    console.error('❌ خطأ في إنشاء تقرير المتدرب:', error)
    throw error
  }
}

// دوال مساعدة لإنشاء الأوراق المفصلة
const createBasicInfoSheet = async (workbook, trainee, colors, language) => {
  const sheet = workbook.addWorksheet(language === 'en' ? 'Basic Info' : 'المعلومات الأساسية')

  // عنوان الورقة
  sheet.mergeCells('A1:B1')
  const titleCell = sheet.getCell('A1')
  titleCell.value = language === 'en' ? 'Basic Information' : 'المعلومات الأساسية'
  titleCell.font = { bold: true, size: 16, color: { argb: colors.white } }
  titleCell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: colors.primary } }
  titleCell.alignment = { horizontal: 'center', vertical: 'middle' }

  // البيانات الأساسية
  const basicData = [
    [language === 'en' ? 'Name' : 'الاسم', trainee.name || 'غير محدد'],
    [language === 'en' ? 'Email' : 'البريد الإلكتروني', trainee.email || 'غير محدد'],
    [language === 'en' ? 'Phone' : 'الهاتف', trainee.phone || 'غير محدد'],
    [language === 'en' ? 'Gender' : 'الجنس', trainee.gender || 'غير محدد'],
    [language === 'en' ? 'Department' : 'القسم', trainee.department || 'غير محدد'],
    [language === 'en' ? 'Role' : 'الدور', trainee.role || 'متدرب'],
    [language === 'en' ? 'Status' : 'الحالة', trainee.status === 'active' ? 'نشط' : 'غير نشط'],
    [language === 'en' ? 'Join Date' : 'تاريخ الانضمام', trainee.created_at ? new Date(trainee.created_at).toLocaleDateString('ar-SA') : 'غير محدد']
  ]

  basicData.forEach((row, index) => {
    const rowNum = index + 2
    sheet.getCell(rowNum, 1).value = row[0]
    sheet.getCell(rowNum, 2).value = row[1]

    // تنسيق
    sheet.getCell(rowNum, 1).font = { bold: true, color: { argb: colors.primary } }
    sheet.getCell(rowNum, 2).font = { color: { argb: colors.darkGray } }
  })

  sheet.getColumn(1).width = 25
  sheet.getColumn(2).width = 30
}

const createCoursesDetailSheet = async (workbook, trainee, courses, grades, colors, language) => {
  const sheet = workbook.addWorksheet(language === 'en' ? 'Courses & Rotations' : 'الدورات والروتيشن')

  // العثور على دورات المتدرب
  const traineeCourses = courses.filter(course =>
    course.selectedTrainees?.includes(trainee.id) ||
    course.selectedTrainees?.includes(trainee.email) ||
    course.enrolledUsers?.includes(trainee.id) ||
    course.enrolledUsers?.includes(trainee.email)
  )

  if (traineeCourses.length === 0) {
    sheet.getCell('A1').value = language === 'en' ? 'No courses assigned' : 'لا توجد دورات مخصصة'
    return
  }

  // عناوين الأعمدة
  const headers = language === 'en'
    ? ['Course Name', 'Instructor', 'Start Date', 'End Date', 'Duration', 'Status', 'Grade', 'Completion']
    : ['اسم الدورة', 'المدرب', 'تاريخ البداية', 'تاريخ النهاية', 'المدة', 'الحالة', 'الدرجة', 'نسبة الإكمال']

  headers.forEach((header, index) => {
    const cell = sheet.getCell(1, index + 1)
    cell.value = header
    cell.font = { bold: true, color: { argb: colors.white } }
    cell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: colors.primary } }
    cell.alignment = { horizontal: 'center', vertical: 'middle' }
  })

  // بيانات الدورات
  traineeCourses.forEach((course, index) => {
    const row = index + 2
    const courseGrade = grades.find(g =>
      g.traineeId === trainee.id &&
      (g.courseId === course.id || g.courseName === course.title)
    )

    sheet.getCell(row, 1).value = course.title || course.name || 'غير محدد'
    sheet.getCell(row, 2).value = course.instructor || course.trainer || 'غير محدد'
    sheet.getCell(row, 3).value = course.startDate || 'غير محدد'
    sheet.getCell(row, 4).value = course.endDate || 'غير محدد'
    sheet.getCell(row, 5).value = course.duration || 'غير محدد'
    sheet.getCell(row, 6).value = course.status === 'completed' ? 'مكتمل' : course.status === 'active' ? 'نشط' : 'مجدول'
    sheet.getCell(row, 7).value = courseGrade ? `${courseGrade.grade}%` : 'لم يتم التقييم'
    sheet.getCell(row, 8).value = course.progress ? `${course.progress}%` : '0%'
  })

  // تنسيق الأعمدة
  for (let col = 1; col <= 8; col++) {
    sheet.getColumn(col).width = 20
  }
}

const createTasksDetailSheet = async (workbook, trainee, tasks, grades, colors, language) => {
  const sheet = workbook.addWorksheet(language === 'en' ? 'Tasks & Assignments' : 'المهام والواجبات')

  // العثور على مهام المتدرب
  const traineeTasks = tasks.filter(task =>
    task.assignedUsers?.includes(trainee.id) ||
    task.assignedUsers?.includes(trainee.email)
  )

  if (traineeTasks.length === 0) {
    sheet.getCell('A1').value = language === 'en' ? 'No tasks assigned' : 'لا توجد مهام مخصصة'
    return
  }

  // عناوين الأعمدة
  const headers = language === 'en'
    ? ['Task Name', 'Course', 'Due Date', 'Priority', 'Status', 'Submitted', 'Grade', 'Feedback']
    : ['اسم المهمة', 'الدورة', 'تاريخ الاستحقاق', 'الأولوية', 'الحالة', 'تم التسليم', 'الدرجة', 'التعليقات']

  headers.forEach((header, index) => {
    const cell = sheet.getCell(1, index + 1)
    cell.value = header
    cell.font = { bold: true, color: { argb: colors.white } }
    cell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: colors.success } }
    cell.alignment = { horizontal: 'center', vertical: 'middle' }
  })

  // بيانات المهام
  traineeTasks.forEach((task, index) => {
    const row = index + 2
    const taskGrade = grades.find(g =>
      g.traineeId === trainee.id &&
      (g.taskId === task.id || g.taskName === task.title)
    )

    sheet.getCell(row, 1).value = task.title || 'غير محدد'
    sheet.getCell(row, 2).value = task.course || 'غير محدد'
    sheet.getCell(row, 3).value = task.dueDate || 'غير محدد'
    sheet.getCell(row, 4).value = task.priority || 'متوسط'
    sheet.getCell(row, 5).value = task.status === 'completed' ? 'مكتمل' : task.status === 'submitted' ? 'تم التسليم' : 'قيد التنفيذ'
    sheet.getCell(row, 6).value = task.submissions?.some(s => s.traineeId === trainee.id) ? 'نعم' : 'لا'
    sheet.getCell(row, 7).value = taskGrade ? `${taskGrade.grade}%` : 'لم يتم التقييم'
    sheet.getCell(row, 8).value = taskGrade?.feedback || 'لا توجد تعليقات'
  })

  // تنسيق الأعمدة
  for (let col = 1; col <= 8; col++) {
    sheet.getColumn(col).width = 20
  }
}

const createWorkshopsDetailSheet = async (workbook, trainee, workshops, colors, language) => {
  const sheet = workbook.addWorksheet(language === 'en' ? 'Workshops' : 'ورش العمل')

  // العثور على ورش عمل المتدرب
  const traineeWorkshops = workshops.filter(workshop =>
    workshop.participants?.includes(trainee.id) ||
    workshop.participants?.includes(trainee.email)
  )

  if (traineeWorkshops.length === 0) {
    sheet.getCell('A1').value = language === 'en' ? 'No workshops attended' : 'لم يحضر أي ورش عمل'
    return
  }

  // عناوين الأعمدة
  const headers = language === 'en'
    ? ['Workshop Name', 'Date', 'Time', 'Duration', 'Trainer', 'Location', 'Status', 'Rating']
    : ['اسم ورشة العمل', 'التاريخ', 'الوقت', 'المدة', 'المدرب', 'المكان', 'الحالة', 'التقييم']

  headers.forEach((header, index) => {
    const cell = sheet.getCell(1, index + 1)
    cell.value = header
    cell.font = { bold: true, color: { argb: colors.white } }
    cell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: colors.secondary } }
    cell.alignment = { horizontal: 'center', vertical: 'middle' }
  })

  // بيانات ورش العمل
  traineeWorkshops.forEach((workshop, index) => {
    const row = index + 2

    sheet.getCell(row, 1).value = workshop.title || 'غير محدد'
    sheet.getCell(row, 2).value = workshop.date || 'غير محدد'
    sheet.getCell(row, 3).value = workshop.time || 'غير محدد'
    sheet.getCell(row, 4).value = workshop.duration || 'غير محدد'
    sheet.getCell(row, 5).value = workshop.trainer || 'غير محدد'
    sheet.getCell(row, 6).value = workshop.location || 'غير محدد'
    sheet.getCell(row, 7).value = workshop.status === 'completed' ? 'مكتمل' : workshop.status === 'active' ? 'نشط' : 'مجدول'
    sheet.getCell(row, 8).value = workshop.rating ? `${workshop.rating}/5 ⭐` : 'غير مقيم'
  })

  // تنسيق الأعمدة
  for (let col = 1; col <= 8; col++) {
    sheet.getColumn(col).width = 20
  }
}

const createAttendanceDetailSheet = async (workbook, trainee, attendance, colors, language) => {
  const sheet = workbook.addWorksheet(language === 'en' ? 'Attendance' : 'الحضور والغياب')

  // العثور على سجلات حضور المتدرب
  const traineeAttendance = attendance.filter(record =>
    record.traineeId === trainee.id || record.traineeId === trainee.email
  )

  if (traineeAttendance.length === 0) {
    sheet.getCell('A1').value = language === 'en' ? 'No attendance records' : 'لا توجد سجلات حضور'
    return
  }

  // عناوين الأعمدة
  const headers = language === 'en'
    ? ['Date', 'Session', 'Status', 'Check-in Time', 'Check-out Time', 'Duration', 'Notes']
    : ['التاريخ', 'الجلسة', 'الحالة', 'وقت الدخول', 'وقت الخروج', 'المدة', 'ملاحظات']

  headers.forEach((header, index) => {
    const cell = sheet.getCell(1, index + 1)
    cell.value = header
    cell.font = { bold: true, color: { argb: colors.white } }
    cell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: colors.warning } }
    cell.alignment = { horizontal: 'center', vertical: 'middle' }
  })

  // بيانات الحضور
  traineeAttendance.forEach((record, index) => {
    const row = index + 2

    sheet.getCell(row, 1).value = record.date || 'غير محدد'
    sheet.getCell(row, 2).value = record.session || 'غير محدد'
    sheet.getCell(row, 3).value = record.status === 'present' ? 'حاضر' : record.status === 'absent' ? 'غائب' : 'متأخر'
    sheet.getCell(row, 4).value = record.checkIn || 'غير محدد'
    sheet.getCell(row, 5).value = record.checkOut || 'غير محدد'
    sheet.getCell(row, 6).value = record.duration || 'غير محدد'
    sheet.getCell(row, 7).value = record.notes || 'لا توجد ملاحظات'
  })

  // تنسيق الأعمدة
  for (let col = 1; col <= 7; col++) {
    sheet.getColumn(col).width = 18
  }
}
