import React from 'react'

// مكون رسم بياني دائري بسيط
export const SimplePieChart = ({ data, title, colors }) => {
  
  const total = data.reduce((sum, item) => sum + item.value, 0)
  
  if (total === 0) {
    return (
      <div className="flex items-center justify-center h-64 bg-white dark:bg-gray-800 rounded-lg">
        <p className="text-gray-500 dark:text-gray-400">لا توجد بيانات للعرض</p>
      </div>
    )
  }

  let currentAngle = 0
  const radius = 80
  const centerX = 120
  const centerY = 120

  const segments = data.map((item, index) => {
    const percentage = (item.value / total) * 100
    const angle = (item.value / total) * 360
    const startAngle = currentAngle
    const endAngle = currentAngle + angle
    
    const x1 = centerX + radius * Math.cos((startAngle * Math.PI) / 180)
    const y1 = centerY + radius * Math.sin((startAngle * Math.PI) / 180)
    const x2 = centerX + radius * Math.cos((endAngle * Math.PI) / 180)
    const y2 = centerY + radius * Math.sin((endAngle * Math.PI) / 180)
    
    const largeArcFlag = angle > 180 ? 1 : 0
    
    const pathData = [
      `M ${centerX} ${centerY}`,
      `L ${x1} ${y1}`,
      `A ${radius} ${radius} 0 ${largeArcFlag} 1 ${x2} ${y2}`,
      'Z'
    ].join(' ')
    
    currentAngle += angle
    
    return {
      ...item,
      pathData,
      percentage: percentage.toFixed(1),
      color: colors[index % colors.length]
    }
  })

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
      <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">{title}</h3>
      <div className="flex items-center justify-between">
        <svg width="240" height="240" className="mx-auto">
          {segments.map((segment, index) => (
            <path
              key={index}
              d={segment.pathData}
              fill={segment.color}
              stroke="white"
              strokeWidth="2"
              className="hover:opacity-80 transition-opacity cursor-pointer"
              title={`${segment.label}: ${segment.value} (${segment.percentage}%)`}
            />
          ))}
        </svg>
        <div className="space-y-2">
          {segments.map((segment, index) => (
            <div key={index} className="flex items-center space-x-2 rtl:space-x-reverse">
              <div 
                className="w-4 h-4 rounded-full" 
                style={{ backgroundColor: segment.color }}
              ></div>
              <span className="text-sm text-gray-700 dark:text-gray-300">
                {segment.label}: {segment.value} ({segment.percentage}%)
              </span>
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}

// مكون رسم بياني شريطي بسيط
export const SimpleBarChart = ({ data, title, color = '#3B82F6' }) => {
  
  if (!data || data.length === 0) {
    return (
      <div className="flex items-center justify-center h-64 bg-white dark:bg-gray-800 rounded-lg">
        <p className="text-gray-500 dark:text-gray-400">لا توجد بيانات للعرض</p>
      </div>
    )
  }

  const maxValue = Math.max(...data.map(item => item.value)) || 1 // تجنب القسمة على صفر
  const chartHeight = 200
  const barWidth = 40
  const spacing = 20

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
      <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">{title}</h3>
      <div className="overflow-x-auto">
        <svg 
          width={data.length * (barWidth + spacing) + spacing} 
          height={chartHeight + 60}
          className="mx-auto"
        >
          {data.map((item, index) => {
            const barHeight = Math.max(0, (item.value / maxValue) * chartHeight) || 0
            const x = index * (barWidth + spacing) + spacing
            const y = chartHeight - barHeight + 20
            
            return (
              <g key={index}>
                <rect
                  x={x}
                  y={y}
                  width={barWidth}
                  height={barHeight}
                  fill={color}
                  className="hover:opacity-80 transition-opacity cursor-pointer"
                  title={`${item.label}: ${item.value}`}
                />
                <text
                  x={x + barWidth / 2}
                  y={y - 5}
                  textAnchor="middle"
                  className="text-xs fill-gray-700 dark:fill-gray-300"
                >
                  {item.value}
                </text>
                <text
                  x={x + barWidth / 2}
                  y={chartHeight + 35}
                  textAnchor="middle"
                  className="text-xs fill-gray-700 dark:fill-gray-300"
                  transform={`rotate(-45, ${x + barWidth / 2}, ${chartHeight + 35})`}
                >
                  {item.label}
                </text>
              </g>
            )
          })}
        </svg>
      </div>
    </div>
  )
}

// مكون رسم بياني خطي بسيط
export const SimpleLineChart = ({ data, title, color = '#10B981' }) => {
  
  if (!data || data.length === 0) {
    return (
      <div className="flex items-center justify-center h-64 bg-white dark:bg-gray-800 rounded-lg">
        <p className="text-gray-500 dark:text-gray-400">لا توجد بيانات للعرض</p>
      </div>
    )
  }

  const maxValue = Math.max(...data.map(item => item.value)) || 1
  const minValue = Math.min(...data.map(item => item.value)) || 0
  const chartHeight = 200
  const chartWidth = 400
  const padding = 40

  const points = data.map((item, index) => {
    const x = data.length > 1 ? (index / (data.length - 1)) * (chartWidth - 2 * padding) + padding : chartWidth / 2
    const valueRange = maxValue - minValue || 1 // تجنب القسمة على صفر
    const y = chartHeight - ((item.value - minValue) / valueRange) * (chartHeight - 2 * padding) + padding
    return { x: isNaN(x) ? chartWidth / 2 : x, y: isNaN(y) ? chartHeight / 2 : y, ...item }
  })

  const pathData = points.map((point, index) => 
    `${index === 0 ? 'M' : 'L'} ${point.x} ${point.y}`
  ).join(' ')

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
      <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">{title}</h3>
      <svg width={chartWidth} height={chartHeight + 40} className="mx-auto">
        {/* Grid lines */}
        <defs>
          <pattern id="grid" width="40" height="40" patternUnits="userSpaceOnUse">
            <path d="M 40 0 L 0 0 0 40" fill="none" stroke="#e5e7eb" strokeWidth="1"/>
          </pattern>
        </defs>
        <rect width="100%" height="100%" fill="url(#grid)" opacity="0.3" />
        
        {/* Line */}
        <path
          d={pathData}
          fill="none"
          stroke={color}
          strokeWidth="3"
          className="drop-shadow-sm"
        />
        
        {/* Points */}
        {points.map((point, index) => (
          <circle
            key={index}
            cx={point.x}
            cy={point.y}
            r="4"
            fill={color}
            className="hover:r-6 transition-all cursor-pointer"
            title={`${point.label}: ${point.value}`}
          />
        ))}
        
        {/* Labels */}
        {points.map((point, index) => (
          <text
            key={index}
            x={point.x}
            y={chartHeight + 20}
            textAnchor="middle"
            className="text-xs fill-gray-700 dark:fill-gray-300"
          >
            {point.label}
          </text>
        ))}
      </svg>
    </div>
  )
}

export default { SimplePieChart, SimpleBarChart, SimpleLineChart }