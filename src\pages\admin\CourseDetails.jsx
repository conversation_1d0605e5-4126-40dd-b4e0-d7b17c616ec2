import React, { useState, useEffect } from 'react'
import { useParams, useNavigate } from 'react-router-dom'
import { motion } from 'framer-motion'
import toast from 'react-hot-toast'
import {
  ArrowLeft,
  Edit,
  Users,
  Calendar,
  Clock,
  BookOpen,
  User,
  FileText,
  Settings,
  UserPlus,
  Upload,
  Download,
  Plus,
  Eye,
  CheckCircle,
  AlertCircle,
  Star,
  Target,
  TrendingUp
} from 'lucide-react'
import Button from '../../components/ui/Button'
import { useAuth } from '../../contexts/AuthContext'
import { useLanguage } from '../../contexts/LanguageContext'

const CourseDetails = () => {
  const { courseId } = useParams()
  const navigate = useNavigate()
  const { user } = useAuth()
  const { t } = useLanguage()
  const [course, setCourse] = useState(null)
  const [loading, setLoading] = useState(true)
  const [activeTab, setActiveTab] = useState('overview')
  const [tasks, setTasks] = useState([])
  const [materials, setMaterials] = useState([])
  const [trainees, setTrainees] = useState([])
  const [submissions, setSubmissions] = useState([])

  useEffect(() => {
    loadCourseData()
  }, [courseId])

  const loadCourseData = async () => {
    try {
      setLoading(true)
      
      // تحميل بيانات الدورة
      const courses = JSON.parse(localStorage.getItem('app_courses') || '[]')
      const foundCourse = courses.find(c => c.id === parseInt(courseId))
      
      if (!foundCourse) {
        toast.error('Course not found')
        navigate('/admin/courses')
        return
      }

      setCourse(foundCourse)

      // تحميل المهام المرتبطة بالدورة
      const allTasks = JSON.parse(localStorage.getItem('tasks_data') || '[]')
      const courseTasks = allTasks.filter(task => task.courseId === parseInt(courseId))
      setTasks(courseTasks)

      // تحميل المواد
      const allMaterials = JSON.parse(localStorage.getItem('course_materials') || '[]')
      const courseMaterials = allMaterials.filter(material => material.courseId === parseInt(courseId))
      setMaterials(courseMaterials)

      // تحميل المتدربين
      const usersData = JSON.parse(localStorage.getItem('users') || '[]')
      const allUsers = Array.isArray(usersData) ? usersData : Object.values(usersData)

      console.log('🔍 Checking selected trainees in course:', foundCourse.selectedTrainees)
      console.log('🔍 All users:', allUsers.filter(u => u.role === 'trainee').map(u => u.name))

      let courseTrainees = []

      if (foundCourse.selectedTrainees && Array.isArray(foundCourse.selectedTrainees)) {
        // إذا كانت selectedTrainees مصفوفة من الكائنات
        if (foundCourse.selectedTrainees.length > 0 && typeof foundCourse.selectedTrainees[0] === 'object') {
          const selectedIds = foundCourse.selectedTrainees.map(t => t.id || t.name)
          courseTrainees = allUsers.filter(u =>
            u.role === 'trainee' && selectedIds.includes(u.id || u.name)
          )
        }
        // إذا كانت selectedTrainees مصفوفة من الأسماء أو IDs
        else {
          courseTrainees = allUsers.filter(u =>
            u.role === 'trainee' && foundCourse.selectedTrainees.includes(u.name || u.id)
          )
        }
      } else {
        // إذا لم يتم اختيار متدربين محددين، اختر جميع المتدربين
        courseTrainees = allUsers.filter(u => u.role === 'trainee')
      }

      console.log('👥 Loaded trainees for course:', courseTrainees.length, courseTrainees.map(t => t.name))
      setTrainees(courseTrainees)

      // تحميل التسليمات
      const allSubmissions = JSON.parse(localStorage.getItem('task_submissions') || '[]')
      const courseSubmissions = allSubmissions.filter(sub => 
        courseTasks.some(task => task.id === sub.taskId)
      )
      setSubmissions(courseSubmissions)

      console.log('📚 تم تحميل بيانات الدورة:', {
        course: foundCourse,
        tasks: courseTasks.length,
        materials: courseMaterials.length,
        trainees: courseTrainees.length,
        submissions: courseSubmissions.length
      })

    } catch (error) {
      console.error('Error loading course data:', error)
      toast.error('Failed to load course data')
    } finally {
      setLoading(false)
    }
  }

  const handleEditCourse = () => {
    navigate(`/courses/${courseId}/edit`)
  }

  const handleAssignTrainees = () => {
    navigate(`/courses/${courseId}/assign`)
  }

  const handleManageMaterials = () => {
    navigate(`/courses/${courseId}/materials`)
  }

  const handleCreateTask = () => {
    navigate(`/courses/${courseId}/tasks/create`)
  }

  const handleViewTask = (taskId) => {
    navigate(`/tasks/${taskId}`)
  }

  const handleEvaluateTrainee = (trainee) => {
    // إنشاء نافذة تقييم بسيطة
    const rating = prompt(`تقييم المتدرب: ${trainee.name}\nأدخل التقييم من 1 إلى 5:`)
    if (rating && !isNaN(rating) && rating >= 1 && rating <= 5) {
      const evaluation = {
        id: Date.now(),
        traineeId: trainee.id,
        traineeName: trainee.name,
        courseId: parseInt(courseId),
        courseName: course.courseName,
        rating: parseInt(rating),
        evaluatedBy: user.name,
        evaluatedAt: new Date().toISOString(),
        comments: prompt('تعليقات إضافية (اختياري):') || ''
      }

      // حفظ التقييم
      const evaluations = JSON.parse(localStorage.getItem('trainee_evaluations') || '[]')
      evaluations.push(evaluation)
      localStorage.setItem('trainee_evaluations', JSON.stringify(evaluations))

      toast.success(`تم تقييم المتدرب ${trainee.name} بنجاح!`)
    } else if (rating !== null) {
      toast.error('يرجى إدخال تقييم صحيح من 1 إلى 5')
    }
  }

  const handleViewTraineeDetails = (trainee) => {
    // عرض تفاصيل المتدرب في نافذة منبثقة
    const evaluations = JSON.parse(localStorage.getItem('trainee_evaluations') || '[]')
    const traineeEvaluations = evaluations.filter(e => e.traineeId === trainee.id && e.courseId === parseInt(courseId))

    let details = `تفاصيل المتدرب: ${trainee.name}\n\n`
    details += `البريد الإلكتروني: ${trainee.email}\n`
    details += `القسم: ${trainee.department || 'غير محدد'}\n`
    details += `الهاتف: ${trainee.phone || 'غير محدد'}\n\n`

    if (traineeEvaluations.length > 0) {
      details += `التقييمات السابقة:\n`
      traineeEvaluations.forEach((evaluation, index) => {
        details += `${index + 1}. التقييم: ${evaluation.rating}/5 - بواسطة: ${evaluation.evaluatedBy}\n`
        if (evaluation.comments) details += `   التعليق: ${evaluation.comments}\n`
        details += `   التاريخ: ${new Date(evaluation.evaluatedAt).toLocaleDateString('ar-SA')}\n\n`
      })
    } else {
      details += `لا توجد تقييمات سابقة لهذا المتدرب في هذه الدورة.`
    }

    alert(details)
  }

  const calculateProgress = () => {
    if (tasks.length === 0) return 0
    const completedTasks = tasks.filter(task => task.status === 'completed').length
    return Math.round((completedTasks / tasks.length) * 100)
  }

  const getTaskStatusColor = (status) => {
    switch (status) {
      case 'completed': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
      case 'in_progress': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'
      case 'pending': return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200'
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200'
    }
  }

  const getTaskStatusText = (status) => {
    switch (status) {
      case 'completed': return 'مكتمل'
      case 'in_progress': return 'قيد التنفيذ'
      case 'pending': return 'في الانتظار'
      default: return 'غير محدد'
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary-500"></div>
      </div>
    )
  }

  if (!course) {
    return (
      <div className="text-center py-12">
        <BookOpen className="mx-auto h-12 w-12 text-gray-400" />
        <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-gray-100">{t('courses.notFound')}</h3>
        <div className="mt-6">
          <Button variant="primary" onClick={() => navigate('/courses')}>
            {t('courses.backToCourses')}
          </Button>
        </div>
      </div>
    )
  }

  const progress = calculateProgress()

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4 rtl:space-x-reverse">
          <Button
            variant="outline"
            onClick={() => navigate('/courses')}
            icon={ArrowLeft}
          >
            {t('common.back')}
          </Button>
          <div>
            <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100">
              {course.courseName || course.title}
            </h1>
            <p className="text-sm text-gray-500 dark:text-gray-400">
              دورة رقم {course.courseNumber} - {course.department}
            </p>
          </div>
        </div>

        <div className="flex items-center space-x-3 rtl:space-x-reverse">
          <Button
            variant="outline"
            onClick={handleEditCourse}
            icon={Edit}
          >
            {t('common.edit')}
          </Button>
          <Button
            variant="outline"
            onClick={handleAssignTrainees}
            icon={UserPlus}
          >
            {t('courses.assignTrainees')}
          </Button>
          <Button
            variant="primary"
            onClick={handleManageMaterials}
            icon={Upload}
          >
            {t('courses.manageMaterials')}
          </Button>
        </div>
      </div>

      {/* Course Info Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
          <div className="flex items-center">
            <div className="p-3 rounded-lg bg-blue-500">
              <Users className="h-6 w-6 text-white" />
            </div>
            <div className="ml-4 rtl:ml-0 rtl:mr-4">
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">{t('courses.trainees')}</p>
              <p className="text-2xl font-bold text-gray-900 dark:text-gray-100">{trainees.length}</p>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
          <div className="flex items-center">
            <div className="p-3 rounded-lg bg-green-500">
              <CheckCircle className="h-6 w-6 text-white" />
            </div>
            <div className="ml-4 rtl:ml-0 rtl:mr-4">
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">{t('courses.tasks')}</p>
              <p className="text-2xl font-bold text-gray-900 dark:text-gray-100">{tasks.length}</p>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
          <div className="flex items-center">
            <div className="p-3 rounded-lg bg-purple-500">
              <FileText className="h-6 w-6 text-white" />
            </div>
            <div className="ml-4 rtl:ml-0 rtl:mr-4">
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">{t('courses.materials')}</p>
              <p className="text-2xl font-bold text-gray-900 dark:text-gray-100">{materials.length}</p>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
          <div className="flex items-center">
            <div className="p-3 rounded-lg bg-orange-500">
              <TrendingUp className="h-6 w-6 text-white" />
            </div>
            <div className="ml-4 rtl:ml-0 rtl:mr-4">
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">{t('courses.progress')}</p>
              <p className="text-2xl font-bold text-gray-900 dark:text-gray-100">{progress}%</p>
            </div>
          </div>
        </div>
      </div>

      {/* Progress Bar */}
      <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
        <div className="flex justify-between items-center mb-2">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">{t('courses.courseProgress')}</h3>
          <span className="text-sm font-medium text-gray-600 dark:text-gray-400">{progress}%</span>
        </div>
        <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-3">
          <div
            className="bg-gradient-to-r from-blue-500 to-green-500 h-3 rounded-full transition-all duration-300"
            style={{ width: `${progress}%` }}
          ></div>
        </div>
      </div>

      {/* Tabs */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm">
        <div className="flex border-b border-gray-200 dark:border-gray-700">
          {[
            { id: 'overview', name: t('courses.overview'), icon: Eye },
            { id: 'tasks', name: t('courses.tasks'), icon: CheckCircle },
            { id: 'trainees', name: t('courses.trainees'), icon: Users },
            { id: 'materials', name: t('courses.materials'), icon: FileText }
          ].map(tab => {
            const Icon = tab.icon
            return (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`flex items-center px-6 py-3 text-sm font-medium transition-colors ${
                  activeTab === tab.id
                    ? 'bg-blue-500 text-white border-b-2 border-blue-500'
                    : 'text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200'
                }`}
              >
                <Icon className="w-4 h-4 mr-2 rtl:mr-0 rtl:ml-2" />
                {tab.name}
              </button>
            )
          })}
        </div>

        {/* Tab Content */}
        <div className="p-6">
          {/* Overview Tab */}
          {activeTab === 'overview' && (
            <div className="space-y-6">
              {/* Course Information */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">{t('courses.courseInfo')}</h3>
                  <div className="space-y-3">
                    <div className="flex justify-between">
                      <span className="text-gray-600 dark:text-gray-400">{t('courses.courseName')}:</span>
                      <span className="font-medium text-gray-900 dark:text-gray-100">{course.courseName}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600 dark:text-gray-400">{t('courses.courseNumber')}:</span>
                      <span className="font-medium text-gray-900 dark:text-gray-100">{course.courseNumber}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600 dark:text-gray-400">{t('common.department')}:</span>
                      <span className="font-medium text-gray-900 dark:text-gray-100">{course.department}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600 dark:text-gray-400">{t('courses.trainer')}:</span>
                      <span className="font-medium text-gray-900 dark:text-gray-100">{course.trainer}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600 dark:text-gray-400">{t('courses.courseManager')}:</span>
                      <span className="font-medium text-gray-900 dark:text-gray-100">{course.courseManager}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600 dark:text-gray-400">{t('courses.startDate')}:</span>
                      <span className="font-medium text-gray-900 dark:text-gray-100">
                        {course.startDate ? new Date(course.startDate).toLocaleDateString('en-US') : t('common.notSpecified')}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600 dark:text-gray-400">{t('courses.endDate')}:</span>
                      <span className="font-medium text-gray-900 dark:text-gray-100">
                        {course.endDate ? new Date(course.endDate).toLocaleDateString('en-US') : t('common.notSpecified')}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600 dark:text-gray-400">{t('common.status')}:</span>
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                        course.status === 'active' ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' :
                        course.status === 'completed' ? 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200' :
                        'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200'
                      }`}>
                        {course.status === 'active' ? t('common.active') : course.status === 'completed' ? t('common.completed') : t('common.notSpecified')}
                      </span>
                    </div>
                  </div>
                </div>

                <div className="space-y-4">
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">{t('courses.quickStats')}</h3>
                  <div className="grid grid-cols-2 gap-4">
                    <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4 text-center">
                      <div className="text-2xl font-bold text-blue-600 dark:text-blue-400">{trainees.length}</div>
                      <div className="text-sm text-gray-600 dark:text-gray-400">{t('courses.registeredTrainees')}</div>
                    </div>
                    <div className="bg-green-50 dark:bg-green-900/20 rounded-lg p-4 text-center">
                      <div className="text-2xl font-bold text-green-600 dark:text-green-400">{tasks.filter(t => t.status === 'completed').length}</div>
                      <div className="text-sm text-gray-600 dark:text-gray-400">{t('courses.completedTasks')}</div>
                    </div>
                    <div className="bg-purple-50 dark:bg-purple-900/20 rounded-lg p-4 text-center">
                      <div className="text-2xl font-bold text-purple-600 dark:text-purple-400">{materials.length}</div>
                      <div className="text-sm text-gray-600 dark:text-gray-400">{t('courses.educationalMaterials')}</div>
                    </div>
                    <div className="bg-orange-50 dark:bg-orange-900/20 rounded-lg p-4 text-center">
                      <div className="text-2xl font-bold text-orange-600 dark:text-orange-400">{submissions.length}</div>
                      <div className="text-sm text-gray-600 dark:text-gray-400">{t('courses.taskSubmissions')}</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Tasks Tab */}
          {activeTab === 'tasks' && (
            <div className="space-y-6">
              <div className="flex justify-between items-center">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">{t('courses.tasks')}</h3>
                <Button
                  variant="primary"
                  onClick={handleCreateTask}
                  icon={Plus}
                >
                  {t('courses.addNewTask')}
                </Button>
              </div>

              {tasks.length > 0 ? (
                <div className="space-y-4">
                  {tasks.map(task => (
                    <div key={task.id} className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                      <div className="flex justify-between items-start">
                        <div className="flex-1">
                          <h4 className="font-medium text-gray-900 dark:text-gray-100 mb-2">{task.title}</h4>
                          <p className="text-sm text-gray-600 dark:text-gray-400 mb-3">{task.description}</p>
                          <div className="flex items-center space-x-4 rtl:space-x-reverse text-sm text-gray-500 dark:text-gray-400">
                            <span className="flex items-center">
                              <Calendar className="w-4 h-4 mr-1 rtl:mr-0 rtl:ml-1" />
                              {task.dueDate ? new Date(task.dueDate).toLocaleDateString('ar-SA') : 'غير محدد'}
                            </span>
                            <span className="flex items-center">
                              <Users className="w-4 h-4 mr-1 rtl:mr-0 rtl:ml-1" />
                              {task.assignedUsers?.length || 0} متدرب
                            </span>
                          </div>
                        </div>
                        <div className="flex items-center space-x-3 rtl:space-x-reverse">
                          <span className={`px-2 py-1 rounded-full text-xs font-medium ${getTaskStatusColor(task.status)}`}>
                            {getTaskStatusText(task.status)}
                          </span>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleViewTask(task.id)}
                            icon={Eye}
                          >
                            عرض
                          </Button>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-12">
                  <CheckCircle className="mx-auto h-12 w-12 text-gray-400" />
                  <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-gray-100">{t('courses.noTasks')}</h3>
                  <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">{t('courses.startAddingTasks')}</p>
                  <div className="mt-6">
                    <Button
                      variant="primary"
                      onClick={handleCreateTask}
                      icon={Plus}
                    >
                      {t('courses.addNewTask')}
                    </Button>
                  </div>
                </div>
              )}
            </div>
          )}

          {/* Trainees Tab */}
          {activeTab === 'trainees' && (
            <div className="space-y-6">
              <div className="flex justify-between items-center">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">{t('courses.enrolledTrainees')}</h3>
                <Button
                  variant="primary"
                  onClick={handleAssignTrainees}
                  icon={UserPlus}
                >
                  {t('courses.addTrainees')}
                </Button>
              </div>

              {trainees.length > 0 ? (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {trainees.map(trainee => {
                    const traineeSubmissions = submissions.filter(sub => sub.traineeName === trainee.name)
                    const traineeTasks = tasks.filter(task => task.assignedUsers?.includes(trainee.name))
                    const completedTasks = traineeTasks.filter(task => task.status === 'completed')
                    const progress = traineeTasks.length > 0 ? Math.round((completedTasks.length / traineeTasks.length) * 100) : 0

                    return (
                      <div key={trainee.id} className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                        <div className="flex items-center space-x-3 rtl:space-x-reverse mb-3">
                          <div className="w-10 h-10 bg-blue-500 rounded-full flex items-center justify-center text-white font-bold">
                            {trainee.name?.charAt(0) || 'م'}
                          </div>
                          <div className="flex-1">
                            <h4 className="font-medium text-gray-900 dark:text-gray-100">{trainee.name}</h4>
                            <p className="text-sm text-gray-600 dark:text-gray-400">{trainee.email}</p>
                          </div>
                        </div>
                        <div className="space-y-2">
                          <div className="flex justify-between text-sm">
                            <span className="text-gray-600 dark:text-gray-400">التقدم:</span>
                            <span className="font-medium text-gray-900 dark:text-gray-100">{progress}%</span>
                          </div>
                          <div className="w-full bg-gray-200 dark:bg-gray-600 rounded-full h-2">
                            <div
                              className="bg-blue-500 h-2 rounded-full transition-all duration-300"
                              style={{ width: `${progress}%` }}
                            ></div>
                          </div>
                          <div className="flex justify-between text-xs text-gray-500 dark:text-gray-400">
                            <span>{completedTasks.length}/{traineeTasks.length} مهام</span>
                            <span>{traineeSubmissions.length} تسليم</span>
                          </div>
                          <div className="mt-3 flex space-x-2 rtl:space-x-reverse">
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => handleEvaluateTrainee(trainee)}
                              icon={Star}
                              className="flex-1"
                            >
                              تقييم
                            </Button>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => handleViewTraineeDetails(trainee)}
                              icon={Eye}
                              className="flex-1"
                            >
                              عرض
                            </Button>
                          </div>
                        </div>
                      </div>
                    )
                  })}
                </div>
              ) : (
                <div className="text-center py-12">
                  <Users className="mx-auto h-12 w-12 text-gray-400" />
                  <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-gray-100">{t('courses.noTrainees')}</h3>
                  <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">{t('courses.startAddingTrainees')}</p>
                  <div className="mt-6">
                    <Button
                      variant="primary"
                      onClick={handleAssignTrainees}
                      icon={UserPlus}
                    >
                      {t('courses.addTrainees')}
                    </Button>
                  </div>
                </div>
              )}
            </div>
          )}

          {/* Materials Tab */}
          {activeTab === 'materials' && (
            <div className="space-y-6">
              <div className="flex justify-between items-center">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">{t('courses.materials')}</h3>
                <Button
                  variant="primary"
                  onClick={handleManageMaterials}
                  icon={Upload}
                >
                  {t('courses.addMaterials')}
                </Button>
              </div>

              {materials.length > 0 ? (
                <div className="space-y-4">
                  {materials.map(material => (
                    <div key={material.id} className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                      <div className="flex justify-between items-start">
                        <div className="flex-1">
                          <h4 className="font-medium text-gray-900 dark:text-gray-100 mb-2">{material.title}</h4>
                          <p className="text-sm text-gray-600 dark:text-gray-400 mb-3">{material.description}</p>
                          <div className="flex items-center space-x-4 rtl:space-x-reverse text-sm text-gray-500 dark:text-gray-400">
                            <span className="flex items-center">
                              <FileText className="w-4 h-4 mr-1 rtl:mr-0 rtl:ml-1" />
                              {material.type || 'ملف'}
                            </span>
                            <span className="flex items-center">
                              <Calendar className="w-4 h-4 mr-1 rtl:mr-0 rtl:ml-1" />
                              {material.uploadDate ? new Date(material.uploadDate).toLocaleDateString('ar-SA') : 'غير محدد'}
                            </span>
                          </div>
                        </div>
                        <div className="flex items-center space-x-3 rtl:space-x-reverse">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => window.open(material.url, '_blank')}
                            icon={Download}
                          >
                            تحميل
                          </Button>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-12">
                  <FileText className="mx-auto h-12 w-12 text-gray-400" />
                  <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-gray-100">{t('courses.noMaterials')}</h3>
                  <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">{t('courses.startAddingMaterials')}</p>
                  <div className="mt-6">
                    <Button
                      variant="primary"
                      onClick={handleManageMaterials}
                      icon={Upload}
                    >
                      {t('courses.addMaterials')}
                    </Button>
                  </div>
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

export default CourseDetails
