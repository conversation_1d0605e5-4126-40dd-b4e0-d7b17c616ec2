import React, { useState, useRef, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import {
  ChevronDown,
  Check,
  X,
  Users
} from 'lucide-react'
import { useLanguage } from '../../contexts/LanguageContext'

const SimpleTraineeDropdown = ({ 
  selectedTrainees = [], 
  onSelectionChange, 
  placeholder = "اختر المتدربين...",
  disabled = false
}) => {
  const { t } = useLanguage()
  const [isOpen, setIsOpen] = useState(false)
  const dropdownRef = useRef(null)

  // تحميل المتدربين الحقيقيين من localStorage
  const [allTrainees, setAllTrainees] = useState([])

  useEffect(() => {
    const loadTrainees = async () => {
      try {
        // تحميل المستخدمين من localStorage
        const users = JSON.parse(localStorage.getItem('users') || '{}')
        const appUsers = JSON.parse(localStorage.getItem('app_users') || '{}')

        // دمج المستخدمين من كلا المصدرين
        const allUsers = { ...users, ...appUsers }

        // فلترة المتدربين فقط
        const trainees = Object.values(allUsers)
          .filter(user => user.role === 'trainee')
          .map(user => ({
            id: user.id || user.email,
            name: user.name,
            department: user.department || 'غير محدد'
          }))

        setAllTrainees(trainees)
      } catch (error) {
        console.error('Error loading trainees:', error)
        setAllTrainees([])
      }
    }

    loadTrainees()
  }, [])

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setIsOpen(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => document.removeEventListener('mousedown', handleClickOutside)
  }, [])

  const handleTraineeToggle = (trainee) => {
    if (disabled) return

    const isSelected = selectedTrainees.some(t => t.id === trainee.id)
    let newSelection

    if (isSelected) {
      newSelection = selectedTrainees.filter(t => t.id !== trainee.id)
    } else {
      newSelection = [...selectedTrainees, trainee]
    }

    onSelectionChange(newSelection)
  }

  const handleSelectAll = () => {
    if (disabled) return

    if (selectedTrainees.length === allTrainees.length) {
      // Deselect all
      onSelectionChange([])
    } else {
      // Select all
      onSelectionChange([...allTrainees])
    }
  }

  const handleRemoveTrainee = (traineeId) => {
    if (disabled) return
    const newSelection = selectedTrainees.filter(t => t.id !== traineeId)
    onSelectionChange(newSelection)
  }

  const getInitials = (name) => {
    return name.split(' ').map(n => n[0]).join('').substring(0, 2).toUpperCase()
  }

  const isAllSelected = allTrainees.length > 0 && 
    allTrainees.every(trainee => selectedTrainees.some(t => t.id === trainee.id))

  return (
    <div className="relative" ref={dropdownRef}>
      {/* Selected Trainees Display */}
      {selectedTrainees.length > 0 && (
        <div className="mb-3">
          <div className="flex flex-wrap gap-2">
            {selectedTrainees.map(trainee => (
              <motion.div
                key={trainee.id}
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                exit={{ opacity: 0, scale: 0.8 }}
                className="flex items-center space-x-2 rtl:space-x-reverse bg-primary-100 dark:bg-primary-900 text-primary-800 dark:text-primary-200 px-3 py-1 rounded-full text-sm"
              >
                <div className="flex items-center space-x-2 rtl:space-x-reverse">
                  <div className="w-6 h-6 rounded-full bg-primary-200 dark:bg-primary-800 flex items-center justify-center text-xs font-medium">
                    {getInitials(trainee.name)}
                  </div>
                  <span>{trainee.name}</span>
                </div>
                {!disabled && (
                  <button
                    onClick={() => handleRemoveTrainee(trainee.id)}
                    className="text-primary-600 hover:text-primary-800 dark:text-primary-400 dark:hover:text-primary-200"
                  >
                    <X className="w-4 h-4" />
                  </button>
                )}
              </motion.div>
            ))}
          </div>
        </div>
      )}

      {/* Dropdown Trigger */}
      <button
        type="button"
        onClick={() => !disabled && setIsOpen(!isOpen)}
        disabled={disabled}
        className={`w-full flex items-center justify-between px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-primary-500 ${
          disabled ? 'opacity-50 cursor-not-allowed' : 'hover:border-gray-400 dark:hover:border-gray-500'
        }`}
      >
        <div className="flex items-center space-x-2 rtl:space-x-reverse">
          <Users className="w-4 h-4 text-gray-400" />
          <span className={selectedTrainees.length === 0 ? 'text-gray-500 dark:text-gray-400' : ''}>
            {selectedTrainees.length === 0 
              ? placeholder 
              : `${selectedTrainees.length} ${t('common.selected')}`
            }
          </span>
        </div>
        <ChevronDown className={`w-4 h-4 text-gray-400 transition-transform ${isOpen ? 'rotate-180' : ''}`} />
      </button>

      {/* Dropdown Menu */}
      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
            className="absolute z-50 w-full mt-1 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-md shadow-lg max-h-60 overflow-hidden"
          >
            {/* Select All Button */}
            <div className="p-3 border-b border-gray-200 dark:border-gray-700">
              <button
                onClick={handleSelectAll}
                className="w-full flex items-center justify-center space-x-2 rtl:space-x-reverse px-3 py-2 bg-primary-50 dark:bg-primary-900/20 text-primary-700 dark:text-primary-300 rounded-md hover:bg-primary-100 dark:hover:bg-primary-900/40 transition-colors"
              >
                <Users className="w-4 h-4" />
                <span>
                  {isAllSelected ? t('common.deselectAll') : t('common.selectAll')}
                </span>
              </button>
            </div>

            {/* Trainees List */}
            <div className="max-h-48 overflow-y-auto">
              {allTrainees.map(trainee => {
                const isSelected = selectedTrainees.some(t => t.id === trainee.id)
                return (
                  <motion.button
                    key={trainee.id}
                    type="button"
                    onClick={() => handleTraineeToggle(trainee)}
                    className={`w-full flex items-center space-x-3 rtl:space-x-reverse px-3 py-2 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors ${
                      isSelected ? 'bg-primary-50 dark:bg-primary-900/20' : ''
                    }`}
                    whileHover={{ backgroundColor: 'rgba(0,0,0,0.05)' }}
                  >
                    {/* Avatar */}
                    <div className="flex-shrink-0">
                      <div className="w-8 h-8 rounded-full bg-primary-100 dark:bg-primary-900 flex items-center justify-center">
                        <span className="text-primary-600 dark:text-primary-400 font-medium text-sm">
                          {getInitials(trainee.name)}
                        </span>
                      </div>
                    </div>

                    {/* Trainee Info */}
                    <div className="flex-1 text-left rtl:text-right">
                      <div className="font-medium text-gray-900 dark:text-gray-100">
                        {trainee.name}
                      </div>
                      <div className="text-sm text-gray-500 dark:text-gray-400">
                        {trainee.department}
                      </div>
                    </div>

                    {/* Checkbox */}
                    <div className="flex-shrink-0">
                      <div className={`w-5 h-5 rounded border-2 flex items-center justify-center ${
                        isSelected 
                          ? 'bg-primary-600 border-primary-600' 
                          : 'border-gray-300 dark:border-gray-600'
                      }`}>
                        {isSelected && (
                          <Check className="w-3 h-3 text-white" />
                        )}
                      </div>
                    </div>
                  </motion.button>
                )
              })}
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  )
}

export default SimpleTraineeDropdown
