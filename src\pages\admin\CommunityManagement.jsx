import React, { useState } from 'react'
import { motion } from 'framer-motion'
import toast from 'react-hot-toast'
import {
  Plus,
  Search,
  Filter,
  Download,
  Upload,
  Edit,
  Trash2,
  Eye,
  Users,
  MessageSquare,
  Heart,
  Share2,
  Flag,
  Shield,
  UserCheck,
  UserX,
  AlertTriangle,
  TrendingUp,
  Activity,
  Clock
} from 'lucide-react'
import Button from '../../components/ui/Button'
import { useLanguage } from '../../contexts/LanguageContext'

const CommunityManagement = () => {
  const { t } = useLanguage()
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedFilter, setSelectedFilter] = useState('all')
  const [isExporting, setIsExporting] = useState(false)

  // تحميل بيانات المجتمع الحقيقية من localStorage
  const [posts, setPosts] = useState([])

  // تحميل المنشورات من localStorage
  useEffect(() => {
    const loadPosts = () => {
      try {
        const savedPosts = JSON.parse(localStorage.getItem('community_posts') || '[]')
        setPosts(savedPosts)
      } catch (error) {
        console.error('Error loading community posts:', error)
        setPosts([])
      }
    }

    loadPosts()
  }, [])

  const filteredPosts = posts.filter(post => {
    const matchesSearch = post.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         post.author.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         post.content.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesFilter = selectedFilter === 'all' ||
                         (selectedFilter === 'reported' && post.reported) ||
                         (selectedFilter === 'flagged' && post.status === 'flagged') ||
                         post.status === selectedFilter
    return matchesSearch && matchesFilter
  })

  const getStatusColor = (status) => {
    switch (status) {
      case 'published': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
      case 'flagged': return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
      case 'pending': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'
      case 'archived': return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200'
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200'
    }
  }

  const getCategoryColor = (category) => {
    switch (category) {
      case 'discussion': return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200'
      case 'question': return 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200'
      case 'achievement': return 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200'
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200'
    }
  }

  const formatDate = (dateString) => {
    const date = new Date(dateString)
    return date.toLocaleDateString() + ' ' + date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
  }

  // Button handlers
  const handleExportPosts = async () => {
    setIsExporting(true)
    try {
      await new Promise(resolve => setTimeout(resolve, 2000))

      const csvContent = [
        ['Title', 'Author', 'Course', 'Category', 'Status', 'Likes', 'Comments', 'Created'],
        ...filteredPosts.map(post => [
          post.title,
          post.author,
          post.course,
          post.category,
          post.status,
          post.likes,
          post.comments,
          formatDate(post.createdAt)
        ])
      ].map(row => row.join(',')).join('\n')

      const blob = new Blob([csvContent], { type: 'text/csv' })
      const url = window.URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = 'community_posts.csv'
      a.click()
      window.URL.revokeObjectURL(url)

      toast.success('Community posts exported successfully!')
    } catch (error) {
      toast.error('Failed to export posts')
    } finally {
      setIsExporting(false)
    }
  }

  const handleViewPost = (postId) => {
    toast.info(`View post ${postId} - Feature coming soon!`)
  }

  const handleEditPost = (postId) => {
    toast.info(`Edit post ${postId} - Feature coming soon!`)
  }

  const handleDeletePost = (postId) => {
    if (window.confirm('Are you sure you want to delete this post?')) {
      setPosts(prev => prev.filter(p => p.id !== postId))
      toast.success('Post deleted successfully!')
    }
  }

  const handleApprovePost = (postId) => {
    setPosts(prev => prev.map(p =>
      p.id === postId ? { ...p, status: 'published', reported: false } : p
    ))
    toast.success('Post approved!')
  }

  const handleFlagPost = (postId) => {
    setPosts(prev => prev.map(p =>
      p.id === postId ? { ...p, status: 'flagged', reported: true } : p
    ))
    toast.warning('Post flagged for review!')
  }

  const handleMoreFilters = () => {
    toast.info('More filters - Feature coming soon!')
  }

  const stats = [
    { name: 'Total Posts', value: posts.length, icon: MessageSquare, color: 'bg-blue-500' },
    { name: 'Active Users', value: '156', icon: Users, color: 'bg-green-500' },
    { name: 'Reported Posts', value: posts.filter(p => p.reported).length, icon: Flag, color: 'bg-red-500' },
    { name: 'Engagement Rate', value: '87%', icon: TrendingUp, color: 'bg-purple-500' }
  ]

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100">
            Community Management
          </h1>
          <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
            Monitor and moderate community posts and interactions
          </p>
        </div>
        <div className="mt-4 sm:mt-0 flex space-x-3">
          <Button
            variant="outline"
            onClick={handleExportPosts}
            loading={isExporting}
            icon={Download}
          >
            Export
          </Button>
          <Button
            variant="primary"
            onClick={() => toast.info('Create announcement - Feature coming soon!')}
            icon={Plus}
          >
            Create Announcement
          </Button>
        </div>
      </div>

      {/* Stats */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
        {stats.map((stat, index) => {
          const Icon = stat.icon
          return (
            <motion.div
              key={stat.name}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
              className="card"
            >
              <div className="flex items-center">
                <div className={`p-3 rounded-xl ${stat.color}`}>
                  <Icon className="h-6 w-6 text-white" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                    {stat.name}
                  </p>
                  <p className="text-2xl font-semibold text-gray-900 dark:text-gray-100">
                    {stat.value}
                  </p>
                </div>
              </div>
            </motion.div>
          )
        })}
      </div>

      {/* Filters and Search */}
      <div className="card">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0">
          <div className="flex flex-col sm:flex-row space-y-4 sm:space-y-0 sm:space-x-4">
            {/* Search */}
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <input
                type="text"
                placeholder="Search posts..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 input-field w-full sm:w-64"
              />
            </div>

            {/* Status Filter */}
            <select
              value={selectedFilter}
              onChange={(e) => setSelectedFilter(e.target.value)}
              className="input-field w-full sm:w-auto"
            >
              <option value="all">All Posts</option>
              <option value="published">Published</option>
              <option value="flagged">Flagged</option>
              <option value="reported">Reported</option>
              <option value="pending">Pending</option>
              <option value="archived">Archived</option>
            </select>
          </div>

          <div className="flex space-x-2">
            <Button
              variant="outline"
              onClick={handleMoreFilters}
              icon={Filter}
            >
              More Filters
            </Button>
          </div>
        </div>
      </div>

      {/* Posts List */}
      <div className="space-y-4">
        {filteredPosts.map((post, index) => (
          <motion.div
            key={post.id}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: index * 0.1 }}
            className={`card ${post.reported ? 'border-l-4 border-red-500 bg-red-50 dark:bg-red-900/10' : ''}`}
          >
            {/* Post Header */}
            <div className="flex items-start justify-between mb-4">
              <div className="flex-1">
                <div className="flex items-center space-x-3 mb-2">
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                    {post.title}
                  </h3>
                  <span className={`px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(post.status)}`}>
                    {post.status.charAt(0).toUpperCase() + post.status.slice(1)}
                  </span>
                  <span className={`px-2 py-1 text-xs font-semibold rounded-full ${getCategoryColor(post.category)}`}>
                    {post.category.charAt(0).toUpperCase() + post.category.slice(1)}
                  </span>
                  {post.reported && (
                    <div className="flex items-center text-red-600 dark:text-red-400">
                      <Flag className="w-4 h-4 mr-1" />
                      <span className="text-xs font-medium">Reported</span>
                    </div>
                  )}
                </div>

                <p className="text-sm text-gray-600 dark:text-gray-400 line-clamp-2 mb-3">
                  {post.content}
                </p>

                <div className="flex items-center space-x-4 text-sm text-gray-500 dark:text-gray-400">
                  <div className="flex items-center">
                    <Users className="w-4 h-4 mr-1" />
                    {post.author} ({post.authorRole})
                  </div>
                  <div className="flex items-center">
                    <Activity className="w-4 h-4 mr-1" />
                    {post.course}
                  </div>
                  <div className="flex items-center">
                    <Clock className="w-4 h-4 mr-1" />
                    {formatDate(post.createdAt)}
                  </div>
                </div>
              </div>
            </div>

            {/* Engagement Stats */}
            <div className="flex items-center space-x-6 mb-4 p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
              <div className="flex items-center text-sm text-gray-600 dark:text-gray-400">
                <Heart className="w-4 h-4 mr-1 text-red-500" />
                {post.likes} likes
              </div>
              <div className="flex items-center text-sm text-gray-600 dark:text-gray-400">
                <MessageSquare className="w-4 h-4 mr-1 text-blue-500" />
                {post.comments} comments
              </div>
              <div className="flex items-center text-sm text-gray-600 dark:text-gray-400">
                <Share2 className="w-4 h-4 mr-1 text-green-500" />
                {post.shares} shares
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex items-center justify-between pt-4 border-t border-gray-200 dark:border-gray-700">
              <div className="flex space-x-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleViewPost(post.id)}
                  icon={Eye}
                >
                  View
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleEditPost(post.id)}
                  icon={Edit}
                >
                  Edit
                </Button>
                <Button
                  variant="danger"
                  size="sm"
                  onClick={() => handleDeletePost(post.id)}
                  icon={Trash2}
                >
                  Delete
                </Button>
              </div>

              <div className="flex space-x-2">
                {post.status === 'flagged' && (
                  <Button
                    variant="success"
                    size="sm"
                    onClick={() => handleApprovePost(post.id)}
                    icon={UserCheck}
                  >
                    Approve
                  </Button>
                )}
                {post.status === 'published' && !post.reported && (
                  <Button
                    variant="warning"
                    size="sm"
                    onClick={() => handleFlagPost(post.id)}
                    icon={Flag}
                  >
                    Flag
                  </Button>
                )}
              </div>
            </div>
          </motion.div>
        ))}
      </div>

      {filteredPosts.length === 0 && (
        <div className="text-center py-12">
          <MessageSquare className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-gray-100">لا توجد منشورات</h3>
          <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
            {selectedFilter === 'all' ? 'لا توجد منشورات في المجتمع.' : `لا توجد منشورات ${selectedFilter}.`}
          </p>
        </div>
      )}
    </div>
  )
}

export default CommunityManagement
