import React, { useState, useEffect } from 'react'
import { Link, useNavigate } from 'react-router-dom'
import { motion } from 'framer-motion'
import {
  Clock,
  Calendar,
  CheckSquare,
  BookOpen,
  Users,
  Award,
  TrendingUp,
  Play,
  Download,
  MessageSquare,
  RotateCw,
  Target,
  Star,
  Activity,
  Bell,
  ChevronRight,
  GitBranch,
  Plus,
  Eye,
  BarChart3,
  Zap,
  Timer,
  CheckCircle,
  AlertCircle,
  Search,
  Filter,
  CalendarDays,
  GraduationCap,
  FileText,
  UserCheck,
  Shield
} from 'lucide-react'
import { useLanguage } from '../../contexts/LanguageContext'
import { useAuth } from '../../contexts/AuthContext'
import { authService } from '../../config/dataConfig'
import { formatNumber } from '../../utils/numberUtils'
import Button from '../../components/ui/Button'
import toast from 'react-hot-toast'

const AdminDashboard = () => {
  const { t, language } = useLanguage()
  const { user } = useAuth()
  const navigate = useNavigate()
  const [dashboardData, setDashboardData] = useState({
    totalUsers: 0,
    activeCourses: 0,
    totalWorkshops: 0,
    totalTasks: 0,
    pendingSubmissions: 0,
    completionRate: 0,
    upcomingWorkshops: [],
    recentSubmissions: [],
    recentActivities: [],
    systemHealth: 100
  })
  const [loading, setLoading] = useState(true)

  // Load admin data
  useEffect(() => {
    if (user?.name) {
      loadAdminData()
    }
  }, [user])

  // Function to load real data
  const loadAdminData = async () => {
    try {
      setLoading(true)

      // Fetch real users
      const users = await authService.getAllUsers()
      const totalUsers = users.length

      // Fetch real data from localStorage
      const allTasks = JSON.parse(localStorage.getItem('app_tasks') || '[]')
      const allCourses = JSON.parse(localStorage.getItem('app_courses') || '[]')
      const allWorkshops = JSON.parse(localStorage.getItem('app_workshops') || '[]')
      const workshopsData = JSON.parse(localStorage.getItem('workshops_data') || '[]')
      const allSubmissions = JSON.parse(localStorage.getItem('task_submissions') || '[]')

      // Merge workshops from multiple sources and remove duplicates
      const combinedWorkshops = [...allWorkshops, ...workshopsData]
      const uniqueWorkshops = combinedWorkshops.filter((workshop, index, self) =>
        index === self.findIndex(w => w.id === workshop.id)
      )

      // Calculate real statistics
      const activeCourses = allCourses.filter(c => c.status === 'active' || c.status === 'published').length
      const totalTasks = allTasks.filter(t => t.status !== 'deleted' && t.status !== 'archived').length
      const totalWorkshops = uniqueWorkshops.filter(w => w.status !== 'deleted' && w.status !== 'archived').length
      const pendingSubmissions = allSubmissions.filter(s => !s.graded).length
      const completionRate = allTasks.length > 0 ? Math.round((allSubmissions.length / allTasks.length) * 100) : 0

      // Upcoming workshops
      const today = new Date().toISOString().split('T')[0]
      const upcomingWorkshops = uniqueWorkshops
        .filter(w => w.startDate >= today && w.status === 'published')
        .slice(0, 3)

      // Recent submissions
      const recentSubmissions = allSubmissions
        .sort((a, b) => new Date(b.submittedAt) - new Date(a.submittedAt))
        .slice(0, 5)
        .map(sub => {
          const task = allTasks.find(t => t.id === sub.taskId)
          return {
            ...sub,
            taskTitle: task?.title || 'Unknown Task'
          }
        })

      setDashboardData({
        totalUsers,
        activeCourses,
        totalWorkshops,
        totalTasks,
        pendingSubmissions,
        completionRate,
        upcomingWorkshops,
        recentSubmissions,
        recentActivities: [],
        systemHealth: 100
      })

    } catch (error) {
      console.error('Error loading admin data:', error)
    } finally {
      setLoading(false)
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600 dark:text-gray-400">{t('common.loading')}</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">

        {/* Header with Personal Schedule */}
        <div className="mb-8">
          <motion.div
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="bg-white dark:bg-gray-800 rounded-2xl p-6 shadow-sm border border-gray-200 dark:border-gray-700"
          >
            <div className="flex items-center justify-between mb-6">
              <div className="flex items-center space-x-3 rtl:space-x-reverse">
                <div className="p-3 bg-red-100 dark:bg-red-900/30 rounded-xl">
                  <Shield className="w-6 h-6 text-red-600 dark:text-red-400" />
                </div>
                <div>
                  <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                    {t('dashboard.admin.title')}
                  </h1>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    {t('dashboard.welcome')} {user?.name || t('roles.admin')} - {t('dashboard.admin.subtitle')}
                  </p>
                </div>
              </div>
              <div className="text-left rtl:text-right">
                <p className="text-sm text-gray-500 dark:text-gray-400">
                  {new Date().toLocaleDateString('ar-SA', {
                    weekday: 'long',
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric'
                  })}
                </p>
                <div className="flex items-center mt-1">
                  <div className="w-2 h-2 bg-green-500 rounded-full ml-2"></div>
                  <span className="text-xs text-green-600 dark:text-green-400">{t('dashboard.onlineNow')}</span>
                </div>
              </div>
            </div>
          </motion.div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-2 lg:grid-cols-6 gap-4 mb-8">
          {/* Total Users */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.1 }}
            className="bg-white dark:bg-gray-800 rounded-xl p-4 shadow-sm border border-gray-200 dark:border-gray-700 cursor-pointer hover:shadow-md transition-all duration-300"
            onClick={() => navigate('/users')}
          >
            <div className="flex items-center justify-between mb-3">
              <div className="p-2 bg-blue-100 dark:bg-blue-900/30 rounded-lg">
                <Users className="w-6 h-6 sm:w-5 sm:h-5 text-blue-600 dark:text-blue-400" />
              </div>
              <span className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                {dashboardData.totalUsers}
              </span>
            </div>
            <p className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              {t('dashboard.totalUsers')}
            </p>
            <p className="text-xs text-gray-500 dark:text-gray-400">
              {t('dashboard.registeredUsers')}
            </p>
          </motion.div>

          {/* Active Courses */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
            className="bg-white dark:bg-gray-800 rounded-xl p-4 shadow-sm border border-gray-200 dark:border-gray-700 cursor-pointer hover:shadow-md transition-all duration-300"
            onClick={() => navigate('/courses')}
          >
            <div className="flex items-center justify-between mb-3">
              <div className="p-2 bg-green-100 dark:bg-green-900/30 rounded-lg">
                <BookOpen className="w-6 h-6 sm:w-5 sm:h-5 text-green-600 dark:text-green-400" />
              </div>
              <span className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                {dashboardData.activeCourses}
              </span>
            </div>
            <p className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              {t('dashboard.activeCourses')}
            </p>
            <p className="text-xs text-gray-500 dark:text-gray-400">
              {t('dashboard.publishedCourses')}
            </p>
          </motion.div>

          {/* Workshops */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3 }}
            className="bg-white dark:bg-gray-800 rounded-xl p-4 shadow-sm border border-gray-200 dark:border-gray-700 cursor-pointer hover:shadow-md transition-all duration-300"
            onClick={() => navigate('/workshops')}
          >
            <div className="flex items-center justify-between mb-3">
              <div className="p-2 bg-purple-100 dark:bg-purple-900/30 rounded-lg">
                <Calendar className="w-6 h-6 sm:w-5 sm:h-5 text-purple-600 dark:text-purple-400" />
              </div>
              <span className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                {dashboardData.totalWorkshops}
              </span>
            </div>
            <p className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              {t('nav.workshops')}
            </p>
            <p className="text-xs text-gray-500 dark:text-gray-400">
              {t('dashboard.availableWorkshops')}
            </p>
          </motion.div>

          {/* Total Tasks */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4 }}
            className="bg-white dark:bg-gray-800 rounded-xl p-4 shadow-sm border border-gray-200 dark:border-gray-700 cursor-pointer hover:shadow-md transition-all duration-300"
            onClick={() => navigate('/tasks')}
          >
            <div className="flex items-center justify-between mb-3">
              <div className="p-2 bg-orange-100 dark:bg-orange-900/30 rounded-lg">
                <CheckSquare className="w-6 h-6 sm:w-5 sm:h-5 text-orange-600 dark:text-orange-400" />
              </div>
              <span className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                {dashboardData.totalTasks}
              </span>
            </div>
            <p className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              {t('tasks.totalTasks')}
            </p>
            <p className="text-xs text-gray-500 dark:text-gray-400">
              {t('dashboard.publishedTasks')}
            </p>
          </motion.div>

          {/* Pending Tasks */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.5 }}
            className="bg-white dark:bg-gray-800 rounded-xl p-4 shadow-sm border border-gray-200 dark:border-gray-700 cursor-pointer hover:shadow-md transition-all duration-300"
            onClick={() => navigate('/tasks')}
          >
            <div className="flex items-center justify-between mb-3">
              <div className="p-2 bg-red-100 dark:bg-red-900/30 rounded-lg">
                <Timer className="w-6 h-6 sm:w-5 sm:h-5 text-red-600 dark:text-red-400" />
              </div>
              <span className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                {dashboardData.pendingSubmissions}
              </span>
            </div>
            <p className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              {t('dashboard.pendingTasks')}
            </p>
            <p className="text-xs text-gray-500 dark:text-gray-400">
              {t('dashboard.needsGrading')}
            </p>
          </motion.div>

          {/* Timeline */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.6 }}
            className="bg-white dark:bg-gray-800 rounded-xl p-4 shadow-sm border border-gray-200 dark:border-gray-700 cursor-pointer hover:shadow-md transition-all duration-300"
            onClick={() => navigate('/timeline-management')}
          >
            <div className="flex items-center justify-between mb-3">
              <div className="p-2 bg-indigo-100 dark:bg-indigo-900/30 rounded-lg">
                <GitBranch className="w-6 h-6 sm:w-5 sm:h-5 text-indigo-600 dark:text-indigo-400" />
              </div>
              <span className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                {dashboardData.totalEvents || 0}
              </span>
            </div>
            <p className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              {t('nav.timeline') || 'التايم لاين'}
            </p>
            <p className="text-xs text-gray-500 dark:text-gray-400">
              {t('dashboard.timelineEvents') || 'الأحداث والفعاليات'}
            </p>
          </motion.div>
        </div>

        {/* Search and Filter Section */}
        <div className="mb-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.6 }}
            className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700"
          >
            <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                  <input
                    type="text"
                    placeholder={t('dashboard.searchSystem')}
                    className="w-full pr-10 pl-4 py-3 border border-gray-200 dark:border-gray-600 rounded-lg bg-gray-50 dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>
              </div>
              <div className="flex items-center gap-3">
                <select className="px-4 py-3 border border-gray-200 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-blue-500">
                  <option value="all">{t('dashboard.allItems')}</option>
                  <option value="users">{t('nav.users')}</option>
                  <option value="courses">{t('nav.courses')}</option>
                  <option value="workshops">{t('nav.workshops')}</option>
                  <option value="tasks">{t('tasks.title')}</option>
                </select>
                <Button
                  variant="outline"
                  icon={Filter}
                  className="px-4 py-3"
                >
                  {t('common.filter')}
                </Button>
              </div>
            </div>
          </motion.div>
        </div>

        {/* Main Content Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
          {/* Upcoming Workshops */}
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.7 }}
            className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700"
          >
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                {t('dashboard.upcomingWorkshops')}
              </h3>
              <div className="p-2 bg-purple-100 dark:bg-purple-900/30 rounded-lg">
                <Calendar className="w-6 h-6 sm:w-5 sm:h-5 text-purple-600 dark:text-purple-400" />
              </div>
            </div>

            <div className="space-y-4">
              {dashboardData.upcomingWorkshops.length > 0 ? (
                dashboardData.upcomingWorkshops.slice(0, 3).map((workshop, index) => (
                  <div key={index} className="flex items-center p-4 bg-gray-50 dark:bg-gray-700 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors cursor-pointer">
                    <div className="p-2 bg-purple-100 dark:bg-purple-900/30 rounded-lg ml-3">
                      <Calendar className="w-4 h-4 text-purple-600 dark:text-purple-400" />
                    </div>
                    <div className="flex-1">
                      <h4 className="font-medium text-gray-900 dark:text-gray-100 text-sm">
                        {workshop.title || workshop.workshopName}
                      </h4>
                      <p className="text-xs text-gray-500 dark:text-gray-400">
                        {t('workshops.startDate')}: {new Date(workshop.startDate).toLocaleDateString(language === 'ar' ? 'ar-SA' : 'en-US')}
                      </p>
                    </div>
                    <div className="text-xs text-purple-600 dark:text-purple-400 font-medium">
                      {t('dashboard.upcoming')}
                    </div>
                  </div>
                ))
              ) : (
                <div className="text-center py-8">
                  <Calendar className="w-12 h-12 text-gray-400 mx-auto mb-3" />
                  <p className="text-gray-500 dark:text-gray-400">{t('dashboard.noUpcomingWorkshops')}</p>
                </div>
              )}
            </div>

            {dashboardData.upcomingWorkshops.length > 3 && (
              <div className="mt-4 text-center">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => navigate('/workshops')}
                >
                  {t('dashboard.viewAllWorkshops')}
                </Button>
              </div>
            )}
          </motion.div>

          {/* Recent Submissions */}
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.8 }}
            className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700"
          >
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                {t('dashboard.recentSubmissions')}
              </h3>
              <div className="p-2 bg-green-100 dark:bg-green-900/30 rounded-lg">
                <CheckSquare className="w-6 h-6 sm:w-5 sm:h-5 text-green-600 dark:text-green-400" />
              </div>
            </div>

            <div className="space-y-4">
              {dashboardData.recentSubmissions.length > 0 ? (
                dashboardData.recentSubmissions.slice(0, 3).map((submission, index) => (
                  <div key={index} className="flex items-center p-4 bg-gray-50 dark:bg-gray-700 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors cursor-pointer">
                    <div className="p-2 bg-green-100 dark:bg-green-900/30 rounded-lg ml-3">
                      <CheckSquare className="w-4 h-4 text-green-600 dark:text-green-400" />
                    </div>
                    <div className="flex-1">
                      <h4 className="font-medium text-gray-900 dark:text-gray-100 text-sm">
                        {submission.studentName} - {submission.taskTitle}
                      </h4>
                      <p className="text-xs text-gray-500 dark:text-gray-400">
                        {t('tasks.submittedOn')}: {new Date(submission.submittedAt).toLocaleDateString(language === 'ar' ? 'ar-SA' : 'en-US')}
                      </p>
                    </div>
                    <div className="text-xs text-green-600 dark:text-green-400 font-medium">
                      {t('dashboard.new')}
                    </div>
                  </div>
                ))
              ) : (
                <div className="text-center py-8">
                  <CheckSquare className="w-12 h-12 text-gray-400 mx-auto mb-3" />
                  <p className="text-gray-500 dark:text-gray-400">{t('dashboard.noSubmissions')}</p>
                </div>
              )}
            </div>

            {dashboardData.recentSubmissions.length > 3 && (
              <div className="mt-4 text-center">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => navigate('/tasks')}
                >
                  {t('dashboard.viewAllTasks')}
                </Button>
              </div>
            )}
          </motion.div>
        </div>

        {/* Quick Actions */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.9 }}
          className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700"
        >
          <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-6">
            {t('dashboard.quickActions')}
          </h3>
          <div className="grid grid-cols-2 lg:grid-cols-4 gap-4">
            <Button
              onClick={() => navigate('/users')}
              variant="outline"
              icon={Users}
              className="h-20 flex-col"
            >
              <span className="mt-2 text-sm">{t('nav.userManagement')}</span>
            </Button>
            <Button
              onClick={() => navigate('/courses')}
              variant="outline"
              icon={BookOpen}
              className="h-20 flex-col"
            >
              <span className="mt-2 text-sm">{t('nav.courseManagement')}</span>
            </Button>
            <Button
              onClick={() => navigate('/workshops')}
              variant="outline"
              icon={Calendar}
              className="h-20 flex-col"
            >
              <span className="mt-2 text-sm">{t('nav.workshopManagement')}</span>
            </Button>
            <Button
              onClick={() => navigate('/tasks')}
              variant="outline"
              icon={CheckSquare}
              className="h-20 flex-col"
            >
              <span className="mt-2 text-sm">{t('nav.taskManagement')}</span>
            </Button>
          </div>
        </motion.div>
      </div>
    </div>
  )
}

export default AdminDashboard
