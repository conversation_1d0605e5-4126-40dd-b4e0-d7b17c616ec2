import React, { useState, useEffect } from 'react'
import { useNavigate } from 'react-router-dom'
import { motion } from 'framer-motion'
import toast from 'react-hot-toast'
import {
  Plus,
  Search,
  Filter,
  Download,
  Upload,
  Edit,
  Trash2,
  Eye,
  Users,
  Calendar,
  Clock,
  BookOpen,
  User,
  FileText,
  Settings,
  UserPlus,
  PlayCircle
} from 'lucide-react'
import Button from '../../components/ui/Button'
import CreateCourseModal from '../../components/modals/CreateCourseModal'
import { useLanguage } from '../../contexts/LanguageContext'

const CourseManagement = () => {
  const { t } = useLanguage()
  const navigate = useNavigate()
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedFilter, setSelectedFilter] = useState('all')
  const [showCreateModal, setShowCreateModal] = useState(false)
  const [isExporting, setIsExporting] = useState(false)
  const [courses, setCourses] = useState([])

  // تحميل الدورات عند تحميل المكون
  useEffect(() => {
    loadCourses()
  }, [])

  const loadCourses = () => {
    try {
      const savedCourses = JSON.parse(localStorage.getItem('app_courses') || '[]')
      setCourses(savedCourses)
      console.log('تم تحميل الدورات:', savedCourses.length)
    } catch (error) {
      console.error('خطأ في تحميل الدورات:', error)
    }
  }

  const filteredCourses = courses.filter(course => {
    // التحقق من وجود الحقول قبل استخدامها
    const title = course.title || course.courseName || course.name || ''
    const trainer = course.trainer || ''

    const matchesSearch = title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         trainer.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesFilter = selectedFilter === 'all' || course.status === selectedFilter
    return matchesSearch && matchesFilter
  })

  const getStatusColor = (status) => {
    switch (status) {
      case 'active': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
      case 'completed': return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200'
      case 'draft': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'
      case 'archived': return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200'
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200'
    }
  }

  const getCategoryColor = (category) => {
    switch (category) {
      case 'Development': return 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200'
      case 'Design': return 'bg-pink-100 text-pink-800 dark:bg-pink-900 dark:text-pink-200'
      case 'Marketing': return 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200'
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200'
    }
  }

  // Button handlers
  const handleCreateCourse = () => {
    setShowCreateModal(true)
  }

  const handleCourseCreated = (newCourse) => {
    setCourses(prev => [...prev, newCourse])
    setShowCreateModal(false)
    toast.success('تم إنشاء الدورة بنجاح!')
    // إعادة تحميل الدورات للتأكد من التحديث
    setTimeout(() => {
      loadCourses()
    }, 500)
  }

  const handleExportCourses = async () => {
    setIsExporting(true)
    try {
      // استيراد ExcelJS
      const ExcelJS = await import('exceljs')
      const { saveAs } = await import('file-saver')

      // الحصول على ألوان المستخدم المخصصة
      const getUserThemeColors = () => {
        const currentUser = JSON.parse(localStorage.getItem('currentUser') || '{}')
        const userId = currentUser.id || 'default'

        return {
          primary: localStorage.getItem(`primaryColor_${userId}`) || '#4F46E5',
          secondary: localStorage.getItem(`secondaryColor_${userId}`) || '#7C3AED'
        }
      }

      const colors = getUserThemeColors()

      // إنشاء workbook جديد
      const workbook = new ExcelJS.Workbook()
      const worksheet = workbook.addWorksheet('الدورات التدريبية', {
        rightToLeft: true
      })

      // إضافة عنوان رئيسي
      worksheet.mergeCells('A1:J1')
      const titleCell = worksheet.getCell('A1')
      titleCell.value = '📚 تقرير الدورات التدريبية'
      titleCell.font = { size: 16, bold: true, color: { argb: 'FFFFFFFF' } }
      titleCell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: colors.primary.replace('#', 'FF') }
      }
      titleCell.alignment = { horizontal: 'center', vertical: 'middle' }

      // إضافة معلومات التقرير
      worksheet.mergeCells('A2:J2')
      const infoCell = worksheet.getCell('A2')
      infoCell.value = `تاريخ التقرير: ${new Date().toLocaleDateString('ar-SA')} | إجمالي الدورات: ${filteredCourses.length}`
      infoCell.font = { size: 10, italic: true }
      infoCell.alignment = { horizontal: 'center' }

      // رؤوس الأعمدة
      const headers = [
        'اسم الدورة', 'المدرب', 'تاريخ البداية', 'تاريخ النهاية',
        'المدة', 'الحالة', 'عدد المتدربين', 'الحد الأقصى', 'الوصف', 'المتدربين المسجلين'
      ]

      // إضافة رؤوس الأعمدة
      headers.forEach((header, index) => {
        const cell = worksheet.getCell(4, index + 1)
        cell.value = header
        cell.font = { bold: true, color: { argb: 'FFFFFFFF' } }
        cell.fill = {
          type: 'pattern',
          pattern: 'solid',
          fgColor: { argb: colors.secondary.replace('#', 'FF') }
        }
        cell.alignment = { horizontal: 'center', vertical: 'middle' }
        cell.border = {
          top: { style: 'thin' },
          left: { style: 'thin' },
          bottom: { style: 'thin' },
          right: { style: 'thin' }
        }
      })

      // إضافة بيانات الدورات
      filteredCourses.forEach((course, index) => {
        const rowNum = index + 5

        // تحضير قائمة المتدربين
        const traineesList = course.selectedTrainees?.join(', ') || 'لا يوجد متدربين'

        // تحديد حالة الدورة مع رموز
        let statusDisplay = ''
        let statusColor = 'FF000000'
        switch(course.status) {
          case 'active':
            statusDisplay = '🟢 نشطة'
            statusColor = 'FF00AA00'
            break
          case 'completed':
            statusDisplay = '✅ مكتملة'
            statusColor = 'FF0066CC'
            break
          case 'cancelled':
            statusDisplay = '❌ ملغية'
            statusColor = 'FFCC0000'
            break
          case 'pending':
            statusDisplay = '⏳ معلقة'
            statusColor = 'FFFF6600'
            break
          default:
            statusDisplay = '❓ غير محددة'
            statusColor = 'FF666666'
        }

        const rowData = [
          course.title || 'غير محدد',
          course.instructor || 'غير محدد',
          course.startDate || 'غير محدد',
          course.endDate || 'غير محدد',
          course.duration || 'غير محدد',
          statusDisplay,
          course.selectedTrainees?.length || 0,
          course.maxStudents || 'غير محدد',
          course.description || 'لا يوجد وصف',
          traineesList
        ]

        rowData.forEach((value, colIndex) => {
          const cell = worksheet.getCell(rowNum, colIndex + 1)
          cell.value = value

          // تنسيق خاص لعمود الحالة
          if (colIndex === 5) {
            cell.font = { bold: true, color: { argb: statusColor } }
          }

          // حدود للخلايا
          cell.border = {
            top: { style: 'thin' },
            left: { style: 'thin' },
            bottom: { style: 'thin' },
            right: { style: 'thin' }
          }

          // تلوين الصفوف بالتناوب
          if (index % 2 === 0) {
            cell.fill = {
              type: 'pattern',
              pattern: 'solid',
              fgColor: { argb: 'FFF8F9FA' }
            }
          }
        })
      })

      // تحديد عرض الأعمدة
      const columnWidths = [30, 20, 15, 15, 15, 15, 12, 12, 40, 50]
      columnWidths.forEach((width, index) => {
        worksheet.getColumn(index + 1).width = width
      })

      // حفظ الملف
      const buffer = await workbook.xlsx.writeBuffer()
      const blob = new Blob([buffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' })
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-').split('T')[0]
      const fileName = `الدورات_التدريبية_${timestamp}.xlsx`

      saveAs(blob, fileName)

      toast.success(`✅ تم تصدير ${filteredCourses.length} دورة تدريبية بنجاح!`)
      console.log('✅ تم تصدير الدورات بتنسيق متقدم')

    } catch (error) {
      console.error('❌ خطأ في التصدير:', error)
      toast.error('فشل في تصدير الدورات: ' + error.message)
    } finally {
      setIsExporting(false)
    }
  }

  const handleViewCourse = (courseId) => {
    navigate(`/courses/${courseId}`)
  }

  const handleEditCourse = (courseId) => {
    navigate(`/courses/${courseId}/edit`)
  }

  const handleAssignTrainees = (courseId) => {
    navigate(`/courses/${courseId}/assign`)
  }

  const handleDeleteCourse = (courseId) => {
    if (window.confirm('هل أنت متأكد من حذف هذه الدورة؟ سيتم حذف جميع المهام والمواد المرتبطة بها. هذا الإجراء لا يمكن التراجع عنه.')) {
      try {
        // حذف الدورة من app_courses
        const allCourses = JSON.parse(localStorage.getItem('app_courses') || '[]')
        const updatedCourses = allCourses.filter(course => course.id !== courseId)
        localStorage.setItem('app_courses', JSON.stringify(updatedCourses))

        // حذف الدورة من rotations_data إذا كانت روتيشن
        const allRotations = JSON.parse(localStorage.getItem('rotations_data') || '[]')
        const updatedRotations = allRotations.filter(rotation => rotation.id !== courseId)
        localStorage.setItem('rotations_data', JSON.stringify(updatedRotations))

        // حذف المهام المرتبطة بالدورة
        const allTasks = JSON.parse(localStorage.getItem('app_tasks') || '[]')
        const updatedTasks = allTasks.filter(task => task.courseId !== courseId)
        localStorage.setItem('app_tasks', JSON.stringify(updatedTasks))

        // حذف المواد المرتبطة بالدورة
        const allMaterials = JSON.parse(localStorage.getItem('course_materials') || '[]')
        const updatedMaterials = allMaterials.filter(material => material.courseId !== courseId)
        localStorage.setItem('course_materials', JSON.stringify(updatedMaterials))

        // حذف التسليمات المرتبطة بالدورة
        const allSubmissions = JSON.parse(localStorage.getItem('task_submissions') || '[]')
        const courseTaskIds = allTasks.filter(task => task.courseId === courseId).map(task => task.id)
        const updatedSubmissions = allSubmissions.filter(submission => !courseTaskIds.includes(submission.taskId))
        localStorage.setItem('task_submissions', JSON.stringify(updatedSubmissions))

        // إعادة تحميل الدورات
        loadCourses()

        toast.success('تم حذف الدورة وجميع البيانات المرتبطة بها بنجاح!')
        console.log('✅ تم حذف الدورة:', courseId)
      } catch (error) {
        console.error('خطأ في حذف الدورة:', error)
        toast.error('فشل في حذف الدورة')
      }
    }
  }

  const handleManageMaterials = (courseId) => {
    navigate(`/courses/${courseId}/materials`)
  }

  const handleMoreFilters = () => {
    toast.info('More filters - Feature coming soon!')
  }

  // حساب إجمالي المتدربين الفريدين (بدون تكرار)
  const getAllUniqueTrainees = () => {
    // الحصول على جميع المتدربين من النظام
    const allUsers = JSON.parse(localStorage.getItem('users') || '[]')
    const trainees = allUsers.filter(user => user.role === 'trainee')

    console.log('👥 إجمالي المتدربين في النظام:', trainees.length)
    console.log('📋 قائمة المتدربين:', trainees.map(t => t.name))

    return trainees.length
  }

  const stats = [
    { name: t('courses.totalCourses'), value: courses.length, icon: BookOpen, color: 'bg-blue-500' },
    { name: t('courses.activeCourses'), value: courses.filter(c => c.status === 'active').length, icon: PlayCircle, color: 'bg-green-500' },
    { name: t('courses.totalTrainees'), value: getAllUniqueTrainees(), icon: Users, color: 'bg-purple-500' },
    { name: t('courses.completionRate'), value: courses.length > 0 ? Math.round(courses.reduce((sum, c) => sum + (c.progress || 0), 0) / courses.length) + '%' : '0%', icon: Calendar, color: 'bg-orange-500' }
  ]

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100">
            {t('courses.title')}
          </h1>
          <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
            {t('courses.subtitle')}
          </p>
        </div>
        <div className="mt-4 sm:mt-0 flex flex-col sm:flex-row gap-3">
          <Button
            variant="outline"
            onClick={handleExportCourses}
            loading={isExporting}
            icon={Download}
          >
            {t('courses.export')}
          </Button>
          <Button
            variant="primary"
            onClick={handleCreateCourse}
            icon={Plus}
          >
            {t('courses.create')}
          </Button>
        </div>
      </div>

      {/* Stats */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
        {stats.map((stat, index) => {
          const Icon = stat.icon
          return (
            <motion.div
              key={stat.name}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
              className="card"
            >
              <div className="flex items-center">
                <div className={`p-3 rounded-xl ${stat.color}`}>
                  <Icon className="h-6 w-6 text-white" />
                </div>
                <div className="ml-4 rtl:ml-0 rtl:mr-4">
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                    {stat.name}
                  </p>
                  <p className="text-2xl font-semibold text-gray-900 dark:text-gray-100">
                    {stat.value}
                  </p>
                </div>
              </div>
            </motion.div>
          )
        })}
      </div>

      {/* Filters and Search */}
      <div className="card">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0">
          <div className="flex flex-col sm:flex-row gap-4">
            {/* Search */}
            <div className="relative">
              <Search className="absolute left-3 rtl:left-auto rtl:right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <input
                type="text"
                placeholder={t('courses.search')}
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 rtl:pl-4 rtl:pr-10 input-field w-full sm:w-64"
              />
            </div>

            {/* Status Filter */}
            <select
              value={selectedFilter}
              onChange={(e) => setSelectedFilter(e.target.value)}
              className="input-field w-full sm:w-auto"
            >
              <option value="all">{t('courses.allStatus')}</option>
              <option value="active">{t('courses.active')}</option>
              <option value="completed">{t('courses.completed')}</option>
              <option value="draft">{t('courses.draft')}</option>
              <option value="archived">{t('courses.archived')}</option>
            </select>
          </div>

          <div className="flex flex-wrap gap-2">
            <Button
              variant="outline"
              onClick={handleMoreFilters}
              icon={Filter}
            >
              {t('courses.moreFilters')}
            </Button>
          </div>
        </div>
      </div>

      {/* Courses Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6 card-grid">
        {filteredCourses.map((course, index) => (
          <motion.div
            key={course.id}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: index * 0.1 }}
            className="card h-full flex flex-col hover:shadow-soft-lg transition-all duration-200"
          >
            {/* Course Header */}
            <div className="flex items-start justify-between mb-4 flex-grow">
              <div className="flex-1 min-w-0">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2 truncate">
                  {course.title || course.courseName || course.name || 'دورة بدون عنوان'}
                </h3>
                <p className="text-sm text-gray-600 dark:text-gray-400 line-clamp-2 min-h-[2.5rem]">
                  {course.description}
                </p>
              </div>
              <div className="flex flex-wrap gap-1 ml-4 rtl:ml-0 rtl:mr-4">
                <span className={`px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(course.status)}`}>
                  {course.status.charAt(0).toUpperCase() + course.status.slice(1)}
                </span>
                <span className={`px-2 py-1 text-xs font-semibold rounded-full ${getCategoryColor(course.category)}`}>
                  {course.category}
                </span>
              </div>
            </div>

            {/* Course Details */}
            <div className="space-y-3 mb-4">
              {/* تواريخ البداية والنهاية */}
              {(course.startDate || course.endDate) && (
                <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2 text-sm">
                  {course.startDate && (
                    <div className="flex items-center text-gray-500 dark:text-gray-400">
                      <Calendar className="w-4 h-4 mr-2 rtl:mr-0 rtl:ml-2" />
                      {t('courses.start')}: {new Date(course.startDate).toLocaleDateString(language === 'ar' ? 'ar-SA' : 'en-US')}
                    </div>
                  )}
                  {course.endDate && (
                    <div className="flex items-center text-gray-500 dark:text-gray-400">
                      <Calendar className="w-4 h-4 mr-2 rtl:mr-0 rtl:ml-2" />
                      النهاية: {new Date(course.endDate).toLocaleDateString('ar-SA')}
                    </div>
                  )}
                </div>
              )}

              <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2 text-sm">
                <div className="flex items-center text-gray-500 dark:text-gray-400">
                  <Calendar className="w-4 h-4 mr-2 rtl:mr-0 rtl:ml-2" />
                  {t('courses.duration')}: {course.duration?.weeks || 0} {t('common.weeks')}
                </div>
                <div className="flex items-center text-gray-500 dark:text-gray-400">
                  <Clock className="w-4 h-4 mr-2 rtl:mr-0 rtl:ml-2" />
                  {course.duration?.months || 0} {t('common.months')}
                </div>
              </div>

              <div className="flex items-center text-sm">
                <div className="flex items-center text-gray-500 dark:text-gray-400">
                  <User className="w-4 h-4 mr-2 rtl:mr-0 rtl:ml-2" />
                  {t('courses.trainer')}: {course.trainer}
                </div>
              </div>

              {course.courseManager && (
                <div className="flex items-center text-sm text-gray-500 dark:text-gray-400">
                  <Settings className="w-4 h-4 mr-2 rtl:mr-0 rtl:ml-2" />
                  {t('courses.manager')}: {course.courseManager}
                </div>
              )}

              <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2 text-sm">
                <div className="flex items-center text-gray-500 dark:text-gray-400">
                  <Users className="w-4 h-4 mr-2 rtl:mr-0 rtl:ml-2" />
                  {course.selectedTrainees?.length || 0}/{course.maxCapacity || 30} {t('courses.enrolled')}
                </div>
                <div className="flex items-center text-gray-500 dark:text-gray-400">
                  <FileText className="w-4 h-4 mr-2 rtl:mr-0 rtl:ml-2" />
                  {course.materials} {t('courses.materials')}
                </div>
              </div>

              {/* Progress Bar */}
              {course.status === 'active' && (
                <div>
                  <div className="flex justify-between text-xs text-gray-500 dark:text-gray-400 mb-1">
                    <span>{t('courses.progress')}</span>
                    <span>{course.progress}%</span>
                  </div>
                  <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                    <div
                      className="bg-primary-500 h-2 rounded-full transition-all duration-300"
                      style={{ width: `${course.progress}%` }}
                    ></div>
                  </div>
                </div>
              )}
            </div>

            {/* Action Buttons */}
            <div className="flex flex-col sm:flex-row items-stretch sm:items-center justify-between gap-3 pt-4 border-t border-gray-200 dark:border-gray-700 mt-auto">
              <div className="flex flex-wrap gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleViewCourse(course.id)}
                  icon={Eye}
                >
                  {t('common.view')}
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleEditCourse(course.id)}
                  icon={Edit}
                >
                  {t('common.edit')}
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleDeleteCourse(course.id)}
                  className="text-red-600 hover:text-red-700 hover:border-red-300"
                >
                  حذف
                </Button>
              </div>

              <div className="flex flex-wrap gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleAssignTrainees(course.id)}
                  icon={UserPlus}
                >
                  {t('courses.assign')}
                </Button>
                <Button
                  variant="primary"
                  size="sm"
                  onClick={() => handleManageMaterials(course.id)}
                  icon={Upload}
                >
                  {t('courses.materials')}
                </Button>
              </div>
            </div>
          </motion.div>
        ))}
      </div>

      {filteredCourses.length === 0 && (
        <div className="text-center py-12">
          <BookOpen className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-gray-100">{t('courses.noRotations')}</h3>
          <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
            {selectedFilter === 'all' ? t('courses.noRotationsDesc') : `${t('common.no')} ${selectedFilter} ${t('courses.noFilteredRotations')}`}
          </p>
          <div className="mt-6">
            <Button
              variant="primary"
              onClick={handleCreateCourse}
              icon={Plus}
            >
              {t('courses.create')}
            </Button>
          </div>
        </div>
      )}

      {/* Create Course Modal */}
      <CreateCourseModal
        isOpen={showCreateModal}
        onClose={() => setShowCreateModal(false)}
        onCourseCreated={handleCourseCreated}
      />
    </div>
  )
}

export default CourseManagement
