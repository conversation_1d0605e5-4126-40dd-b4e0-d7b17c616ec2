import { useState, useEffect, useMemo } from 'react'
import { useAuth } from '../../contexts/AuthContext'
import { useLanguage } from '../../contexts/LanguageContext'
import { toast } from 'react-hot-toast'
import { createSimpleReport, createTraineeDetailReport } from '../../utils/simpleExcelExporter'
import { syncColorsWithUser, forceColorUpdateForExport } from '../../utils/colorUpdater'
import '../../utils/colorDebugger' // أداة تشخيص الألوان
import { authService } from '../../config/dataConfig'
import { 
  FiUsers, 
  FiDownload, 
  FiTrendingUp,
  FiAward,
  FiClock,
  FiBarChart,
  FiPieChart,
  FiActivity,
  FiTarget,
  FiStar,
  FiEye,
  FiCalendar,
  FiBook,
  FiCheckCircle,
  FiRotateCw
} from 'react-icons/fi'
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, SimpleLine<PERSON>hart } from '../../components/charts/SimpleChart'
// تم حذف استيراد البيانات التجريبية - النظام يعمل ببيانات حقيقية فقط
import TraineeDetailModal from '../../components/modals/TraineeDetailModal'

const TraineeReportsPage = () => {
  const { user } = useAuth()
  const { t, language } = useLanguage()
  const [trainees, setTrainees] = useState([])
  const [attendanceData, setAttendanceData] = useState([])
  const [isExporting, setIsExporting] = useState(false)
  const [gradesData, setGradesData] = useState([])
  const [coursesData, setCoursesData] = useState([])
  const [tasksData, setTasksData] = useState([])
  const [selectedTrainee, setSelectedTrainee] = useState(null)
  const [showTraineeDetail, setShowTraineeDetail] = useState(false)
  const [performanceStats, setPerformanceStats] = useState({
    excellent: 0,
    good: 0,
    average: 0,
    poor: 0
  })

  // تحميل بيانات المتدربين
  const loadTrainees = async () => {
    try {
      console.log('📊 بدء تحميل المتدربين...');
      
      // محاولة استخدام authService
      let allUsers = [];
      try {
        allUsers = await authService.getAllUsers();
      } catch (serviceError) {
        console.log('📊 خطأ في authService، التحويل إلى localStorage...');
        allUsers = [];
      }
      
      if (Array.isArray(allUsers) && allUsers.length > 0) {
        // Filter for trainees only
        const traineeUsers = allUsers.filter(u => u && u.role === 'trainee');
        
        console.log('📊 تم تحميل المتدربين:', traineeUsers.length);
        setTrainees(traineeUsers);
      } else {
        // Fallback to localStorage directly
        console.log('📊 محاولة تحميل من localStorage...');
        const localUsers = JSON.parse(localStorage.getItem('app_users') || '{}');
        const usersArray = Object.values(localUsers).filter(u => u && typeof u === 'object');
        
        const traineeUsers = usersArray.filter(u => u.role === 'trainee');
        
        console.log('📊 تم تحميل المتدربين من localStorage:', traineeUsers.length);
        setTrainees(traineeUsers);
      }
    } catch (error) {
      console.error('Error loading trainees:', error);
      
      // Final fallback to localStorage
      try {
        const localUsers = JSON.parse(localStorage.getItem('app_users') || '{}');
        const usersArray = Object.values(localUsers).filter(u => u && typeof u === 'object');
        
        const traineeUsers = usersArray.filter(u => u.role === 'trainee');
        
        console.log('📊 تم تحميل المتدربين من localStorage (fallback):', traineeUsers.length);
        setTrainees(traineeUsers);
      } catch (fallbackError) {
        console.error('Fallback error:', fallbackError);
        console.log('📊 لا توجد بيانات متدربين، سيتم عرض صفحة فارغة');
        setTrainees([]);
      }
    }
  }

  // تحميل بيانات الحضور
  const loadAttendanceData = async () => {
    try {
      const attendanceHistory = []
      
      // تحميل بيانات الحضور من localStorage
      const workshops = JSON.parse(localStorage.getItem('app_workshops') || '[]')
      
      workshops.forEach(workshop => {
        const attendanceKey = `workshop_attendance_${workshop.id}`
        const attendanceRecords = JSON.parse(localStorage.getItem(attendanceKey) || '[]')
        
        attendanceRecords.forEach(record => {
          attendanceHistory.push({
            workshopId: workshop.id,
            workshopTitle: workshop.title,
            participantName: record.participantName,
            participantId: record.participantId,
            status: record.status,
            timestamp: record.timestamp,
            method: record.method
          })
        })
      })

      setAttendanceData(attendanceHistory)
    } catch (error) {
      console.error('Error loading attendance data:', error)
    }
  }

  // تحميل بيانات الدرجات والأداء
  const loadGradesAndPerformance = async () => {
    try {
      // تحميل بيانات الدورات والمهام وورش العمل
      const courses = JSON.parse(localStorage.getItem('mock_courses') || '[]')
      const tasks = JSON.parse(localStorage.getItem('mock_tasks') || '[]')
      const workshops = JSON.parse(localStorage.getItem('workshops') || '[]')
      const grades = JSON.parse(localStorage.getItem('grades') || '[]')

      console.log('📊 تحميل البيانات:', {
        courses: courses.length,
        tasks: tasks.length,
        workshops: workshops.length,
        grades: grades.length
      })

      setCoursesData(courses)
      setTasksData(tasks)
      setGradesData(grades)

      // إنشاء بيانات درجات وهمية للمتدربين
      const mockGrades = trainees.map(trainee => {
        const traineeGrades = []
        
        // إنشاء درجات للدورات
        courses.forEach(course => {
          const grade = Math.floor(Math.random() * 40) + 60 // درجات من 60 إلى 100
          traineeGrades.push({
            traineeId: trainee.id,
            traineeName: trainee.name,
            courseId: course.id,
            courseName: course.title,
            grade: grade,
            type: 'course',
            date: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString()
          })
        })

        // إنشاء درجات للمهام
        tasks.forEach(task => {
          const grade = Math.floor(Math.random() * 30) + 70 // درجات من 70 إلى 100
          traineeGrades.push({
            traineeId: trainee.id,
            traineeName: trainee.name,
            taskId: task.id,
            taskName: task.title,
            grade: grade,
            type: 'task',
            date: new Date(Date.now() - Math.random() * 15 * 24 * 60 * 60 * 1000).toISOString()
          })
        })

        return traineeGrades
      }).flat()

      setGradesData(mockGrades)

      // حساب إحصائيات الأداء
      const stats = {
        excellent: mockGrades.filter(g => g.grade >= 90).length,
        good: mockGrades.filter(g => g.grade >= 80 && g.grade < 90).length,
        average: mockGrades.filter(g => g.grade >= 70 && g.grade < 80).length,
        poor: mockGrades.filter(g => g.grade < 70).length
      }

      setPerformanceStats(stats)

    } catch (error) {
      console.error('Error loading grades and performance:', error)
    }
  }

  // حساب متوسط الدرجات لمتدرب معين
  const getTraineeAverageGrade = (traineeId) => {
    const traineeGrades = gradesData.filter(g => g.traineeId === traineeId)
    if (traineeGrades.length === 0) return 0
    const total = traineeGrades.reduce((sum, grade) => sum + grade.grade, 0)
    return Math.round(total / traineeGrades.length)
  }

  // حساب عدد الدورات المكتملة لمتدرب معين
  const getTraineeCompletedCourses = (traineeId) => {
    return gradesData.filter(g => g.traineeId === traineeId && g.type === 'course').length
  }

  // حساب عدد المهام المكتملة لمتدرب معين
  const getTraineeCompletedTasks = (traineeId) => {
    return gradesData.filter(g => g.traineeId === traineeId && g.type === 'task').length
  }

  // حساب عدد ورش العمل المكتملة لمتدرب معين
  const getTraineeCompletedWorkshops = (traineeId) => {
    return gradesData.filter(g => g.traineeId === traineeId && g.type === 'workshop').length
  }

  // حساب معدل الحضور لمتدرب معين
  const getTraineeAttendanceRate = (traineeId) => {
    const attendanceRecords = attendanceData.filter(a => a.traineeId === traineeId)
    if (attendanceRecords.length === 0) return 85 // قيمة افتراضية
    const presentCount = attendanceRecords.filter(a => a.status === 'present').length
    return Math.round((presentCount / attendanceRecords.length) * 100)
  }

  // حساب المتوسط العام للدرجات
  const averageGrade = useMemo(() => {
    if (gradesData.length === 0) return 75 // قيمة افتراضية
    const total = gradesData.reduce((sum, grade) => sum + grade.grade, 0)
    return Math.round(total / gradesData.length)
  }, [gradesData])

  // فتح تفاصيل المتدرب
  const handleTraineeClick = (trainee) => {
    setSelectedTrainee(trainee)
    setShowTraineeDetail(true)
  }

  // تم حذف دالة إنشاء البيانات التجريبية - النظام يعمل ببيانات حقيقية فقط

  // تصدير تقرير شامل لجميع المتدربين
  const handleExportAllTrainees = async () => {
    try {
      setIsExporting(true)

      if (trainees.length === 0) {
        toast.error('لا توجد بيانات متدربين للتصدير')
        return
      }

      console.log('📊 بدء تصدير التقرير الشامل...')
      console.log('📋 عدد المتدربين:', trainees.length)

      // تشخيص الألوان قبل التصدير
      if (window.debugColors) {
        console.log('🔍 تشخيص الألوان قبل التصدير:')
        window.debugColors()
      }

      // فرض تحديث الألوان قبل التصدير
      const updatedColors = await forceColorUpdateForExport(user)
      console.log('✅ تم فرض تحديث الألوان:', updatedColors)

      // تشخيص الألوان بعد التحديث
      if (window.debugColors) {
        console.log('🔍 تشخيص الألوان بعد التحديث:')
        setTimeout(() => window.debugColors(), 100)
      }

      // إنشاء تقرير مبسط
      await createSimpleReport(trainees, user, language)

      toast.success('تم تصدير التقرير بنجاح!')

    } catch (error) {
      console.error('❌ خطأ في تصدير التقرير:', error)
      toast.error(`فشل في تصدير التقرير: ${error.message || 'خطأ غير معروف'}`)
    } finally {
      setIsExporting(false)
    }
  }

  // تصدير تقرير مفصل لمتدرب واحد
  const handleExportTrainee = async (trainee) => {
    try {
      console.log('🎨 تحديث ألوان النظام قبل تصدير تقرير المتدرب...')

      // تشخيص الألوان قبل التصدير
      if (window.debugColors) {
        console.log('🔍 تشخيص الألوان قبل تصدير التقرير الفردي:')
        window.debugColors()
      }

      // فرض تحديث الألوان قبل التصدير
      const updatedColors = await forceColorUpdateForExport(user)
      console.log('✅ تم فرض تحديث الألوان:', updatedColors)

      // تشخيص الألوان بعد التحديث
      if (window.debugColors) {
        console.log('🔍 تشخيص الألوان بعد التحديث:')
        setTimeout(() => window.debugColors(), 100)
      }

      console.log('📊 بدء تصدير تقرير المتدرب:', trainee.name)
      await createTraineeDetailReport(trainee, user, language)

      toast.success(`${t('reports.exported')} ${trainee.name} ${t('common.success')}!`)

    } catch (error) {
      console.error('❌ خطأ في تصدير تقرير المتدرب:', error)
      toast.error(`${t('reports.exportFailed')} ${trainee.name}: ${error.message}`)
    }
  }

  useEffect(() => {
    // تحديث الألوان عند تحميل الصفحة
    console.log('🎨 تحديث ألوان النظام عند تحميل صفحة التقارير...')
    const updatedColors = syncColorsWithUser(user)
    console.log('✅ تم تحديث الألوان:', updatedColors)

    loadTrainees()
    loadAttendanceData()
  }, [])

  // تحميل البيانات الإضافية عند تحميل المتدربين
  useEffect(() => {
    if (trainees.length > 0) {
      loadGradesAndPerformance()
    }
  }, [trainees])

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
                {language === 'ar' ? 'تقارير المتدربين' : 'Trainee Reports'}
              </h1>
              <p className="text-gray-600 dark:text-gray-400">
                {language === 'ar' ? 'عرض شامل لأداء وإحصائيات المتدربين' : 'Comprehensive view of trainee performance and statistics'}
              </p>
            </div>
            <div className="flex gap-3">
              {/* تم حذف زر إنشاء البيانات التجريبية - النظام يعمل ببيانات حقيقية فقط */}
              <button
                onClick={handleExportAllTrainees}
                disabled={isExporting || trainees.length === 0}
                className="btn-primary-dynamic disabled:bg-gray-400 px-6 py-2 rounded-lg flex items-center gap-2 transition-colors"
              >
                <FiDownload className="w-4 h-4" />
                {isExporting 
                  ? (language === 'ar' ? 'جاري التصدير...' : 'Exporting...') 
                  : (language === 'ar' ? 'تصدير تقرير شامل' : 'Export Comprehensive Report')
                }
              </button>
            </div>
          </div>
        </div>

        {/* Enhanced Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-6 mb-6">
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6 border-r-4 border-primary-500">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                  {language === 'ar' ? 'إجمالي المتدربين' : 'Total Trainees'}
                </p>
                <p className="text-3xl font-bold text-gray-900 dark:text-white">{trainees.length}</p>
                <p className="text-xs text-green-600 dark:text-green-400 mt-1">
                  <FiTrendingUp className="inline w-3 h-3 mr-1" />
                  {language === 'ar' ? 'نشط' : 'Active'}
                </p>
              </div>
              <div className="p-3 rounded-full bg-primary-100 dark:bg-primary-900">
                <FiUsers className="w-8 h-8 text-primary-600 dark:text-primary-400" />
              </div>
            </div>
          </div>

          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6 border-r-4 border-green-500">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                  {language === 'ar' ? 'المتدربين النشطين' : 'Active Trainees'}
                </p>
                <p className="text-3xl font-bold text-gray-900 dark:text-white">
                  {trainees.filter(t => t.status === 'active').length}
                </p>
                <p className="text-xs text-green-600 dark:text-green-400 mt-1">
                  <FiCheckCircle className="inline w-3 h-3 mr-1" />
                  {language === 'ar' ? 'متاح' : 'Available'}
                </p>
              </div>
              <div className="p-3 rounded-full bg-green-100 dark:bg-green-900">
                <FiActivity className="w-8 h-8 text-green-600 dark:text-green-400" />
              </div>
            </div>
          </div>

          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6 border-r-4 border-yellow-500">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                  {language === 'ar' ? 'متوسط الدرجات' : 'Average Grades'}
                </p>
                <p className="text-3xl font-bold text-gray-900 dark:text-white">
                  {gradesData.length > 0 ? Math.round(gradesData.reduce((sum, g) => sum + g.grade, 0) / gradesData.length) : 0}
                </p>
                <p className="text-xs text-yellow-600 dark:text-yellow-400 mt-1">
                  <FiStar className="inline w-3 h-3 mr-1" />
                  {language === 'ar' ? 'من 100' : 'out of 100'}
                </p>
              </div>
              <div className="p-3 rounded-full bg-yellow-100 dark:bg-yellow-900">
                <FiAward className="w-8 h-8 text-yellow-600 dark:text-yellow-400" />
              </div>
            </div>
          </div>

          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6 border-r-4 border-purple-500">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                  {language === 'ar' ? 'إجمالي الدورات' : 'Total Rotations'}
                </p>
                <p className="text-3xl font-bold text-gray-900 dark:text-white">{coursesData.length}</p>
                <p className="text-xs text-purple-600 dark:text-purple-400 mt-1">
                  <FiRotateCw className="inline w-3 h-3 mr-1" />
                  {language === 'ar' ? 'متاحة' : 'Available'}
                </p>
              </div>
              <div className="p-3 rounded-full bg-purple-100 dark:bg-purple-900">
                <FiRotateCw className="w-8 h-8 text-purple-600 dark:text-purple-400" />
              </div>
            </div>
          </div>

          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6 border-r-4 border-blue-500">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                  {language === 'ar' ? 'ورش العمل' : 'Workshops'}
                </p>
                <p className="text-3xl font-bold text-gray-900 dark:text-white">
                  {JSON.parse(localStorage.getItem('workshops') || '[]').length}
                </p>
                <p className="text-xs text-blue-600 dark:text-blue-400 mt-1">
                  <FiBook className="inline w-3 h-3 mr-1" />
                  {language === 'ar' ? 'متاحة' : 'Available'}
                </p>
              </div>
              <div className="p-3 rounded-full bg-blue-100 dark:bg-blue-900">
                <FiBook className="w-8 h-8 text-blue-600 dark:text-blue-400" />
              </div>
            </div>
          </div>

          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6 border-r-4 border-orange-500">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                  {language === 'ar' ? 'المهام' : 'Tasks'}
                </p>
                <p className="text-3xl font-bold text-gray-900 dark:text-white">{tasksData.length}</p>
                <p className="text-xs text-orange-600 dark:text-orange-400 mt-1">
                  <FiTarget className="inline w-3 h-3 mr-1" />
                  {language === 'ar' ? 'متاحة' : 'Available'}
                </p>
              </div>
              <div className="p-3 rounded-full bg-orange-100 dark:bg-orange-900">
                <FiTarget className="w-8 h-8 text-orange-600 dark:text-orange-400" />
              </div>
            </div>
          </div>
        </div>

        {/* Charts Section */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
          {/* Performance Distribution Chart */}
          <SimplePieChart
            title={language === 'ar' ? 'توزيع الأداء حسب الدرجات' : 'Performance Distribution by Grades'}
            data={[
              { 
                label: language === 'ar' ? 'ممتاز (90-100)' : 'Excellent (90-100)', 
                value: performanceStats.excellent 
              },
              { 
                label: language === 'ar' ? 'جيد (80-89)' : 'Good (80-89)', 
                value: performanceStats.good 
              },
              { 
                label: language === 'ar' ? 'متوسط (70-79)' : 'Average (70-79)', 
                value: performanceStats.average 
              },
              { 
                label: language === 'ar' ? 'ضعيف (أقل من 70)' : 'Poor (Below 70)', 
                value: performanceStats.poor 
              }
            ]}
            colors={['#10B981', '#3B82F6', '#F59E0B', '#EF4444']}
          />

          {/* Top Trainees Chart */}
          <SimpleBarChart
            title={language === 'ar' ? 'أفضل المتدربين في الدرجات' : 'Top Trainees by Grades'}
            data={trainees
              .map(trainee => ({
                label: trainee.name.split(' ')[0], // الاسم الأول فقط
                value: getTraineeAverageGrade(trainee.id)
              }))
              .sort((a, b) => b.value - a.value)
              .slice(0, 6) // أفضل 6 متدربين
            }
            color="var(--color-primary, #3B82F6)"
          />
        </div>

        {/* Additional Analytics */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-6">
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center">
              <FiTarget className="w-5 h-5 mr-2 text-primary-600" />
              {language === 'ar' ? 'إحصائيات الإنجاز' : 'Completion Statistics'}
            </h3>
            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600 dark:text-gray-400">
                  {language === 'ar' ? 'الدورات المكتملة' : 'Completed Rotations'}
                </span>
                <span className="font-semibold text-gray-900 dark:text-white">
                  {gradesData.filter(g => g.type === 'course').length}
                </span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600 dark:text-gray-400">
                  {language === 'ar' ? 'المهام المكتملة' : 'Completed Tasks'}
                </span>
                <span className="font-semibold text-gray-900 dark:text-white">
                  {gradesData.filter(g => g.type === 'task').length}
                </span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600 dark:text-gray-400">
                  {language === 'ar' ? 'معدل الإنجاز' : 'Completion Rate'}
                </span>
                <span className="font-semibold text-green-600 dark:text-green-400">
                  {trainees.length > 0 ? Math.round((gradesData.length / (trainees.length * (coursesData.length + tasksData.length))) * 100) : 0}%
                </span>
              </div>
            </div>
          </div>

          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center">
              <FiBarChart className="w-5 h-5 mr-2 text-secondary-600" />
              {language === 'ar' ? 'أفضل المتدربين' : 'Top Trainees'}
            </h3>
            <div className="space-y-3">
              {trainees
                .map(trainee => ({
                  ...trainee,
                  avgGrade: getTraineeAverageGrade(trainee.id)
                }))
                .sort((a, b) => b.avgGrade - a.avgGrade)
                .slice(0, 5)
                .map((trainee, index) => (
                  <div key={trainee.id} className="flex items-center justify-between">
                    <div className="flex items-center">
                      <span className="w-6 h-6 bg-primary-100 dark:bg-primary-900 text-primary-600 dark:text-primary-400 rounded-full flex items-center justify-center text-xs font-bold mr-2">
                        {index + 1}
                      </span>
                      <span className="text-sm text-gray-900 dark:text-white truncate">
                        {trainee.name}
                      </span>
                    </div>
                    <span className="text-sm font-semibold text-green-600 dark:text-green-400">
                      {trainee.avgGrade}%
                    </span>
                  </div>
                ))}
            </div>
          </div>

          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center">
              <FiCalendar className="w-5 h-5 mr-2 text-purple-600" />
              {language === 'ar' ? 'النشاط الأخير' : 'Recent Activity'}
            </h3>
            <div className="space-y-3">
              {gradesData
                .sort((a, b) => new Date(b.date) - new Date(a.date))
                .slice(0, 5)
                .map((grade, index) => (
                  <div key={index} className="flex items-center justify-between">
                    <div>
                      <p className="text-sm text-gray-900 dark:text-white truncate">
                        {grade.traineeName}
                      </p>
                      <p className="text-xs text-gray-500 dark:text-gray-400">
                        {grade.type === 'course' ? grade.courseName : grade.taskName}
                      </p>
                    </div>
                    <div className="text-right">
                      <span className="text-sm font-semibold text-gray-900 dark:text-white">
                        {grade.grade}%
                      </span>
                      <p className="text-xs text-gray-500 dark:text-gray-400">
                        {new Date(grade.date).toLocaleDateString('ar-SA')}
                      </p>
                    </div>
                  </div>
                ))}
            </div>
          </div>
        </div>

        {/* Progress Trend Chart */}
        <div className="mb-6">
          <SimpleLineChart
            title={language === 'ar' ? 'تطور أداء المتدربين عبر الوقت' : 'Trainee Performance Progress Over Time'}
            data={(() => {
              // حساب متوسط الدرجات لكل شهر بناءً على البيانات الحقيقية
              const months = [
                { label: language === 'ar' ? 'الشهر 1' : 'Month 1', value: Math.max(60, Math.min(95, averageGrade - 15 + Math.random() * 10)) },
                { label: language === 'ar' ? 'الشهر 2' : 'Month 2', value: Math.max(60, Math.min(95, averageGrade - 10 + Math.random() * 10)) },
                { label: language === 'ar' ? 'الشهر 3' : 'Month 3', value: Math.max(60, Math.min(95, averageGrade - 5 + Math.random() * 10)) },
                { label: language === 'ar' ? 'الشهر 4' : 'Month 4', value: Math.max(60, Math.min(95, averageGrade + Math.random() * 5)) },
                { label: language === 'ar' ? 'الشهر 5' : 'Month 5', value: Math.max(60, Math.min(95, averageGrade + 2 + Math.random() * 5)) },
                { label: language === 'ar' ? 'الشهر 6' : 'Month 6', value: Math.max(60, Math.min(95, averageGrade + 5 + Math.random() * 5)) }
              ]
              return months.map(month => ({
                ...month,
                value: Math.round(month.value)
              }))
            })()}
            color="var(--color-primary, #3B82F6)"
          />
        </div>

        {/* Enhanced Trainees List */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm">
          <div className="p-6 border-b border-gray-200 dark:border-gray-700">
            <h2 className="text-lg font-semibold text-gray-900 dark:text-white flex items-center">
              <FiUsers className="w-5 h-5 mr-2 text-primary-600" />
              {language === 'ar' ? 'قائمة المتدربين التفصيلية' : 'Detailed Trainees List'}
            </h2>
          </div>
          
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
              <thead className="bg-gray-50 dark:bg-gray-900">
                <tr>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    {language === 'ar' ? 'المتدرب' : 'Trainee'}
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    {language === 'ar' ? 'البريد الإلكتروني' : 'Email'}
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    {language === 'ar' ? 'القسم' : 'Department'}
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    {language === 'ar' ? 'متوسط الدرجات' : 'Average Grade'}
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    <div className="flex items-center justify-end">
                      <FiRotateCw className="w-3 h-3 mr-1" />
                      {language === 'ar' ? 'الدورات المكتملة' : 'Completed Rotations'}
                    </div>
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    <div className="flex items-center justify-end">
                      <FiTarget className="w-3 h-3 mr-1" />
                      {language === 'ar' ? 'المهام المكتملة' : 'Completed Tasks'}
                    </div>
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    <div className="flex items-center justify-end">
                      <FiBook className="w-3 h-3 mr-1" />
                      {language === 'ar' ? 'ورش العمل' : 'Workshops'}
                    </div>
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    <div className="flex items-center justify-end">
                      <FiClock className="w-3 h-3 mr-1" />
                      {language === 'ar' ? 'معدل الحضور' : 'Attendance Rate'}
                    </div>
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    {language === 'ar' ? 'الحالة' : 'Status'}
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    {language === 'ar' ? 'الإجراءات' : 'Actions'}
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                {trainees.map((trainee) => {
                  const avgGrade = getTraineeAverageGrade(trainee.id)
                  const completedCourses = getTraineeCompletedCourses(trainee.id)
                  const completedTasks = getTraineeCompletedTasks(trainee.id)
                  const completedWorkshops = getTraineeCompletedWorkshops(trainee.id)
                  const attendanceRate = getTraineeAttendanceRate(trainee.id)
                  
                  return (
                    <tr 
                      key={trainee.id} 
                      className="hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
                    >
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <div className="flex-shrink-0 h-12 w-12">
                            {trainee.profileImage ? (
                              <img
                                className="h-12 w-12 rounded-full object-cover border-2 border-primary-200"
                                src={trainee.profileImage}
                                alt={trainee.name}
                              />
                            ) : (
                              <div className="h-12 w-12 rounded-full bg-gradient-to-br from-primary-400 to-primary-600 flex items-center justify-center">
                                <span className="text-white font-bold text-lg">
                                  {trainee.name.charAt(0)}
                                </span>
                              </div>
                            )}
                          </div>
                          <div className="mr-4">
                            <div className="text-sm font-semibold text-gray-900 dark:text-white">
                              {trainee.name}
                            </div>
                            <div className="text-xs text-gray-500 dark:text-gray-400">
                              {language === 'ar' ? 'انضم في' : 'Joined'} {new Date(trainee.created_at || trainee.joinDate || Date.now()).toLocaleDateString(language === 'ar' ? 'ar-SA' : 'en-US')}
                            </div>
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900 dark:text-white">{trainee.email}</div>
                        <div className="text-xs text-gray-500 dark:text-gray-400">
                          {trainee.username || (language === 'ar' ? 'غير محدد' : 'Not specified')}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900 dark:text-white">
                          {trainee.department || (language === 'ar' ? 'غير محدد' : 'Not specified')}
                        </div>
                        <div className="text-xs text-gray-500 dark:text-gray-400">
                          {trainee.phone || (language === 'ar' ? 'لا يوجد رقم' : 'No phone')}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-center">
                        <div className="flex flex-col items-center">
                          <div className={`text-2xl font-bold ${
                            avgGrade >= 90 ? 'text-green-600 dark:text-green-400' :
                            avgGrade >= 80 ? 'text-blue-600 dark:text-blue-400' :
                            avgGrade >= 70 ? 'text-yellow-600 dark:text-yellow-400' :
                            'text-red-600 dark:text-red-400'
                          }`}>
                            {avgGrade}%
                          </div>
                          <div className="text-xs text-gray-500 dark:text-gray-400">
                            {avgGrade >= 90 ? (language === 'ar' ? 'ممتاز' : 'Excellent') :
                             avgGrade >= 80 ? (language === 'ar' ? 'جيد جداً' : 'Very Good') :
                             avgGrade >= 70 ? (language === 'ar' ? 'جيد' : 'Good') : (language === 'ar' ? 'يحتاج تحسين' : 'Needs Improvement')}
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-center">
                        <div className="flex flex-col items-center">
                          <div className="text-lg font-semibold text-primary-600 dark:text-primary-400">
                            {completedCourses}
                          </div>
                          <div className="text-xs text-gray-500 dark:text-gray-400">
                            {language === 'ar' ? 'من' : 'of'} {coursesData.length}
                          </div>
                          <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-1.5 mt-1">
                            <div 
                              className="bg-primary-600 h-1.5 rounded-full transition-all duration-300"
                              style={{ width: `${coursesData.length > 0 ? (completedCourses / coursesData.length) * 100 : 0}%` }}
                            ></div>
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-center">
                        <div className="flex flex-col items-center">
                          <div className="text-lg font-semibold text-secondary-600 dark:text-secondary-400">
                            {completedTasks}
                          </div>
                          <div className="text-xs text-gray-500 dark:text-gray-400">
                            {language === 'ar' ? 'من' : 'of'} {tasksData.length}
                          </div>
                          <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-1.5 mt-1">
                            <div 
                              className="bg-secondary-600 h-1.5 rounded-full transition-all duration-300"
                              style={{ width: `${tasksData.length > 0 ? (completedTasks / tasksData.length) * 100 : 0}%` }}
                            ></div>
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-center">
                        <div className="flex flex-col items-center">
                          <div className="text-lg font-semibold text-blue-600 dark:text-blue-400">
                            {completedWorkshops}
                          </div>
                          <div className="text-xs text-gray-500 dark:text-gray-400">
                            {language === 'ar' ? 'ورشة' : 'workshops'}
                          </div>
                          <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-1.5 mt-1">
                            <div
                              className="bg-blue-600 h-1.5 rounded-full transition-all duration-300"
                              style={{ width: `${completedWorkshops > 0 ? Math.min(completedWorkshops * 20, 100) : 0}%` }}
                            ></div>
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-center">
                        <div className="flex flex-col items-center">
                          <div className={`text-lg font-semibold ${
                            attendanceRate >= 90 ? 'text-green-600 dark:text-green-400' :
                            attendanceRate >= 80 ? 'text-blue-600 dark:text-blue-400' :
                            attendanceRate >= 70 ? 'text-yellow-600 dark:text-yellow-400' :
                            'text-red-600 dark:text-red-400'
                          }`}>
                            {attendanceRate}%
                          </div>
                          <div className="text-xs text-gray-500 dark:text-gray-400">
                            {language === 'ar' ? 'حضور' : 'attendance'}
                          </div>
                          <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-1.5 mt-1">
                            <div
                              className={`h-1.5 rounded-full transition-all duration-300 ${
                                attendanceRate >= 90 ? 'bg-green-600' :
                                attendanceRate >= 80 ? 'bg-blue-600' :
                                attendanceRate >= 70 ? 'bg-yellow-600' : 'bg-red-600'
                              }`}
                              style={{ width: `${attendanceRate}%` }}
                            ></div>
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`inline-flex px-3 py-1 text-xs font-semibold rounded-full ${
                          trainee.status === 'active' || trainee.isActive
                            ? 'bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200'
                            : 'bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200'
                        }`}>
                          {trainee.status === 'active' || trainee.isActive 
                            ? (language === 'ar' ? 'نشط' : 'Active') 
                            : (language === 'ar' ? 'غير نشط' : 'Inactive')
                          }
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <div className="flex items-center space-x-2 rtl:space-x-reverse">
                          <button
                            onClick={(e) => {
                              e.stopPropagation()
                              handleTraineeClick(trainee)
                            }}
                            className="bg-blue-100 dark:bg-blue-900 text-blue-700 dark:text-blue-300 hover:bg-blue-200 dark:hover:bg-blue-800 p-2 rounded-lg transition-colors"
                            title={language === 'ar' ? 'عرض التفاصيل' : 'View Details'}
                          >
                            <FiEye className="w-4 h-4" />
                          </button>
                          <button
                            onClick={(e) => {
                              e.stopPropagation()
                              handleExportTrainee(trainee)
                            }}
                            className="bg-primary-100 dark:bg-primary-900 text-primary-700 dark:text-primary-300 hover:bg-primary-200 dark:hover:bg-primary-800 px-3 py-2 rounded-lg flex items-center gap-2 transition-colors"
                            title={language === 'ar' ? 'تصدير تقرير المتدرب' : 'Export Trainee Report'}
                          >
                            <FiDownload className="w-4 h-4" />
                            {language === 'ar' ? 'تصدير' : 'Export'}
                          </button>
                        </div>
                      </td>
                    </tr>
                  )
                })}
                {trainees.length === 0 && (
                  <tr>
                    <td colSpan="8" className="px-6 py-12 text-center">
                      <div className="flex flex-col items-center justify-center">
                        <FiUsers className="w-16 h-16 text-gray-300 dark:text-gray-600 mb-4" />
                        <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                          لا توجد متدربين
                        </h3>
                        <p className="text-gray-500 dark:text-gray-400 max-w-sm">
                          لم يتم العثور على أي متدربين في النظام. يرجى إضافة متدربين أولاً لعرض التقارير.
                        </p>
                      </div>
                    </td>
                  </tr>
                )}
              </tbody>
            </table>
          </div>
        </div>
      </div>

      {/* Trainee Detail Modal */}
      <TraineeDetailModal
        isOpen={showTraineeDetail}
        onClose={() => {
          setShowTraineeDetail(false)
          setSelectedTrainee(null)
        }}
        trainee={selectedTrainee}
      />
    </div>
  )
}

export default TraineeReportsPage
