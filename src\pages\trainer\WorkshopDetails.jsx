import React, { useState, useEffect } from 'react'
import { useParams, useNavigate } from 'react-router-dom'
import { motion } from 'framer-motion'
import { toast } from 'react-hot-toast'
import {
  ArrowLeft,
  Users,
  Calendar,
  Clock,
  BookOpen,
  Plus,
  Edit,
  Trash2,
  Upload,
  Download,
  Video,
  FileText,
  Image,
  Star,
  Award,
  Target,
  TrendingUp,
  Save,
  Eye,
  Settings,
  MessageSquare,
  HelpCircle
} from 'lucide-react'
import Button from '../../components/ui/Button'
import WorkshopChat from '../../components/workshop/WorkshopChat'
import QuizManager from '../../components/workshop/QuizManager'
import GroupManager from '../../components/workshop/GroupManager'
import ContentManager from '../../components/workshop/ContentManager'
import { useAuth } from '../../contexts/AuthContext'
import { useLanguage } from '../../contexts/LanguageContext'
import dataStorage from '../../utils/dataStorage'

const WorkshopDetails = () => {
  const { workshopId } = useParams()
  const navigate = useNavigate()
  const { user } = useAuth()
  const { t, language } = useLanguage()
  const [workshop, setWorkshop] = useState(null)
  const [activeTab, setActiveTab] = useState('overview')
  const [loading, setLoading] = useState(true)
  const [materials, setMaterials] = useState([])
  const [groups, setGroups] = useState([])
  const [showAddMaterial, setShowAddMaterial] = useState(false)
  const [showAddGroup, setShowAddGroup] = useState(false)
  const [chatEnabled, setChatEnabled] = useState(false)

  useEffect(() => {
    loadWorkshopDetails()
  }, [workshopId])

  const loadWorkshopDetails = () => {
    try {
      setLoading(true)
      const allWorkshops = dataStorage.loadWorkshops()
      const foundWorkshop = allWorkshops.find(w => w.id === workshopId)
      
      if (!foundWorkshop) {
        toast.error('ورشة العمل غير موجودة')
        navigate('/trainer-workshops')
        return
      }

      // التحقق من أن المدرب يملك هذه الورشة
      if (foundWorkshop.trainer !== user.name && foundWorkshop.trainerId !== user.id) {
        toast.error('ليس لديك صلاحية للوصول إلى هذه الورشة')
        navigate('/trainer-workshops')
        return
      }

      setWorkshop(foundWorkshop)
      
      // تحميل المواد والمجموعات
      const workshopMaterials = JSON.parse(localStorage.getItem(`workshop_materials_${workshopId}`) || '[]')
      const workshopGroups = JSON.parse(localStorage.getItem(`workshop_groups_${workshopId}`) || '[]')

      // تحميل حالة المحادثة
      const chatSettings = JSON.parse(localStorage.getItem(`workshop_chat_settings_${workshopId}`) || '{}')
      setChatEnabled(chatSettings.enabled || false)

      setMaterials(workshopMaterials)
      setGroups(workshopGroups)
    } catch (error) {
      console.error('Error loading workshop details:', error)
      toast.error('فشل في تحميل تفاصيل ورشة العمل')
    } finally {
      setLoading(false)
    }
  }

  const addMaterial = (materialData) => {
    const newMaterial = {
      id: `material_${Date.now()}`,
      ...materialData,
      uploadedAt: new Date().toISOString(),
      uploadedBy: user.name
    }
    
    const updatedMaterials = [...materials, newMaterial]
    setMaterials(updatedMaterials)
    localStorage.setItem(`workshop_materials_${workshopId}`, JSON.stringify(updatedMaterials))
    toast.success('تم إضافة المادة بنجاح')
  }

  const addGroup = (groupData) => {
    const newGroup = {
      id: `group_${Date.now()}`,
      ...groupData,
      createdAt: new Date().toISOString(),
      points: 0,
      activities: []
    }
    
    const updatedGroups = [...groups, newGroup]
    setGroups(updatedGroups)
    localStorage.setItem(`workshop_groups_${workshopId}`, JSON.stringify(updatedGroups))
    toast.success('تم إضافة المجموعة بنجاح')
  }

  const updateGroupPoints = (groupId, points, reason) => {
    const updatedGroups = groups.map(group => {
      if (group.id === groupId) {
        const newActivity = {
          id: `activity_${Date.now()}`,
          points: parseInt(points),
          reason,
          timestamp: new Date().toISOString(),
          addedBy: user.name
        }
        
        return {
          ...group,
          points: group.points + parseInt(points),
          activities: [...group.activities, newActivity]
        }
      }
      return group
    })
    
    setGroups(updatedGroups)
    localStorage.setItem(`workshop_groups_${workshopId}`, JSON.stringify(updatedGroups))
    toast.success('تم تحديث النقاط بنجاح')
  }

  const toggleChat = () => {
    const newChatEnabled = !chatEnabled
    setChatEnabled(newChatEnabled)

    // حفظ إعدادات المحادثة
    const chatSettings = { enabled: newChatEnabled }
    localStorage.setItem(`workshop_chat_settings_${workshopId}`, JSON.stringify(chatSettings))

    toast.success(newChatEnabled ? 'تم تفعيل المحادثة العامة' : 'تم إلغاء تفعيل المحادثة العامة')
  }

  const tabs = [
    { id: 'overview', name: 'نظرة عامة', icon: Eye },
    { id: 'content', name: 'المحتوى والمواد', icon: BookOpen },
    { id: 'groups', name: 'المجموعات والنقاط', icon: Users },
    { id: 'quizzes', name: 'الاختبارات والأسئلة', icon: HelpCircle },
    { id: 'chat', name: 'المحادثة العامة', icon: MessageSquare },
    { id: 'settings', name: 'الإعدادات', icon: Settings }
  ]

  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
        <span className="ml-2 text-gray-600 dark:text-gray-400">جاري تحميل تفاصيل ورشة العمل...</span>
      </div>
    )
  }

  if (!workshop) {
    return (
      <div className="text-center py-12">
        <BookOpen className="mx-auto h-12 w-12 text-gray-400" />
        <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-gray-100">
          ورشة العمل غير موجودة
        </h3>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4 rtl:space-x-reverse">
          <Button
            variant="outline"
            onClick={() => navigate('/trainer-workshops')}
            icon={ArrowLeft}
            size="sm"
          >
            العودة
          </Button>
          <div>
            <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100">
              {workshop.title}
            </h1>
            <p className="text-sm text-gray-500 dark:text-gray-400">
              إدارة ورشة العمل والمحتوى
            </p>
          </div>
        </div>
        <div className="flex space-x-2 rtl:space-x-reverse">
          <Button variant="outline" size="sm">
            <Edit className="w-4 h-4 mr-2" />
            تعديل الورشة
          </Button>
        </div>
      </div>

      {/* Workshop Info Card */}
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div className="flex items-center">
            <Calendar className="w-5 h-5 text-blue-600 mr-2" />
            <div>
              <p className="text-sm text-gray-500 dark:text-gray-400">تاريخ البداية</p>
              <p className="font-medium text-gray-900 dark:text-gray-100">{workshop.startDate}</p>
            </div>
          </div>
          <div className="flex items-center">
            <Clock className="w-5 h-5 text-green-600 mr-2" />
            <div>
              <p className="text-sm text-gray-500 dark:text-gray-400">الوقت والمدة</p>
              <p className="font-medium text-gray-900 dark:text-gray-100">{workshop.startTime} - {workshop.duration} دقيقة</p>
            </div>
          </div>
          <div className="flex items-center">
            <Users className="w-5 h-5 text-purple-600 mr-2" />
            <div>
              <p className="text-sm text-gray-500 dark:text-gray-400">المشاركين</p>
              <p className="font-medium text-gray-900 dark:text-gray-100">{workshop.selectedTrainees?.length || 0} متدرب</p>
            </div>
          </div>
          <div className="flex items-center">
            <BookOpen className="w-5 h-5 text-orange-600 mr-2" />
            <div>
              <p className="text-sm text-gray-500 dark:text-gray-400">المواد</p>
              <p className="font-medium text-gray-900 dark:text-gray-100">{materials.length} مادة</p>
            </div>
          </div>
        </div>
      </div>

      {/* Tabs */}
      <div className="border-b border-gray-200 dark:border-gray-700">
        <nav className="-mb-px flex space-x-8 rtl:space-x-reverse">
          {tabs.map((tab) => {
            const Icon = tab.icon
            return (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`flex items-center py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === tab.id
                    ? 'border-primary-500 text-primary-600 dark:text-primary-400'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300'
                }`}
              >
                <Icon className="w-4 h-4 mr-2" />
                {tab.name}
              </button>
            )
          })}
        </nav>
      </div>

      {/* Tab Content */}
      <div className="mt-6">
        {activeTab === 'overview' && (
          <OverviewTab workshop={workshop} materials={materials} groups={groups} />
        )}
        {activeTab === 'content' && (
          <ContentManager
            workshopId={workshopId}
            workshopTitle={workshop.title}
          />
        )}
        {activeTab === 'groups' && (
          <GroupManager
            workshopId={workshopId}
            workshopTitle={workshop.title}
            participants={workshop.selectedTrainees || []}
          />
        )}
        {activeTab === 'quizzes' && (
          <QuizManager
            workshopId={workshopId}
            groups={groups}
          />
        )}
        {activeTab === 'chat' && (
          <WorkshopChat
            workshopId={workshopId}
            isEnabled={chatEnabled}
            onToggleChat={toggleChat}
          />
        )}
        {activeTab === 'settings' && (
          <SettingsTab workshop={workshop} />
        )}
      </div>
    </div>
  )
}

// Overview Tab Component
const OverviewTab = ({ workshop, materials, groups }) => {
  const totalPoints = groups.reduce((sum, group) => sum + group.points, 0)
  const avgPoints = groups.length > 0 ? Math.round(totalPoints / groups.length) : 0

  return (
    <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
      {/* Description */}
      <div className="lg:col-span-2 bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">
          {t('workshops.workshopDescription') || 'وصف ورشة العمل'}
        </h3>
        <p className="text-gray-600 dark:text-gray-400 leading-relaxed">
          {workshop.description || t('workshops.noDescriptionAvailable') || 'لا يوجد وصف متاح لهذه الورشة.'}
        </p>
      </div>

      {/* Quick Stats */}
      <div className="space-y-4">
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">
            إحصائيات سريعة
          </h3>
          <div className="space-y-3">
            <div className="flex justify-between">
              <span className="text-gray-600 dark:text-gray-400">المواد التعليمية</span>
              <span className="font-medium text-gray-900 dark:text-gray-100">{materials.length}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600 dark:text-gray-400">المجموعات</span>
              <span className="font-medium text-gray-900 dark:text-gray-100">{groups.length}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600 dark:text-gray-400">إجمالي النقاط</span>
              <span className="font-medium text-gray-900 dark:text-gray-100">{totalPoints}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600 dark:text-gray-400">متوسط النقاط</span>
              <span className="font-medium text-gray-900 dark:text-gray-100">{avgPoints}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

// Materials Tab Component
const MaterialsTab = ({ materials, onAddMaterial, workshopId }) => {
  const [showAddForm, setShowAddForm] = useState(false)
  const [newMaterial, setNewMaterial] = useState({
    title: '',
    type: 'document',
    description: '',
    url: '',
    file: null
  })

  const handleAddMaterial = () => {
    if (!newMaterial.title.trim()) {
      toast.error('يرجى إدخال عنوان المادة')
      return
    }

    if (newMaterial.type === 'link' && !newMaterial.url.trim()) {
      toast.error('يرجى إدخال الرابط')
      return
    }

    if (newMaterial.type !== 'link' && !newMaterial.file) {
      toast.error('يرجى اختيار ملف للرفع')
      return
    }

    onAddMaterial(newMaterial)
    setNewMaterial({ title: '', type: 'document', description: '', url: '', file: null, fileName: '', fileSize: 0 })
    setShowAddForm(false)
  }

  const materialTypes = [
    { value: 'document', label: 'مستند', icon: FileText },
    { value: 'video', label: 'فيديو', icon: Video },
    { value: 'image', label: 'صورة', icon: Image },
    { value: 'link', label: 'رابط', icon: BookOpen }
  ]

  return (
    <div className="space-y-6">
      {/* Add Material Button */}
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
          المواد التعليمية ({materials.length})
        </h3>
        <Button
          variant="primary"
          onClick={() => setShowAddForm(true)}
          icon={Plus}
          size="sm"
        >
          إضافة مادة جديدة
        </Button>
      </div>

      {/* Add Material Form */}
      {showAddForm && (
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
          <h4 className="text-md font-medium text-gray-900 dark:text-gray-100 mb-4">
            إضافة مادة تعليمية جديدة
          </h4>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                عنوان المادة
              </label>
              <input
                type="text"
                value={newMaterial.title}
                onChange={(e) => setNewMaterial(prev => ({ ...prev, title: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100"
                placeholder="أدخل عنوان المادة"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                نوع المادة
              </label>
              <select
                value={newMaterial.type}
                onChange={(e) => setNewMaterial(prev => ({ ...prev, type: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100"
              >
                {materialTypes.map(type => (
                  <option key={type.value} value={type.value}>{type.label}</option>
                ))}
              </select>
            </div>
            <div className="md:col-span-2">
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                الوصف
              </label>
              <textarea
                value={newMaterial.description}
                onChange={(e) => setNewMaterial(prev => ({ ...prev, description: e.target.value }))}
                rows={3}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100"
                placeholder="وصف المادة التعليمية"
              />
            </div>
            {newMaterial.type === 'link' && (
              <div className="md:col-span-2">
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  الرابط
                </label>
                <input
                  type="url"
                  value={newMaterial.url}
                  onChange={(e) => setNewMaterial(prev => ({ ...prev, url: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100"
                  placeholder="https://example.com"
                />
              </div>
            )}
            {newMaterial.type !== 'link' && (
              <div className="md:col-span-2">
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  رفع ملف
                </label>
                <div className="border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg p-6 text-center">
                  <Upload className="mx-auto h-8 w-8 text-gray-400 mb-2" />
                  <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">
                    اسحب الملف هنا أو انقر للاختيار
                  </p>
                  <input
                    type="file"
                    onChange={(e) => {
                      const file = e.target.files[0]
                      if (file) {
                        // محاكاة رفع الملف
                        const fileUrl = URL.createObjectURL(file)
                        setNewMaterial(prev => ({
                          ...prev,
                          file: file,
                          url: fileUrl,
                          fileName: file.name,
                          fileSize: file.size
                        }))
                        toast.success('تم اختيار الملف بنجاح')
                      }
                    }}
                    className="hidden"
                    id="file-upload"
                    accept={
                      newMaterial.type === 'document' ? '.pdf,.doc,.docx,.txt' :
                      newMaterial.type === 'video' ? '.mp4,.avi,.mov,.wmv' :
                      newMaterial.type === 'image' ? '.jpg,.jpeg,.png,.gif' : '*'
                    }
                  />
                  <label
                    htmlFor="file-upload"
                    className="cursor-pointer inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-primary-700 bg-primary-100 hover:bg-primary-200 dark:bg-primary-900 dark:text-primary-300 dark:hover:bg-primary-800"
                  >
                    اختيار ملف
                  </label>
                  {newMaterial.fileName && (
                    <p className="mt-2 text-sm text-green-600 dark:text-green-400">
                      تم اختيار: {newMaterial.fileName}
                    </p>
                  )}
                </div>
              </div>
            )}
          </div>
          <div className="flex justify-end space-x-3 rtl:space-x-reverse mt-4">
            <Button
              variant="outline"
              onClick={() => setShowAddForm(false)}
              size="sm"
            >
              إلغاء
            </Button>
            <Button
              variant="primary"
              onClick={handleAddMaterial}
              size="sm"
              icon={Save}
            >
              حفظ المادة
            </Button>
          </div>
        </div>
      )}

      {/* Materials List */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {materials.map((material) => {
          const MaterialIcon = materialTypes.find(t => t.value === material.type)?.icon || FileText
          return (
            <div
              key={material.id}
              className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4"
            >
              <div className="flex items-start justify-between mb-3">
                <div className="flex items-center">
                  <MaterialIcon className="w-5 h-5 text-blue-600 mr-2" />
                  <h4 className="font-medium text-gray-900 dark:text-gray-100 text-sm">
                    {material.title}
                  </h4>
                </div>
              </div>
              <p className="text-xs text-gray-600 dark:text-gray-400 mb-3">
                {material.description}
              </p>
              <div className="flex justify-between items-center text-xs text-gray-500 dark:text-gray-400">
                <span>{t('common.uploaded')}: {new Date(material.uploadedAt).toLocaleDateString(language === 'ar' ? 'ar-SA' : 'en-US')}</span>
                <div className="flex space-x-2 rtl:space-x-reverse">
                  <button className="text-blue-600 hover:text-blue-800">
                    <Eye className="w-4 h-4" />
                  </button>
                  <button className="text-red-600 hover:text-red-800">
                    <Trash2 className="w-4 h-4" />
                  </button>
                </div>
              </div>
            </div>
          )
        })}
      </div>

      {materials.length === 0 && (
        <div className="text-center py-12">
          <BookOpen className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-gray-100">
            لا توجد مواد تعليمية
          </h3>
          <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
            ابدأ بإضافة المواد التعليمية لورشة العمل
          </p>
        </div>
      )}
    </div>
  )
}

// Groups Tab Component
const GroupsTab = ({ groups, onAddGroup, onUpdatePoints, workshop }) => {
  const [showAddForm, setShowAddForm] = useState(false)
  const [showPointsModal, setShowPointsModal] = useState(false)
  const [selectedGroup, setSelectedGroup] = useState(null)
  const [newGroup, setNewGroup] = useState({
    name: '',
    members: [],
    color: '#3B82F6'
  })
  const [pointsData, setPointsData] = useState({
    points: '',
    reason: ''
  })

  const handleAddGroup = () => {
    if (!newGroup.name.trim()) {
      toast.error('يرجى إدخال اسم المجموعة')
      return
    }

    onAddGroup(newGroup)
    setNewGroup({ name: '', members: [], color: '#3B82F6' })
    setShowAddForm(false)
  }

  const handleAddPoints = () => {
    if (!pointsData.points || !pointsData.reason.trim()) {
      toast.error('يرجى إدخال النقاط والسبب')
      return
    }

    onUpdatePoints(selectedGroup.id, pointsData.points, pointsData.reason)
    setPointsData({ points: '', reason: '' })
    setShowPointsModal(false)
    setSelectedGroup(null)
  }

  const groupColors = [
    '#3B82F6', '#EF4444', '#10B981', '#F59E0B',
    '#8B5CF6', '#EC4899', '#06B6D4', '#84CC16'
  ]

  // تحديد أعضاء المجموعة من المتدربين المسجلين
  const availableTrainees = workshop.selectedTrainees || []

  const generateRandomGroups = () => {
    if (availableTrainees.length === 0) {
      toast.error('لا يوجد متدربين مسجلين في هذه الورشة')
      return
    }

    const groupSize = Math.ceil(availableTrainees.length / 4) // تقسيم إلى 4 مجموعات كحد أقصى
    const shuffledTrainees = [...availableTrainees].sort(() => Math.random() - 0.5)
    const newGroups = []

    for (let i = 0; i < Math.min(4, Math.ceil(availableTrainees.length / groupSize)); i++) {
      const groupMembers = shuffledTrainees.slice(i * groupSize, (i + 1) * groupSize)
      if (groupMembers.length > 0) {
        const newGroup = {
          id: `group_${Date.now()}_${i}`,
          name: `المجموعة ${i + 1}`,
          members: groupMembers,
          color: groupColors[i % groupColors.length],
          createdAt: new Date().toISOString(),
          points: 0,
          activities: []
        }
        newGroups.push(newGroup)
      }
    }

    // إضافة المجموعات الجديدة
    newGroups.forEach(group => onAddGroup(group))
    toast.success(`تم إنشاء ${newGroups.length} مجموعة بنجاح`)
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
          المجموعات والنقاط ({groups.length})
        </h3>
        <div className="flex space-x-2 rtl:space-x-reverse">
          <Button
            variant="outline"
            onClick={() => generateRandomGroups()}
            icon={Users}
            size="sm"
            disabled={availableTrainees.length === 0}
          >
            توليد مجموعات تلقائياً
          </Button>
          <Button
            variant="primary"
            onClick={() => setShowAddForm(true)}
            icon={Plus}
            size="sm"
          >
            إضافة مجموعة جديدة
          </Button>
        </div>
      </div>

      {/* Add Group Form */}
      {showAddForm && (
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
          <h4 className="text-md font-medium text-gray-900 dark:text-gray-100 mb-4">
            إضافة مجموعة جديدة
          </h4>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                اسم المجموعة
              </label>
              <input
                type="text"
                value={newGroup.name}
                onChange={(e) => setNewGroup(prev => ({ ...prev, name: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100"
                placeholder="مثال: المجموعة الأولى"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                لون المجموعة
              </label>
              <div className="flex space-x-2 rtl:space-x-reverse">
                {groupColors.map(color => (
                  <button
                    key={color}
                    onClick={() => setNewGroup(prev => ({ ...prev, color }))}
                    className={`w-8 h-8 rounded-full border-2 ${
                      newGroup.color === color ? 'border-gray-900 dark:border-gray-100' : 'border-gray-300'
                    }`}
                    style={{ backgroundColor: color }}
                  />
                ))}
              </div>
            </div>
            <div className="md:col-span-2">
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                أعضاء المجموعة
              </label>
              <div className="grid grid-cols-2 md:grid-cols-3 gap-2 max-h-32 overflow-y-auto">
                {availableTrainees.map(trainee => (
                  <label key={trainee.id} className="flex items-center">
                    <input
                      type="checkbox"
                      checked={newGroup.members.some(m => m.id === trainee.id)}
                      onChange={(e) => {
                        if (e.target.checked) {
                          setNewGroup(prev => ({
                            ...prev,
                            members: [...prev.members, trainee]
                          }))
                        } else {
                          setNewGroup(prev => ({
                            ...prev,
                            members: prev.members.filter(m => m.id !== trainee.id)
                          }))
                        }
                      }}
                      className="mr-2"
                    />
                    <span className="text-sm text-gray-700 dark:text-gray-300">
                      {trainee.name}
                    </span>
                  </label>
                ))}
              </div>
            </div>
          </div>
          <div className="flex justify-end space-x-3 rtl:space-x-reverse mt-4">
            <Button
              variant="outline"
              onClick={() => setShowAddForm(false)}
              size="sm"
            >
              إلغاء
            </Button>
            <Button
              variant="primary"
              onClick={handleAddGroup}
              size="sm"
              icon={Save}
            >
              حفظ المجموعة
            </Button>
          </div>
        </div>
      )}

      {/* Groups List */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {groups.map((group) => (
          <div
            key={group.id}
            className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden"
          >
            {/* Group Header */}
            <div
              className="p-4 text-white"
              style={{ backgroundColor: group.color }}
            >
              <div className="flex items-center justify-between">
                <h4 className="font-semibold">{group.name}</h4>
                <div className="flex items-center">
                  <Star className="w-4 h-4 mr-1" />
                  <span className="font-bold">{group.points}</span>
                </div>
              </div>
              <p className="text-sm opacity-90">
                {group.members.length} عضو
              </p>
            </div>

            {/* Group Content */}
            <div className="p-4">
              {/* Members */}
              <div className="mb-4">
                <h5 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  الأعضاء:
                </h5>
                <div className="flex flex-wrap gap-1">
                  {group.members.map(member => (
                    <span
                      key={member.id}
                      className="px-2 py-1 bg-gray-100 dark:bg-gray-700 text-xs rounded-full text-gray-700 dark:text-gray-300"
                    >
                      {member.name}
                    </span>
                  ))}
                </div>
              </div>

              {/* Recent Activities */}
              <div className="mb-4">
                <h5 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  آخر الأنشطة:
                </h5>
                <div className="space-y-1 max-h-20 overflow-y-auto">
                  {group.activities.slice(-3).map(activity => (
                    <div key={activity.id} className="text-xs text-gray-600 dark:text-gray-400">
                      <span className="font-medium">+{activity.points}</span> - {activity.reason}
                    </div>
                  ))}
                  {group.activities.length === 0 && (
                    <p className="text-xs text-gray-500 dark:text-gray-400">لا توجد أنشطة بعد</p>
                  )}
                </div>
              </div>

              {/* Actions */}
              <div className="flex space-x-2 rtl:space-x-reverse">
                <Button
                  variant="primary"
                  size="sm"
                  onClick={() => {
                    setSelectedGroup(group)
                    setShowPointsModal(true)
                  }}
                  icon={Award}
                  className="flex-1"
                >
                  إضافة نقاط
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  icon={TrendingUp}
                >
                  التفاصيل
                </Button>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Empty State */}
      {groups.length === 0 && (
        <div className="text-center py-12">
          <Users className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-gray-100">
            لا توجد مجموعات
          </h3>
          <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
            ابدأ بإنشاء مجموعات للمتدربين وإدارة النقاط
          </p>
        </div>
      )}

      {/* Points Modal */}
      {showPointsModal && selectedGroup && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white dark:bg-gray-800 rounded-xl shadow-xl max-w-md w-full p-6">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">
              إضافة نقاط لـ {selectedGroup.name}
            </h3>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  عدد النقاط
                </label>
                <input
                  type="number"
                  value={pointsData.points}
                  onChange={(e) => setPointsData(prev => ({ ...prev, points: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100"
                  placeholder="مثال: 10"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  سبب إضافة النقاط
                </label>
                <input
                  type="text"
                  value={pointsData.reason}
                  onChange={(e) => setPointsData(prev => ({ ...prev, reason: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100"
                  placeholder="مثال: مشاركة ممتازة في النشاط"
                />
              </div>
            </div>
            <div className="flex justify-end space-x-3 rtl:space-x-reverse mt-6">
              <Button
                variant="outline"
                onClick={() => {
                  setShowPointsModal(false)
                  setSelectedGroup(null)
                  setPointsData({ points: '', reason: '' })
                }}
                size="sm"
              >
                إلغاء
              </Button>
              <Button
                variant="primary"
                onClick={handleAddPoints}
                size="sm"
                icon={Award}
              >
                إضافة النقاط
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

// Settings Tab Component
const SettingsTab = ({ workshop }) => {
  return (
    <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
      <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">
        إعدادات ورشة العمل
      </h3>
      <p className="text-gray-600 dark:text-gray-400">
        إعدادات ورشة العمل ستكون متاحة قريباً...
      </p>
    </div>
  )
}

export default WorkshopDetails
