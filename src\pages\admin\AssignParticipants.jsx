import React, { useState, useEffect } from 'react'
import { useParams, useNavigate } from 'react-router-dom'
import { motion } from 'framer-motion'
import {
  ArrowLeft,
  Search,
  UserPlus,
  UserMinus,
  Users,
  CheckSquare,
  Square,
  Filter,
  Mail,
  Building,
  Save
} from 'lucide-react'
import Button from '../../components/ui/Button'
import { useLanguage } from '../../contexts/LanguageContext'
import toast from 'react-hot-toast'

const AssignParticipants = () => {
  const { id } = useParams()
  const navigate = useNavigate()
  const { t } = useLanguage()
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedDepartment, setSelectedDepartment] = useState('all')
  const [selectedUsers, setSelectedUsers] = useState([])
  const [workshop, setWorkshop] = useState(null)
  const [allUsers, setAllUsers] = useState([])
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)

  useEffect(() => {
    loadWorkshopAndUsers()
  }, [id])

  const loadWorkshopAndUsers = async () => {
    try {
      setLoading(true)

      // تحميل ورشة العمل
      const workshopsFromStorage = JSON.parse(localStorage.getItem('workshops_data') || '[]')
      const foundWorkshop = workshopsFromStorage.find(w => w.id === id || w.id === parseInt(id))

      if (foundWorkshop) {
        setWorkshop(foundWorkshop)
        setSelectedUsers(foundWorkshop.selectedTrainees || [])
      }

      // تحميل المتدربين
      const trainees = JSON.parse(localStorage.getItem('app_trainees') || '[]')
      setAllUsers(trainees)

    } catch (error) {
      console.error('Error loading data:', error)
      toast.error('فشل في تحميل البيانات')
    } finally {
      setLoading(false)
    }
  }

  const departments = [...new Set(allUsers.map(user => user.department).filter(Boolean))]

  const filteredUsers = allUsers.filter(user => {
    const matchesSearch = user.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         (user.email && user.email.toLowerCase().includes(searchTerm.toLowerCase()))
    const matchesDepartment = selectedDepartment === 'all' || user.department === selectedDepartment
    return matchesSearch && matchesDepartment
  })

  const handleUserToggle = (userName) => {
    setSelectedUsers(prev => {
      if (prev.includes(userName)) {
        return prev.filter(name => name !== userName)
      } else {
        return [...prev, userName]
      }
    })
  }

  const handleSelectAll = () => {
    const allSelected = filteredUsers.every(user => selectedUsers.includes(user.name))
    if (allSelected) {
      // إلغاء تحديد الكل
      const filteredNames = filteredUsers.map(u => u.name)
      setSelectedUsers(prev => prev.filter(name => !filteredNames.includes(name)))
    } else {
      // تحديد الكل
      const newSelections = filteredUsers.map(u => u.name)
      setSelectedUsers(prev => {
        const combined = [...prev, ...newSelections]
        return [...new Set(combined)] // إزالة المكررات
      })
    }
  }

  const handleSave = async () => {
    setSaving(true)
    try {
      // تحديث ورشة العمل مع المشاركين الجدد
      const workshops = JSON.parse(localStorage.getItem('workshops_data') || '[]')
      const workshopIndex = workshops.findIndex(w => w.id === workshop.id)

      if (workshopIndex === -1) {
        toast.error('ورشة العمل غير موجودة')
        return
      }

      workshops[workshopIndex] = {
        ...workshops[workshopIndex],
        selectedTrainees: selectedUsers,
        enrolledCount: selectedUsers.length,
        updatedAt: new Date().toISOString()
      }

      localStorage.setItem('workshops_data', JSON.stringify(workshops))

      console.log('✅ تم تحديث تكليف المشاركين:', {
        workshopId: workshop.id,
        selectedCount: selectedUsers.length,
        participants: selectedUsers
      })

      toast.success('تم حفظ المشاركين بنجاح')
      navigate(`/workshops/${workshop.id}`)
    } catch (error) {
      console.error('Error saving participants:', error)
      toast.error('فشل في حفظ المشاركين')
    } finally {
      setSaving(false)
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto mb-4"></div>
          <p className="text-gray-600 dark:text-gray-400">جاري تحميل البيانات...</p>
        </div>
      </div>
    )
  }

  if (!workshop) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
        <div className="text-center">
          <p className="text-gray-600 dark:text-gray-400">ورشة العمل غير موجودة</p>
          <Button onClick={() => navigate('/workshops')} className="mt-4">
            العودة لورش العمل
          </Button>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4 rtl:space-x-reverse">
              <Button
                variant="outline"
                onClick={() => navigate(`/workshops/${workshop.id}`)}
                icon={ArrowLeft}
                className="rtl:rotate-180"
              >
                العودة
              </Button>
              <div>
                <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                  تعيين المشاركين
                </h1>
                <p className="text-gray-600 dark:text-gray-400 mt-1">
                  {workshop.title}
                </p>
              </div>
            </div>
            <div className="flex items-center space-x-4 rtl:space-x-reverse">
              <div className="text-sm text-gray-600 dark:text-gray-400">
                المحددين: {selectedUsers.length}
              </div>
              <Button
                onClick={handleSave}
                disabled={saving}
                icon={Save}
                className="bg-green-600 hover:bg-green-700"
              >
                {saving ? 'جاري الحفظ...' : 'حفظ المشاركين'}
              </Button>
            </div>
          </div>
        </div>

        {/* Filters */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6 mb-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {/* Search */}
            <div className="relative">
              <Search className="absolute left-3 rtl:left-auto rtl:right-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <input
                type="text"
                placeholder="البحث بالاسم أو البريد الإلكتروني..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 rtl:pl-4 rtl:pr-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent dark:bg-gray-700 dark:text-gray-100"
              />
            </div>

            {/* Department Filter */}
            <div className="relative">
              <Filter className="absolute left-3 rtl:left-auto rtl:right-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <select
                value={selectedDepartment}
                onChange={(e) => setSelectedDepartment(e.target.value)}
                className="w-full pl-10 rtl:pl-4 rtl:pr-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent dark:bg-gray-700 dark:text-gray-100 appearance-none"
              >
                <option value="all">جميع الأقسام</option>
                {departments.map(dept => (
                  <option key={dept} value={dept}>{dept}</option>
                ))}
              </select>
            </div>

            {/* Actions */}
            <div className="flex space-x-2 rtl:space-x-reverse">
              <Button
                variant="outline"
                onClick={handleSelectAll}
                icon={filteredUsers.every(user => selectedUsers.includes(user.name)) ? CheckSquare : Square}
                className="flex-1"
              >
                {filteredUsers.every(user => selectedUsers.includes(user.name)) ? 'إلغاء الكل' : 'تحديد الكل'}
              </Button>
            </div>
          </div>
        </div>

        {/* Users Grid */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow">
          <div className="p-6 border-b border-gray-200 dark:border-gray-700">
            <div className="flex items-center justify-between">
              <h2 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                المتدربين المتاحين
              </h2>
              <div className="text-sm text-gray-600 dark:text-gray-400">
                إجمالي: {filteredUsers.length} | محدد: {selectedUsers.length}
              </div>
            </div>
          </div>

          <div className="p-6">
            {filteredUsers.length > 0 ? (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {filteredUsers.map(user => {
                  const isSelected = selectedUsers.includes(user.name)
                  return (
                    <motion.div
                      key={user.id}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      className={`p-4 rounded-lg border-2 cursor-pointer transition-all ${
                        isSelected
                          ? 'border-green-500 bg-green-50 dark:bg-green-900/20'
                          : 'border-gray-200 dark:border-gray-600 hover:border-blue-300 dark:hover:border-blue-500'
                      }`}
                      onClick={() => handleUserToggle(user.name)}
                    >
                      <div className="flex items-center space-x-3 rtl:space-x-reverse">
                        <div className="w-10 h-10 bg-blue-500 rounded-full flex items-center justify-center text-white font-bold">
                          {user.name.charAt(0)}
                        </div>
                        <div className="flex-1 min-w-0">
                          <h3 className="text-sm font-medium text-gray-900 dark:text-gray-100 truncate">
                            {user.name}
                          </h3>
                          {user.email && (
                            <p className="text-xs text-gray-500 dark:text-gray-400 truncate">
                              {user.email}
                            </p>
                          )}
                          {user.department && (
                            <p className="text-xs text-gray-500 dark:text-gray-400">
                              {user.department}
                            </p>
                          )}
                        </div>
                        <div className="flex-shrink-0">
                          {isSelected ? (
                            <CheckSquare className="w-5 h-5 text-green-500" />
                          ) : (
                            <Square className="w-5 h-5 text-gray-400" />
                          )}
                        </div>
                      </div>
                    </motion.div>
                  )
                })}
              </div>
            ) : (
              <div className="text-center py-12">
                <Users className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-600 dark:text-gray-400">
                  لا يوجد متدربين متاحين
                </p>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}

export default AssignParticipants
