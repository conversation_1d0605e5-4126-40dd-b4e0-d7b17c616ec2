// نظام حفظ البيانات في localStorage
class DataStorage {
  constructor() {
    this.keys = {
      workshops: 'workshops_data',
      groups: 'workshop_groups',
      points: 'group_points',
      badges: 'user_badges',
      achievements: 'user_achievements',
      chat: 'group_chat',
      community_posts: 'community_posts'
    }
  }

  // حفظ البيانات
  save(key, data) {
    try {
      localStorage.setItem(this.keys[key], JSON.stringify(data))
      return true
    } catch (error) {
      console.error('Error saving data:', error)
      return false
    }
  }

  // استرجاع البيانات
  load(key) {
    try {
      const data = localStorage.getItem(this.keys[key])
      return data ? JSON.parse(data) : null
    } catch (error) {
      console.error('Error loading data:', error)
      return null
    }
  }

  // حذف البيانات
  remove(key) {
    try {
      localStorage.removeItem(this.keys[key])
      return true
    } catch (error) {
      console.error('Error removing data:', error)
      return false
    }
  }

  // ورش العمل
  saveWorkshops(workshops) {
    return this.save('workshops', workshops)
  }

  loadWorkshops() {
    try {
      // تحميل ورش العمل من جميع المصادر
      let workshopsData = this.load('workshops_data') || []
      let appWorkshops = this.load('app_workshops') || []
      let legacyWorkshops = this.load('workshops') || []

      // التأكد من أن جميع البيانات arrays
      if (!Array.isArray(workshopsData)) workshopsData = []
      if (!Array.isArray(appWorkshops)) appWorkshops = []
      if (!Array.isArray(legacyWorkshops)) legacyWorkshops = []

      // دمج البيانات من جميع المصادر
      const allWorkshops = [...workshopsData, ...appWorkshops, ...legacyWorkshops]

      // إزالة التكرار بناءً على الـ ID
      const uniqueWorkshops = allWorkshops.filter((workshop, index, self) =>
        index === self.findIndex(w => w.id === workshop.id)
      )

      return uniqueWorkshops
    } catch (error) {
      console.error('Error loading workshops:', error)
      return []
    }
  }

  // إنشاء ورشة عمل تجريبية إذا لم توجد ورش
  createDemoWorkshopIfNeeded() {
    try {
      const workshops = this.loadWorkshops()
      if (workshops.length === 0) {
        const demoWorkshop = {
          id: 'demo-workshop-1',
          title: 'ورشة عمل تجريبية',
          description: 'ورشة عمل تجريبية لاختبار النظام',
          startDate: new Date().toISOString().split('T')[0],
          endDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
          participants: [],
          selectedTrainees: [],
          status: 'active',
          createdAt: new Date().toISOString(),
          dailyHours: 4,
          totalHours: 28
        }

        this.save('workshops_data', [demoWorkshop])
        console.log('✅ تم إنشاء ورشة عمل تجريبية')
        return demoWorkshop
      }
      return null
    } catch (error) {
      console.error('Error creating demo workshop:', error)
      return null
    }
  }

  // المجموعات (استخدام مفتاح موحد)
  saveGroups(workshopId, groups) {
    const allGroups = this.load('workshop_groups') || {}
    allGroups[workshopId] = groups
    return this.save('workshop_groups', allGroups)
  }

  loadGroups(workshopId) {
    const allGroups = this.load('workshop_groups') || {}
    return allGroups[workshopId] || []
  }

  getGroups(workshopId) {
    return this.loadGroups(workshopId)
  }

  // النقاط
  savePoints(workshopId, points) {
    const allPoints = this.load('points') || {}
    allPoints[workshopId] = points
    return this.save('points', allPoints)
  }

  loadPoints(workshopId) {
    const allPoints = this.load('points') || {}
    return allPoints[workshopId] || {}
  }

  getPoints(workshopId) {
    return this.loadPoints(workshopId)
  }

  // الشارات
  saveBadges(userId, badges) {
    const allBadges = this.load('badges') || {}
    allBadges[userId] = badges
    return this.save('badges', allBadges)
  }

  loadBadges(userId) {
    const allBadges = this.load('badges') || {}
    return allBadges[userId] || []
  }

  // إضافة شارة للمستخدم
  addBadge(userId, badge) {
    const userBadges = this.loadBadges(userId)
    const newBadge = {
      id: Date.now(),
      ...badge,
      earnedAt: new Date().toISOString()
    }
    userBadges.push(newBadge)
    return this.saveBadges(userId, userBadges)
  }

  // الإنجازات
  saveAchievements(userId, achievements) {
    const allAchievements = this.load('achievements') || {}
    allAchievements[userId] = achievements
    return this.save('achievements', allAchievements)
  }

  loadAchievements(userId) {
    const allAchievements = this.load('achievements') || {}
    return allAchievements[userId] || []
  }

  // إضافة إنجاز
  addAchievement(userId, achievement) {
    const userAchievements = this.loadAchievements(userId)
    const newAchievement = {
      id: Date.now(),
      ...achievement,
      achievedAt: new Date().toISOString()
    }
    userAchievements.push(newAchievement)
    return this.saveAchievements(userId, userAchievements)
  }

  // الدردشة
  saveChat(groupId, messages) {
    const allChats = this.load('chat') || {}
    allChats[groupId] = messages
    return this.save('chat', allChats)
  }

  loadChat(groupId) {
    const allChats = this.load('chat') || {}
    return allChats[groupId] || []
  }

  // إضافة رسالة للدردشة
  addMessage(groupId, message) {
    const messages = this.loadChat(groupId)
    const newMessage = {
      id: Date.now(),
      ...message,
      timestamp: new Date().toISOString()
    }
    messages.push(newMessage)
    this.saveChat(groupId, messages)
    return newMessage
  }

  // تحديث نقاط المجموعة
  updateGroupPoints(workshopId, groupId, pointsToAdd) {
    const points = this.loadPoints(workshopId)
    if (!points[groupId]) {
      points[groupId] = 0
    }
    points[groupId] += pointsToAdd
    return this.savePoints(workshopId, points)
  }



  // إضافة مجموعة
  addGroup(workshopId, group) {
    const groups = this.loadGroups(workshopId)
    groups.push(group)
    return this.saveGroups(workshopId, groups)
  }

  // الحصول على ترتيب المجموعات
  getGroupRankings(workshopId) {
    const points = this.loadPoints(workshopId)
    const groups = this.loadGroups(workshopId)
    
    return groups.map(group => ({
      ...group,
      points: points[group.id] || 0
    })).sort((a, b) => b.points - a.points)
  }

  // إنهاء ورشة العمل وإعلان الفائز
  finishWorkshop(workshopId) {
    const rankings = this.getGroupRankings(workshopId)
    if (rankings.length === 0) return null

    const winnerGroup = rankings[0]
    
    // إضافة شارات للفائزين
    winnerGroup.members.forEach(member => {
      this.addBadge(member.id, {
        type: 'workshop_winner',
        title: 'فائز ورشة العمل',
        description: `فاز في ورشة العمل: ${winnerGroup.workshopTitle}`,
        icon: '🏆',
        color: 'gold',
        workshopId: workshopId,
        groupName: winnerGroup.name
      })

      this.addAchievement(member.id, {
        type: 'workshop_victory',
        title: 'انتصار في ورشة العمل',
        description: `فاز مع مجموعة ${winnerGroup.name} بـ ${winnerGroup.points} نقطة`,
        points: winnerGroup.points,
        workshopId: workshopId
      })
    })

    return {
      winnerGroup,
      rankings
    }
  }

  // فحص الدرجات وإضافة الشارات
  checkGradeAndAddBadge(userId, grade, taskTitle) {
    if (grade >= 90) {
      this.addBadge(userId, {
        type: 'excellent_grade',
        title: 'درجة ممتازة',
        description: `حصل على ${grade}% في: ${taskTitle}`,
        icon: '⭐',
        color: 'yellow',
        grade: grade,
        taskTitle: taskTitle
      })

      this.addAchievement(userId, {
        type: 'excellent_performance',
        title: 'أداء ممتاز',
        description: `حصل على درجة ممتازة (${grade}%) في ${taskTitle}`,
        grade: grade,
        taskTitle: taskTitle
      })
    } else if (grade >= 80) {
      this.addBadge(userId, {
        type: 'good_grade',
        title: 'درجة جيدة',
        description: `حصل على ${grade}% في: ${taskTitle}`,
        icon: '👍',
        color: 'blue',
        grade: grade,
        taskTitle: taskTitle
      })
    }
  }

  // منشورات المجتمع
  saveCommunityPosts(posts) {
    try {
      localStorage.setItem('community_posts', JSON.stringify(posts))
      return true
    } catch (error) {
      console.error('Error saving community posts:', error)
      return false
    }
  }

  loadCommunityPosts() {
    try {
      const posts = localStorage.getItem('community_posts')
      return posts ? JSON.parse(posts) : []
    } catch (error) {
      console.error('Error loading community posts:', error)
      return []
    }
  }

  // إضافة منشور جديد
  addCommunityPost(post) {
    const posts = this.loadCommunityPosts()
    const newPost = {
      id: Date.now(),
      ...post,
      createdAt: new Date().toISOString(),
      likes: [],
      comments: [],
      shares: 0
    }
    posts.unshift(newPost)
    this.saveCommunityPosts(posts)
    return newPost
  }

  // تحديث منشور
  updateCommunityPost(postId, updates) {
    const posts = this.loadCommunityPosts()
    const updatedPosts = posts.map(post =>
      post.id === postId ? { ...post, ...updates } : post
    )
    this.saveCommunityPosts(updatedPosts)
    return updatedPosts
  }

  // حذف منشور
  deleteCommunityPost(postId) {
    const posts = this.loadCommunityPosts()
    const filteredPosts = posts.filter(post => post.id !== postId)
    this.saveCommunityPosts(filteredPosts)
    return filteredPosts
  }

  // مسح جميع البيانات (للاختبار)
  clearAll() {
    Object.values(this.keys).forEach(key => {
      localStorage.removeItem(key)
    })
    // مسح منشورات المجتمع أيضاً
    localStorage.removeItem('community_posts')
  }
}

// إنشاء instance واحد للاستخدام
const dataStorage = new DataStorage()

// Create a dataService interface for compatibility
export const dataService = {
  // Generic data operations
  getData(key) {
    return dataStorage.load(key);
  },
  
  setData(key, data) {
    return dataStorage.save(key, data);
  },
  
  removeData(key) {
    try {
      localStorage.removeItem(dataStorage.keys[key] || key);
      return true;
    } catch (error) {
      console.error('Error removing data:', error);
      return false;
    }
  },

  // Specific operations
  getUsers() {
    return JSON.parse(localStorage.getItem('users') || '[]');
  },
  
  getCourses() {
    return JSON.parse(localStorage.getItem('courses') || '[]');
  },
  
  getWorkshops() {
    return dataStorage.load('workshops') || [];
  },
  
  getTasks() {
    return JSON.parse(localStorage.getItem('tasks') || '[]');
  },
  
  getGroups() {
    return JSON.parse(localStorage.getItem('groups') || '[]');
  },
  
  getNotifications() {
    return JSON.parse(localStorage.getItem('notifications') || '[]');
  }
};

export default dataStorage
