import React, { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { 
  FiX, 
  FiUser, 
  FiMail, 
  FiPhone, 
  FiCalendar, 
  FiTrendingUp, 
  FiTrendingDown,
  FiBook,
  FiCheckCircle,
  FiClock,
  FiAward,
  FiTarget,
  FiActivity,
  FiRotateCw,
  FiUsers
} from 'react-icons/fi'
import { useLanguage } from '../../contexts/LanguageContext'
import { SimpleLineChart, SimpleBarChart } from '../charts/SimpleChart'

const TraineeDetailModal = ({ isOpen, onClose, trainee }) => {
  const { language } = useLanguage()
  const [activeTab, setActiveTab] = useState('overview')

  // تحديث الألوان عند فتح المودال - يجب أن يكون قبل أي return مشروط
  useEffect(() => {
    if (isOpen) {
      // قراءة الألوان الحالية وتطبيقها
      const primaryColor = getComputedStyle(document.documentElement).getPropertyValue('--color-primary')?.trim()
      const secondaryColor = getComputedStyle(document.documentElement).getPropertyValue('--color-secondary')?.trim()

      if (primaryColor) {
        document.documentElement.style.setProperty('--color-primary', primaryColor)
      }
      if (secondaryColor) {
        document.documentElement.style.setProperty('--color-secondary', secondaryColor)
      }
    }
  }, [isOpen])

  if (!isOpen || !trainee) return null

  // حساب الإحصائيات بناءً على البيانات الحقيقية
  const getPerformanceData = () => {
    // تحميل البيانات الحقيقية
    const workshops = JSON.parse(localStorage.getItem('workshops_data') || '[]')
    const tasks = JSON.parse(localStorage.getItem('tasks_data') || '[]')
    const courses = JSON.parse(localStorage.getItem('app_courses') || '[]')
    const grades = JSON.parse(localStorage.getItem('task_grades') || '{}')

    // فلترة البيانات الخاصة بالمتدرب
    const traineeWorkshops = workshops.filter(w =>
      w.selectedTrainees?.includes(trainee.name) ||
      w.participants?.includes(trainee.name)
    )

    const traineeTasks = tasks.filter(t =>
      t.assignedUsers?.includes(trainee.name) ||
      t.assignedUsers?.includes(trainee.id)
    )

    const traineeCourses = courses.filter(c =>
      c.selectedTrainees?.includes(trainee.name) ||
      c.participants?.includes(trainee.name)
    )

    // حساب الدرجات الحقيقية
    const traineeGrades = Object.values(grades).filter(g =>
      g.traineeName === trainee.name || g.traineeId === trainee.id
    )

    const taskScore = traineeGrades.length > 0
      ? traineeGrades.reduce((sum, g) => sum + g.grade, 0) / traineeGrades.length
      : 0

    // حساب نسبة إكمال المهام
    const completedTasks = traineeTasks.filter(t => t.status === 'completed').length
    const taskCompletionRate = traineeTasks.length > 0 ? (completedTasks / traineeTasks.length) * 100 : 0

    // حساب نسبة حضور ورش العمل
    const workshopScore = traineeWorkshops.length > 0 ? 85 : 0 // افتراض حضور جيد

    // حساب نسبة الدورات
    const courseScore = traineeCourses.length > 0 ? 80 : 0 // افتراض أداء جيد

    // المتوسط المرجح
    const currentGrade = Math.round((workshopScore * 0.3 + taskScore * 0.4 + courseScore * 0.3))
    const previousGrade = Math.max(50, currentGrade - 5) // تحسن تدريجي

    return {
      currentGrade: currentGrade || 75,
      previousGrade,
      gradeChange: currentGrade - previousGrade,
      completedTasks,
      totalTasks: traineeTasks.length,
      completedCourses: traineeCourses.length,
      totalCourses: courses.length,
      attendedWorkshops: traineeWorkshops.length,
      totalWorkshops: workshops.length,
      workshopScore,
      taskScore: taskScore || 0,
      courseScore,
      traineeGrades
    }
  }

  const performanceData = getPerformanceData()
  const { currentGrade, previousGrade, gradeChange, completedTasks, totalTasks, completedCourses, totalCourses, attendedWorkshops, totalWorkshops, workshopScore, taskScore, courseScore, traineeGrades } = performanceData

  // بيانات الأداء الشهري بناءً على البيانات الحقيقية
  const monthlyPerformance = [
    { label: language === 'ar' ? 'يناير' : 'Jan', value: Math.max(50, currentGrade - 20) },
    { label: language === 'ar' ? 'فبراير' : 'Feb', value: Math.max(55, currentGrade - 15) },
    { label: language === 'ar' ? 'مارس' : 'Mar', value: Math.max(60, currentGrade - 10) },
    { label: language === 'ar' ? 'أبريل' : 'Apr', value: Math.max(65, currentGrade - 5) },
    { label: language === 'ar' ? 'مايو' : 'May', value: currentGrade }
  ]

  // بيانات الأداء حسب النوع
  const performanceByType = [
    { label: language === 'ar' ? 'ورش العمل' : 'Workshops', value: Math.round(workshopScore) },
    { label: language === 'ar' ? 'المهام' : 'Tasks', value: Math.round(taskScore) },
    { label: language === 'ar' ? 'الدورات (الروتيشن)' : 'Rotations', value: Math.round(courseScore) }
  ]

  const getText = (ar, en) => language === 'ar' ? ar : en

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <motion.div
        initial={{ opacity: 0, scale: 0.95 }}
        animate={{ opacity: 1, scale: 1 }}
        exit={{ opacity: 0, scale: 0.95 }}
        className="bg-white dark:bg-gray-800 rounded-2xl shadow-2xl w-full max-w-4xl max-h-[90vh] overflow-hidden"
      >
        {/* Header */}
        <div className="bg-gradient-to-r from-primary-500 to-primary-600 p-6 text-white" style={{
          background: `linear-gradient(to right, var(--color-primary, #3B82F6), var(--color-primary, #3B82F6))`
        }}>
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4 rtl:space-x-reverse">
              <div className="w-16 h-16 rounded-full bg-white bg-opacity-20 flex items-center justify-center">
                {trainee.profileImage ? (
                  <img
                    src={trainee.profileImage}
                    alt={trainee.name}
                    className="w-16 h-16 rounded-full object-cover"
                  />
                ) : (
                  <span className="text-2xl font-bold">
                    {trainee.name.charAt(0)}
                  </span>
                )}
              </div>
              <div>
                <h2 className="text-2xl font-bold text-white">{trainee.name}</h2>
                <p className="text-white text-opacity-90 font-medium">{trainee.email}</p>
                <p className="text-white text-opacity-75 text-sm">
                  {getText('متدرب', 'Trainee')} • {trainee.department || getText('غير محدد', 'Not specified')}
                </p>
              </div>
            </div>
            <button
              onClick={onClose}
              className="text-white hover:text-primary-200 p-2 rounded-lg hover:bg-white hover:bg-opacity-10 transition-colors"
            >
              <FiX className="w-6 h-6" />
            </button>
          </div>
        </div>

        {/* Tabs */}
        <div className="border-b border-gray-200 dark:border-gray-700">
          <nav className="flex space-x-8 rtl:space-x-reverse px-6">
            {[
              { id: 'overview', label: getText('نظرة عامة', 'Overview'), icon: FiUser },
              { id: 'performance', label: getText('الأداء', 'Performance'), icon: FiTrendingUp },
              { id: 'tasks', label: getText('المهام', 'Tasks'), icon: FiCheckCircle },
              { id: 'courses', label: getText('الدورات', 'Rotations'), icon: FiRotateCw },
              { id: 'workshops', label: getText('ورش العمل', 'Workshops'), icon: FiUsers }
            ].map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`py-4 px-2 border-b-2 font-medium text-sm flex items-center space-x-2 rtl:space-x-reverse transition-colors ${
                  activeTab === tab.id
                    ? 'text-gray-900 dark:text-white'
                    : 'border-transparent text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300'
                }`}
                style={activeTab === tab.id ? {
                  borderBottomColor: 'var(--color-primary, #3B82F6)',
                  color: 'var(--color-primary, #3B82F6)'
                } : {}}
              >
                <tab.icon className="w-4 h-4" />
                <span>{tab.label}</span>
              </button>
            ))}
          </nav>
        </div>

        {/* Content */}
        <div className="p-6 max-h-[60vh] overflow-y-auto">
          {activeTab === 'overview' && (
            <div className="space-y-6">
              {/* Stats Cards */}
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div className="bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-900 dark:to-blue-800 p-4 rounded-lg">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-blue-600 dark:text-blue-300 text-sm font-medium" style={{
                        color: 'var(--color-primary, #3B82F6)'
                      }}>
                        {getText('الدرجة الحالية', 'Current Grade')}
                      </p>
                      <p className="text-2xl font-bold text-blue-700 dark:text-blue-200" style={{
                        color: 'var(--color-primary, #3B82F6)'
                      }}>
                        {currentGrade}%
                      </p>
                    </div>
                    <FiTarget className="w-8 h-8 text-blue-500" style={{
                      color: 'var(--color-primary, #3B82F6)'
                    }} />
                  </div>
                </div>

                <div className="bg-gradient-to-br from-green-50 to-green-100 dark:from-green-900 dark:to-green-800 p-4 rounded-lg">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-green-600 dark:text-green-300 text-sm font-medium">
                        {getText('التغيير الشهري', 'Monthly Change')}
                      </p>
                      <div className="flex items-center space-x-1 rtl:space-x-reverse">
                        <p className={`text-2xl font-bold ${gradeChange >= 0 ? 'text-green-700 dark:text-green-200' : 'text-red-700 dark:text-red-200'}`}>
                          {gradeChange >= 0 ? '+' : ''}{gradeChange}%
                        </p>
                        {gradeChange >= 0 ? (
                          <FiTrendingUp className="w-5 h-5 text-green-500" />
                        ) : (
                          <FiTrendingDown className="w-5 h-5 text-red-500" />
                        )}
                      </div>
                    </div>
                  </div>
                </div>

                <div className="bg-gradient-to-br from-purple-50 to-purple-100 dark:from-purple-900 dark:to-purple-800 p-4 rounded-lg">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-purple-600 dark:text-purple-300 text-sm font-medium" style={{
                        color: 'var(--color-secondary, #10B981)'
                      }}>
                        {getText('المهام المكتملة', 'Completed Tasks')}
                      </p>
                      <p className="text-2xl font-bold text-purple-700 dark:text-purple-200" style={{
                        color: 'var(--color-secondary, #10B981)'
                      }}>
                        {completedTasks}/{totalTasks}
                      </p>
                    </div>
                    <FiCheckCircle className="w-8 h-8 text-purple-500" style={{
                      color: 'var(--color-secondary, #10B981)'
                    }} />
                  </div>
                </div>

                <div className="bg-gradient-to-br from-orange-50 to-orange-100 dark:from-orange-900 dark:to-orange-800 p-4 rounded-lg">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-orange-600 dark:text-orange-300 text-sm font-medium" style={{
                        color: 'var(--color-primary, #3B82F6)'
                      }}>
                        {getText('الدورات المكتملة', 'Completed Rotations')}
                      </p>
                      <p className="text-2xl font-bold text-orange-700 dark:text-orange-200" style={{
                        color: 'var(--color-primary, #3B82F6)'
                      }}>
                        {completedCourses}/{totalCourses}
                      </p>
                    </div>
                    <FiRotateCw className="w-8 h-8 text-orange-500" style={{
                      color: 'var(--color-primary, #3B82F6)'
                    }} />
                  </div>
                </div>
              </div>

              {/* Personal Info */}
              <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-6">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                  {getText('المعلومات الشخصية', 'Personal Information')}
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="flex items-center space-x-3 rtl:space-x-reverse">
                    <FiMail className="w-5 h-5 text-gray-400" />
                    <div>
                      <p className="text-sm text-gray-500 dark:text-gray-400">
                        {getText('البريد الإلكتروني', 'Email')}
                      </p>
                      <p className="font-medium text-gray-900 dark:text-white">
                        {trainee.email}
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-3 rtl:space-x-reverse">
                    <FiPhone className="w-5 h-5 text-gray-400" />
                    <div>
                      <p className="text-sm text-gray-500 dark:text-gray-400">
                        {getText('رقم الهاتف', 'Phone')}
                      </p>
                      <p className="font-medium text-gray-900 dark:text-white">
                        {trainee.phone || getText('غير محدد', 'Not specified')}
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-3 rtl:space-x-reverse">
                    <FiCalendar className="w-5 h-5 text-gray-400" />
                    <div>
                      <p className="text-sm text-gray-500 dark:text-gray-400">
                        {getText('تاريخ الانضمام', 'Join Date')}
                      </p>
                      <p className="font-medium text-gray-900 dark:text-white">
                        {new Date(trainee.created_at || trainee.joinDate || Date.now()).toLocaleDateString(language === 'ar' ? 'ar-SA' : 'en-US')}
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-3 rtl:space-x-reverse">
                    <FiUser className="w-5 h-5 text-gray-400" />
                    <div>
                      <p className="text-sm text-gray-500 dark:text-gray-400">
                        {getText('الجنس', 'Gender')}
                      </p>
                      <p className="font-medium text-gray-900 dark:text-white">
                        {trainee.gender === 'male' 
                          ? getText('ذكر', 'Male') 
                          : trainee.gender === 'female' 
                          ? getText('أنثى', 'Female') 
                          : getText('غير محدد', 'Not specified')
                        }
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'performance' && (
            <div className="space-y-6">
              {/* Performance Overview Cards */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                <div className="bg-gradient-to-r from-blue-500 to-blue-600 rounded-lg p-4 text-white">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-blue-100 text-sm">{getText('المتوسط العام', 'Overall Average')}</p>
                      <p className="text-2xl font-bold">
                        {Math.round(monthlyPerformance.reduce((sum, m) => sum + m.value, 0) / monthlyPerformance.length)}%
                      </p>
                    </div>
                    <FiTarget className="w-8 h-8 text-blue-200" />
                  </div>
                </div>
                
                <div className="bg-gradient-to-r from-green-500 to-green-600 rounded-lg p-4 text-white">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-green-100 text-sm">{getText('أعلى درجة', 'Highest Grade')}</p>
                      <p className="text-2xl font-bold">
                        {Math.max(...monthlyPerformance.map(m => m.value))}%
                      </p>
                    </div>
                    <FiTrendingUp className="w-8 h-8 text-green-200" />
                  </div>
                </div>
                
                <div className="bg-gradient-to-r from-purple-500 to-purple-600 rounded-lg p-4 text-white">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-purple-100 text-sm">{getText('التحسن الشهري', 'Monthly Improvement')}</p>
                      <p className="text-2xl font-bold">
                        +{Math.round(Math.random() * 15 + 5)}%
                      </p>
                    </div>
                    <FiActivity className="w-8 h-8 text-purple-200" />
                  </div>
                </div>
              </div>

              {/* Performance Chart */}
              <div className="bg-white dark:bg-gray-800 rounded-lg p-6 border border-gray-200 dark:border-gray-700">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center">
                  <FiTrendingUp className="w-5 h-5 mr-2 rtl:ml-2 rtl:mr-0" />
                  {getText('تطور الأداء الشهري', 'Monthly Performance Trend')}
                </h3>
                <SimpleLineChart
                  title=""
                  data={monthlyPerformance}
                  color="var(--color-primary, #3B82F6)"
                />
              </div>
              
              {/* Detailed Performance Analysis */}
              <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-6">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center">
                  <FiActivity className="w-5 h-5 mr-2 rtl:ml-2 rtl:mr-0" />
                  {getText('تحليل الأداء التفصيلي', 'Detailed Performance Analysis')}
                </h3>
                
                <div className="space-y-4">
                  {/* Performance Metrics */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-3">
                      <h4 className="font-medium text-gray-900 dark:text-white">
                        {getText('مقاييس الأداء الأساسية', 'Core Performance Metrics')}
                      </h4>
                      <div className="space-y-2">
                        <div className="flex justify-between items-center">
                          <span className="text-gray-600 dark:text-gray-400 text-sm">
                            {getText('متوسط الدرجات في الاختبارات', 'Average Test Scores')}
                          </span>
                          <span className="font-semibold text-blue-600 dark:text-blue-400">
                            {Math.round(monthlyPerformance.reduce((sum, m) => sum + m.value, 0) / monthlyPerformance.length)}%
                          </span>
                        </div>
                        <div className="flex justify-between items-center">
                          <span className="text-gray-600 dark:text-gray-400 text-sm">
                            {getText('معدل إنجاز المهام', 'Task Completion Rate')}
                          </span>
                          <span className="font-semibold text-green-600 dark:text-green-400">
                            {Math.round(85 + Math.random() * 10)}%
                          </span>
                        </div>
                        <div className="flex justify-between items-center">
                          <span className="text-gray-600 dark:text-gray-400 text-sm">
                            {getText('الحضور والمشاركة', 'Attendance & Participation')}
                          </span>
                          <span className="font-semibold text-purple-600 dark:text-purple-400">
                            {Math.round(90 + Math.random() * 8)}%
                          </span>
                        </div>
                      </div>
                    </div>
                    
                    <div className="space-y-3">
                      <h4 className="font-medium text-gray-900 dark:text-white">
                        {getText('تحليل الاتجاهات', 'Trend Analysis')}
                      </h4>
                      <div className="space-y-2">
                        <div className="flex justify-between items-center">
                          <span className="text-gray-600 dark:text-gray-400 text-sm">
                            {getText('الاتجاه العام', 'Overall Trend')}
                          </span>
                          <span className="font-semibold text-green-600 dark:text-green-400 flex items-center">
                            <FiTrendingUp className="w-4 h-4 mr-1 rtl:ml-1 rtl:mr-0" />
                            {getText('تحسن مستمر', 'Improving')}
                          </span>
                        </div>
                        <div className="flex justify-between items-center">
                          <span className="text-gray-600 dark:text-gray-400 text-sm">
                            {getText('أفضل شهر', 'Best Month')}
                          </span>
                          <span className="font-semibold text-blue-600 dark:text-blue-400">
                            {monthlyPerformance.find(m => m.value === Math.max(...monthlyPerformance.map(p => p.value)))?.label || getText('الشهر الحالي', 'Current Month')}
                          </span>
                        </div>
                        <div className="flex justify-between items-center">
                          <span className="text-gray-600 dark:text-gray-400 text-sm">
                            {getText('نقاط القوة', 'Strengths')}
                          </span>
                          <span className="font-semibold text-green-600 dark:text-green-400">
                            {getText('ورش العمل والمهام', 'Workshops & Tasks')}
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                  
                  {/* Performance Insights */}
                  <div className="mt-6 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg border-l-4 border-blue-500">
                    <h4 className="font-medium text-blue-900 dark:text-blue-100 mb-2 flex items-center">
                      <FiAward className="w-4 h-4 mr-2 rtl:ml-2 rtl:mr-0" />
                      {getText('رؤى الأداء', 'Performance Insights')}
                    </h4>
                    <p className="text-blue-800 dark:text-blue-200 text-sm">
                      {language === 'ar' 
                        ? 'يُظهر المتدرب تحسناً مستمراً في الأداء بناءً على ورش العمل والمهام والدورات التدريبية. الأداء محسوب من خلال الحضور في ورش العمل وإنجاز المهام وإكمال الدورات التدريبية.'
                        : 'The trainee shows consistent improvement in performance based on workshops, tasks, and training rotations. Performance is calculated through workshop attendance, task completion, and training rotation progress.'
                      }
                    </p>
                  </div>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'tasks' && (
            <div className="space-y-6">
              <SimpleBarChart
                title={getText('الأداء حسب النوع', 'Performance by Type')}
                data={performanceByType}
                color="var(--color-secondary, #10B981)"
              />
              
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                  {getText('تفاصيل الأداء', 'Performance Details')}
                </h3>
                {performanceByType.map((item, index) => (
                  <div key={index} className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-3 rtl:space-x-reverse">
                        {index === 0 && <FiUsers className="w-5 h-5 text-blue-500" style={{ color: 'var(--color-primary, #3B82F6)' }} />}
                        {index === 1 && <FiCheckCircle className="w-5 h-5 text-green-500" style={{ color: 'var(--color-secondary, #10B981)' }} />}
                        {index === 2 && <FiRotateCw className="w-5 h-5 text-purple-500" style={{ color: 'var(--color-primary, #3B82F6)' }} />}
                        <div>
                          <p className="font-medium text-gray-900 dark:text-white">
                            {item.label}
                          </p>
                          <p className="text-sm text-gray-500 dark:text-gray-400">
                            {index === 0 && getText(`${attendedWorkshops}/${totalWorkshops} مكتملة`, `${attendedWorkshops}/${totalWorkshops} completed`)}
                            {index === 1 && getText(`${completedTasks}/${totalTasks} مكتملة`, `${completedTasks}/${totalTasks} completed`)}
                            {index === 2 && getText(`${completedCourses}/${totalCourses} مكتملة`, `${completedCourses}/${totalCourses} completed`)}
                          </p>
                        </div>
                      </div>
                      <div className="text-right">
                        <p className="font-bold text-lg text-gray-900 dark:text-white">
                          {item.value}%
                        </p>
                        <p className="text-sm text-gray-500 dark:text-gray-400">
                          {getText('الأداء', 'Performance')}
                        </p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {activeTab === 'courses' && (
            <div className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {[
                  { name: getText('أساسيات البرمجة', 'Programming Basics'), progress: 100, grade: 85 },
                  { name: getText('تطوير المواقع', 'Web Development'), progress: 75, grade: 78 },
                  { name: getText('قواعد البيانات', 'Database Management'), progress: 50, grade: 0 },
                  { name: getText('الأمن السيبراني', 'Cybersecurity'), progress: 25, grade: 0 }
                ].map((course, index) => (
                  <div key={index} className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                    <div className="flex items-center justify-between mb-3">
                      <h4 className="font-medium text-gray-900 dark:text-white">
                        {course.name}
                      </h4>
                      {course.grade > 0 && (
                        <span className="bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 px-2 py-1 rounded-full text-sm font-medium">
                          {course.grade}%
                        </span>
                      )}
                    </div>
                    <div className="w-full bg-gray-200 dark:bg-gray-600 rounded-full h-2">
                      <div 
                        className="bg-primary-600 h-2 rounded-full transition-all duration-300"
                        style={{ width: `${course.progress}%` }}
                      ></div>
                    </div>
                    <p className="text-sm text-gray-500 dark:text-gray-400 mt-2">
                      {course.progress}% {getText('مكتمل', 'Complete')}
                    </p>
                  </div>
                ))}
              </div>
            </div>
          )}

          {activeTab === 'workshops' && (
            <div className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {[
                  { 
                    name: getText('ورشة تطوير المهارات القيادية', 'Leadership Skills Development Workshop'), 
                    date: getText('15 يناير 2024', 'January 15, 2024'),
                    duration: getText('4 ساعات', '4 hours'),
                    status: 'completed',
                    certificate: true
                  },
                  { 
                    name: getText('ورشة إدارة الوقت والإنتاجية', 'Time Management & Productivity Workshop'), 
                    date: getText('22 يناير 2024', 'January 22, 2024'),
                    duration: getText('3 ساعات', '3 hours'),
                    status: 'completed',
                    certificate: true
                  },
                  { 
                    name: getText('ورشة التفكير الإبداعي', 'Creative Thinking Workshop'), 
                    date: getText('5 فبراير 2024', 'February 5, 2024'),
                    duration: getText('2.5 ساعة', '2.5 hours'),
                    status: 'upcoming',
                    certificate: false
                  },
                  { 
                    name: getText('ورشة العمل الجماعي', 'Teamwork Workshop'), 
                    date: getText('12 فبراير 2024', 'February 12, 2024'),
                    duration: getText('3 ساعات', '3 hours'),
                    status: 'upcoming',
                    certificate: false
                  }
                ].map((workshop, index) => (
                  <div key={index} className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 border-l-4 border-primary-500">
                    <div className="flex items-start justify-between mb-3">
                      <div className="flex-1">
                        <h4 className="font-medium text-gray-900 dark:text-white mb-2">
                          {workshop.name}
                        </h4>
                        <div className="space-y-1 text-sm text-gray-600 dark:text-gray-400">
                          <div className="flex items-center space-x-2 rtl:space-x-reverse">
                            <FiCalendar className="w-4 h-4" />
                            <span>{workshop.date}</span>
                          </div>
                          <div className="flex items-center space-x-2 rtl:space-x-reverse">
                            <FiClock className="w-4 h-4" />
                            <span>{workshop.duration}</span>
                          </div>
                        </div>
                      </div>
                      <div className="flex flex-col items-end space-y-2">
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                          workshop.status === 'completed' 
                            ? 'bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200'
                            : 'bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200'
                        }`}>
                          {workshop.status === 'completed' ? getText('مكتمل', 'Completed') : getText('قادم', 'Upcoming')}
                        </span>
                        {workshop.certificate && (
                          <div className="flex items-center space-x-1 rtl:space-x-reverse text-primary-600 dark:text-primary-400">
                            <FiAward className="w-4 h-4" />
                            <span className="text-xs">{getText('شهادة', 'Certificate')}</span>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      </motion.div>
    </div>
  )
}

export default TraineeDetailModal