import React, { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import {
  FiX,
  FiUser,
  FiMail,
  FiPhone,
  FiCalendar,
  FiTrendingUp,
  FiTrendingDown,
  FiBook,
  FiCheckCircle,
  FiClock,
  FiAward,
  FiTarget,
  FiActivity,
  FiRotateCw,
  FiUsers,
  FiBarChart
} from 'react-icons/fi'
import { useLanguage } from '../../contexts/LanguageContext'
import { SimpleLineChart, SimpleBarChart } from '../charts/SimpleChart'

const TraineeDetailModal = ({ isOpen, onClose, trainee }) => {
  const { language } = useLanguage()
  const [activeTab, setActiveTab] = useState('overview')

  // تحديث الألوان عند فتح المودال - يجب أن يكون قبل أي return مشروط
  useEffect(() => {
    if (isOpen) {
      // قراءة الألوان الحالية وتطبيقها
      const primaryColor = getComputedStyle(document.documentElement).getPropertyValue('--color-primary')?.trim()
      const secondaryColor = getComputedStyle(document.documentElement).getPropertyValue('--color-secondary')?.trim()

      if (primaryColor) {
        document.documentElement.style.setProperty('--color-primary', primaryColor)
      }
      if (secondaryColor) {
        document.documentElement.style.setProperty('--color-secondary', secondaryColor)
      }
    }
  }, [isOpen])

  if (!isOpen || !trainee) return null

  // حساب الإحصائيات بناءً على البيانات الحقيقية فقط
  const getPerformanceData = () => {
    // تحميل البيانات الحقيقية
    const workshops = JSON.parse(localStorage.getItem('workshops') || '[]')
    const tasks = JSON.parse(localStorage.getItem('tasks') || '[]')
    const courses = JSON.parse(localStorage.getItem('courses') || '[]')
    const grades = JSON.parse(localStorage.getItem('grades') || '[]')

    // فلترة البيانات الخاصة بالمتدرب
    const traineeWorkshops = workshops.filter(w =>
      w.participants?.includes(trainee.id) ||
      w.participants?.includes(trainee.email)
    )

    const traineeTasks = tasks.filter(t =>
      t.assignedUsers?.includes(trainee.id) ||
      t.assignedUsers?.includes(trainee.email)
    )

    const traineeCourses = courses.filter(c =>
      c.selectedTrainees?.includes(trainee.id) ||
      c.selectedTrainees?.includes(trainee.email) ||
      c.enrolledUsers?.includes(trainee.id) ||
      c.enrolledUsers?.includes(trainee.email)
    )

    // حساب الدرجات الحقيقية
    const traineeGrades = grades.filter(g =>
      g.traineeId === trainee.id || g.traineeId === trainee.email
    )

    const taskScore = traineeGrades.length > 0
      ? traineeGrades.reduce((sum, g) => sum + (g.grade || 0), 0) / traineeGrades.length
      : 0

    // حساب نسبة إكمال المهام
    const completedTasks = traineeTasks.filter(t => t.status === 'completed').length
    const taskCompletionRate = traineeTasks.length > 0 ? (completedTasks / traineeTasks.length) * 100 : 0

    // حساب الدرجات الحقيقية فقط
    const workshopGrades = traineeGrades.filter(g => g.type === 'workshop')
    const workshopScore = workshopGrades.length > 0
      ? workshopGrades.reduce((sum, g) => sum + (g.grade || 0), 0) / workshopGrades.length
      : 0

    const courseGrades = traineeGrades.filter(g => g.type === 'course')
    const courseScore = courseGrades.length > 0
      ? courseGrades.reduce((sum, g) => sum + (g.grade || 0), 0) / courseGrades.length
      : 0

    // المتوسط الحقيقي
    const currentGrade = traineeGrades.length > 0 ? Math.round(taskScore) : 0
    const previousGrade = 0 // لا توجد بيانات سابقة

    return {
      currentGrade,
      previousGrade,
      gradeChange: currentGrade - previousGrade,
      completedTasks,
      totalTasks: traineeTasks.length,
      completedCourses: traineeCourses.length,
      totalCourses: traineeCourses.length,
      attendedWorkshops: traineeWorkshops.length,
      totalWorkshops: workshops.length,
      workshopScore,
      taskScore: taskScore || 0,
      courseScore,
      traineeGrades
    }
  }

  const performanceData = getPerformanceData()
  const { currentGrade, previousGrade, gradeChange, completedTasks, totalTasks, completedCourses, totalCourses, attendedWorkshops, totalWorkshops, workshopScore, taskScore, courseScore, traineeGrades } = performanceData

  // استخراج البيانات للاستخدام في المكونات
  const workshops = JSON.parse(localStorage.getItem('workshops') || '[]')
  const tasks = JSON.parse(localStorage.getItem('tasks') || '[]')
  const courses = JSON.parse(localStorage.getItem('courses') || '[]')

  // فلترة البيانات الخاصة بالمتدرب
  const traineeWorkshops = workshops.filter(w =>
    w.participants?.includes(trainee.id) ||
    w.participants?.includes(trainee.email) ||
    w.selectedTrainees?.includes(trainee.id) ||
    w.selectedTrainees?.includes(trainee.email)
  )

  const traineeCourses = courses.filter(c =>
    c.selectedTrainees?.includes(trainee.id) ||
    c.selectedTrainees?.includes(trainee.email) ||
    c.enrolledUsers?.includes(trainee.id) ||
    c.enrolledUsers?.includes(trainee.email)
  )

  // بيانات الأداء الشهري (فقط إذا كانت هناك درجات حقيقية)
  const monthlyPerformance = traineeGrades.length > 0 ? [
    { label: language === 'ar' ? 'الشهر الحالي' : 'Current Month', value: currentGrade }
  ] : []

  // بيانات الأداء حسب النوع
  const performanceByType = [
    { label: language === 'ar' ? 'ورش العمل' : 'Workshops', value: Math.round(workshopScore) },
    { label: language === 'ar' ? 'المهام' : 'Tasks', value: Math.round(taskScore) },
    { label: language === 'ar' ? 'الدورات (الروتيشن)' : 'Rotations', value: Math.round(courseScore) }
  ]

  const getText = (ar, en) => language === 'ar' ? ar : en

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <motion.div
        initial={{ opacity: 0, scale: 0.95 }}
        animate={{ opacity: 1, scale: 1 }}
        exit={{ opacity: 0, scale: 0.95 }}
        className="bg-white dark:bg-gray-800 rounded-2xl shadow-2xl w-full max-w-4xl max-h-[90vh] overflow-hidden"
      >
        {/* Header */}
        <div className="bg-gradient-to-r from-primary-500 to-primary-600 p-6 text-white" style={{
          background: `linear-gradient(to right, var(--color-primary, #3B82F6), var(--color-primary, #3B82F6))`
        }}>
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4 rtl:space-x-reverse">
              <div className="w-16 h-16 rounded-full bg-white bg-opacity-20 flex items-center justify-center">
                {trainee.profileImage ? (
                  <img
                    src={trainee.profileImage}
                    alt={trainee.name}
                    className="w-16 h-16 rounded-full object-cover"
                  />
                ) : (
                  <span className="text-2xl font-bold">
                    {trainee.name.charAt(0)}
                  </span>
                )}
              </div>
              <div>
                <h2 className="text-2xl font-bold text-white">{trainee.name}</h2>
                <p className="text-white text-opacity-90 font-medium">{trainee.email}</p>
                <p className="text-white text-opacity-75 text-sm">
                  {getText('متدرب', 'Trainee')} • {trainee.department || getText('غير محدد', 'Not specified')}
                </p>
              </div>
            </div>
            <button
              onClick={onClose}
              className="text-white hover:text-primary-200 p-2 rounded-lg hover:bg-white hover:bg-opacity-10 transition-colors"
            >
              <FiX className="w-6 h-6" />
            </button>
          </div>
        </div>

        {/* Tabs */}
        <div className="border-b border-gray-200 dark:border-gray-700">
          <nav className="flex space-x-8 rtl:space-x-reverse px-6">
            {[
              { id: 'overview', label: getText('نظرة عامة', 'Overview'), icon: FiUser },
              { id: 'performance', label: getText('الأداء', 'Performance'), icon: FiTrendingUp },
              { id: 'tasks', label: getText('المهام', 'Tasks'), icon: FiCheckCircle },
              { id: 'courses', label: getText('الدورات', 'Rotations'), icon: FiRotateCw },
              { id: 'workshops', label: getText('ورش العمل', 'Workshops'), icon: FiUsers }
            ].map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`py-4 px-2 border-b-2 font-medium text-sm flex items-center space-x-2 rtl:space-x-reverse transition-colors ${
                  activeTab === tab.id
                    ? 'text-gray-900 dark:text-white'
                    : 'border-transparent text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300'
                }`}
                style={activeTab === tab.id ? {
                  borderBottomColor: 'var(--color-primary, #3B82F6)',
                  color: 'var(--color-primary, #3B82F6)'
                } : {}}
              >
                <tab.icon className="w-4 h-4" />
                <span>{tab.label}</span>
              </button>
            ))}
          </nav>
        </div>

        {/* Content */}
        <div className="p-6 max-h-[60vh] overflow-y-auto">
          {activeTab === 'overview' && (
            <div className="space-y-6">
              {/* Stats Cards */}
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div className="bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-900 dark:to-blue-800 p-4 rounded-lg">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-blue-600 dark:text-blue-300 text-sm font-medium" style={{
                        color: 'var(--color-primary, #3B82F6)'
                      }}>
                        {getText('الدرجة الحالية', 'Current Grade')}
                      </p>
                      <p className="text-2xl font-bold text-blue-700 dark:text-blue-200" style={{
                        color: 'var(--color-primary, #3B82F6)'
                      }}>
                        {currentGrade}%
                      </p>
                    </div>
                    <FiTarget className="w-8 h-8 text-blue-500" style={{
                      color: 'var(--color-primary, #3B82F6)'
                    }} />
                  </div>
                </div>

                <div className="bg-gradient-to-br from-green-50 to-green-100 dark:from-green-900 dark:to-green-800 p-4 rounded-lg">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-green-600 dark:text-green-300 text-sm font-medium">
                        {getText('التغيير الشهري', 'Monthly Change')}
                      </p>
                      <div className="flex items-center space-x-1 rtl:space-x-reverse">
                        <p className={`text-2xl font-bold ${gradeChange >= 0 ? 'text-green-700 dark:text-green-200' : 'text-red-700 dark:text-red-200'}`}>
                          {gradeChange >= 0 ? '+' : ''}{gradeChange}%
                        </p>
                        {gradeChange >= 0 ? (
                          <FiTrendingUp className="w-5 h-5 text-green-500" />
                        ) : (
                          <FiTrendingDown className="w-5 h-5 text-red-500" />
                        )}
                      </div>
                    </div>
                  </div>
                </div>

                <div className="bg-gradient-to-br from-purple-50 to-purple-100 dark:from-purple-900 dark:to-purple-800 p-4 rounded-lg">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-purple-600 dark:text-purple-300 text-sm font-medium" style={{
                        color: 'var(--color-secondary, #10B981)'
                      }}>
                        {getText('المهام المكتملة', 'Completed Tasks')}
                      </p>
                      <p className="text-2xl font-bold text-purple-700 dark:text-purple-200" style={{
                        color: 'var(--color-secondary, #10B981)'
                      }}>
                        {completedTasks}/{totalTasks}
                      </p>
                    </div>
                    <FiCheckCircle className="w-8 h-8 text-purple-500" style={{
                      color: 'var(--color-secondary, #10B981)'
                    }} />
                  </div>
                </div>

                <div className="bg-gradient-to-br from-orange-50 to-orange-100 dark:from-orange-900 dark:to-orange-800 p-4 rounded-lg">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-orange-600 dark:text-orange-300 text-sm font-medium" style={{
                        color: 'var(--color-primary, #3B82F6)'
                      }}>
                        {getText('الدورات المكتملة', 'Completed Rotations')}
                      </p>
                      <p className="text-2xl font-bold text-orange-700 dark:text-orange-200" style={{
                        color: 'var(--color-primary, #3B82F6)'
                      }}>
                        {completedCourses}/{totalCourses}
                      </p>
                    </div>
                    <FiRotateCw className="w-8 h-8 text-orange-500" style={{
                      color: 'var(--color-primary, #3B82F6)'
                    }} />
                  </div>
                </div>
              </div>

              {/* Personal Info */}
              <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-6">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                  {getText('المعلومات الشخصية', 'Personal Information')}
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="flex items-center space-x-3 rtl:space-x-reverse">
                    <FiMail className="w-5 h-5 text-gray-400" />
                    <div>
                      <p className="text-sm text-gray-500 dark:text-gray-400">
                        {getText('البريد الإلكتروني', 'Email')}
                      </p>
                      <p className="font-medium text-gray-900 dark:text-white">
                        {trainee.email}
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-3 rtl:space-x-reverse">
                    <FiPhone className="w-5 h-5 text-gray-400" />
                    <div>
                      <p className="text-sm text-gray-500 dark:text-gray-400">
                        {getText('رقم الهاتف', 'Phone')}
                      </p>
                      <p className="font-medium text-gray-900 dark:text-white">
                        {trainee.phone || getText('غير محدد', 'Not specified')}
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-3 rtl:space-x-reverse">
                    <FiCalendar className="w-5 h-5 text-gray-400" />
                    <div>
                      <p className="text-sm text-gray-500 dark:text-gray-400">
                        {getText('تاريخ الانضمام', 'Join Date')}
                      </p>
                      <p className="font-medium text-gray-900 dark:text-white">
                        {new Date(trainee.created_at || trainee.joinDate || Date.now()).toLocaleDateString(language === 'ar' ? 'ar-SA' : 'en-US')}
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-3 rtl:space-x-reverse">
                    <FiUser className="w-5 h-5 text-gray-400" />
                    <div>
                      <p className="text-sm text-gray-500 dark:text-gray-400">
                        {getText('الجنس', 'Gender')}
                      </p>
                      <p className="font-medium text-gray-900 dark:text-white">
                        {trainee.gender === 'male' 
                          ? getText('ذكر', 'Male') 
                          : trainee.gender === 'female' 
                          ? getText('أنثى', 'Female') 
                          : getText('غير محدد', 'Not specified')
                        }
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'performance' && (
            <div className="space-y-6">
              {/* Performance Overview Cards */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                <div className="bg-gradient-to-r from-blue-500 to-blue-600 rounded-lg p-4 text-white">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-blue-100 text-sm">{getText('المتوسط العام', 'Overall Average')}</p>
                      <p className="text-2xl font-bold">
                        {currentGrade || 0}%
                      </p>
                    </div>
                    <FiTarget className="w-8 h-8 text-blue-200" />
                  </div>
                </div>

                <div className="bg-gradient-to-r from-green-500 to-green-600 rounded-lg p-4 text-white">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-green-100 text-sm">{getText('المهام المكتملة', 'Completed Tasks')}</p>
                      <p className="text-2xl font-bold">
                        {completedTasks}
                      </p>
                    </div>
                    <FiTrendingUp className="w-8 h-8 text-green-200" />
                  </div>
                </div>

                <div className="bg-gradient-to-r from-purple-500 to-purple-600 rounded-lg p-4 text-white">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-purple-100 text-sm">{getText('ورش العمل', 'Workshops')}</p>
                      <p className="text-2xl font-bold">
                        {attendedWorkshops}
                      </p>
                    </div>
                    <FiActivity className="w-8 h-8 text-purple-200" />
                  </div>
                </div>
              </div>

              {/* Performance Chart - عرض فقط عند وجود بيانات */}
              {traineeGrades.length > 0 ? (
                <div className="bg-white dark:bg-gray-800 rounded-lg p-6 border border-gray-200 dark:border-gray-700">
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center">
                    <FiTrendingUp className="w-5 h-5 mr-2 rtl:ml-2 rtl:mr-0" />
                    {getText('تطور الأداء الشهري', 'Monthly Performance Trend')}
                  </h3>
                  <SimpleLineChart
                    title=""
                    data={monthlyPerformance}
                    color="var(--color-primary, #3B82F6)"
                  />
                </div>
              ) : (
                <div className="bg-white dark:bg-gray-800 rounded-lg p-6 border border-gray-200 dark:border-gray-700 text-center">
                  <FiTrendingUp className="w-12 h-12 text-gray-400 mx-auto mb-3" />
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                    {getText('لا توجد بيانات أداء', 'No Performance Data')}
                  </h3>
                  <p className="text-gray-500 dark:text-gray-400">
                    {getText('لم يتم تسجيل أي درجات أو تقييمات للمتدرب بعد', 'No grades or evaluations have been recorded for this trainee yet')}
                  </p>
                </div>
              )}
              
              {/* Detailed Performance Analysis */}
              <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-6">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center">
                  <FiActivity className="w-5 h-5 mr-2 rtl:ml-2 rtl:mr-0" />
                  {getText('تحليل الأداء التفصيلي', 'Detailed Performance Analysis')}
                </h3>
                
                <div className="space-y-4">
                  {/* Performance Metrics */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-3">
                      <h4 className="font-medium text-gray-900 dark:text-white">
                        {getText('مقاييس الأداء الأساسية', 'Core Performance Metrics')}
                      </h4>
                      <div className="space-y-2">
                        {traineeGrades.length > 0 && (
                          <div className="flex justify-between items-center">
                            <span className="text-gray-600 dark:text-gray-400 text-sm">
                              {getText('متوسط الدرجات', 'Average Grades')}
                            </span>
                            <span className="font-semibold text-blue-600 dark:text-blue-400">
                              {currentGrade}%
                            </span>
                          </div>
                        )}
                        <div className="flex justify-between items-center">
                          <span className="text-gray-600 dark:text-gray-400 text-sm">
                            {getText('معدل إنجاز المهام', 'Task Completion Rate')}
                          </span>
                          <span className="font-semibold text-green-600 dark:text-green-400">
                            {totalTasks > 0 ? Math.round((completedTasks / totalTasks) * 100) : 0}%
                          </span>
                        </div>
                        <div className="flex justify-between items-center">
                          <span className="text-gray-600 dark:text-gray-400 text-sm">
                            {getText('ورش العمل المكتملة', 'Completed Workshops')}
                          </span>
                          <span className="font-semibold text-purple-600 dark:text-purple-400">
                            {attendedWorkshops}
                          </span>
                        </div>
                      </div>
                    </div>
                    
                    <div className="space-y-3">
                      <h4 className="font-medium text-gray-900 dark:text-white">
                        {getText('تحليل الاتجاهات', 'Trend Analysis')}
                      </h4>
                      <div className="space-y-2">
                        <div className="flex justify-between items-center">
                          <span className="text-gray-600 dark:text-gray-400 text-sm">
                            {getText('الاتجاه العام', 'Overall Trend')}
                          </span>
                          <span className="font-semibold text-green-600 dark:text-green-400 flex items-center">
                            <FiTrendingUp className="w-4 h-4 mr-1 rtl:ml-1 rtl:mr-0" />
                            {getText('تحسن مستمر', 'Improving')}
                          </span>
                        </div>
                        <div className="flex justify-between items-center">
                          <span className="text-gray-600 dark:text-gray-400 text-sm">
                            {getText('أفضل شهر', 'Best Month')}
                          </span>
                          <span className="font-semibold text-blue-600 dark:text-blue-400">
                            {monthlyPerformance.find(m => m.value === Math.max(...monthlyPerformance.map(p => p.value)))?.label || getText('الشهر الحالي', 'Current Month')}
                          </span>
                        </div>
                        <div className="flex justify-between items-center">
                          <span className="text-gray-600 dark:text-gray-400 text-sm">
                            {getText('نقاط القوة', 'Strengths')}
                          </span>
                          <span className="font-semibold text-green-600 dark:text-green-400">
                            {getText('ورش العمل والمهام', 'Workshops & Tasks')}
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                  
                  {/* Performance Insights */}
                  <div className="mt-6 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg border-l-4 border-blue-500">
                    <h4 className="font-medium text-blue-900 dark:text-blue-100 mb-2 flex items-center">
                      <FiAward className="w-4 h-4 mr-2 rtl:ml-2 rtl:mr-0" />
                      {getText('رؤى الأداء', 'Performance Insights')}
                    </h4>
                    <p className="text-blue-800 dark:text-blue-200 text-sm">
                      {language === 'ar' 
                        ? 'يُظهر المتدرب تحسناً مستمراً في الأداء بناءً على ورش العمل والمهام والدورات التدريبية. الأداء محسوب من خلال الحضور في ورش العمل وإنجاز المهام وإكمال الدورات التدريبية.'
                        : 'The trainee shows consistent improvement in performance based on workshops, tasks, and training rotations. Performance is calculated through workshop attendance, task completion, and training rotation progress.'
                      }
                    </p>
                  </div>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'tasks' && (
            <div className="space-y-6">
              {/* عرض الرسم البياني فقط عند وجود بيانات */}
              {(workshopScore > 0 || taskScore > 0 || courseScore > 0) ? (
                <SimpleBarChart
                  title={getText('الأداء حسب النوع', 'Performance by Type')}
                  data={performanceByType.filter(item => item.value > 0)}
                  color="var(--color-secondary, #10B981)"
                />
              ) : (
                <div className="text-center py-8">
                  <FiBarChart className="w-12 h-12 text-gray-400 mx-auto mb-3" />
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                    {getText('لا توجد بيانات أداء', 'No Performance Data')}
                  </h3>
                  <p className="text-gray-500 dark:text-gray-400">
                    {getText('لم يتم تسجيل أي أنشطة أو تقييمات بعد', 'No activities or evaluations have been recorded yet')}
                  </p>
                </div>
              )}

              {/* عرض تفاصيل الأداء فقط عند وجود بيانات */}
              {(workshopScore > 0 || taskScore > 0 || courseScore > 0) && (
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                    {getText('تفاصيل الأداء', 'Performance Details')}
                  </h3>
                  {performanceByType.filter(item => item.value > 0).map((item, index) => (
                  <div key={index} className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-3 rtl:space-x-reverse">
                        {index === 0 && <FiUsers className="w-5 h-5 text-blue-500" style={{ color: 'var(--color-primary, #3B82F6)' }} />}
                        {index === 1 && <FiCheckCircle className="w-5 h-5 text-green-500" style={{ color: 'var(--color-secondary, #10B981)' }} />}
                        {index === 2 && <FiRotateCw className="w-5 h-5 text-purple-500" style={{ color: 'var(--color-primary, #3B82F6)' }} />}
                        <div>
                          <p className="font-medium text-gray-900 dark:text-white">
                            {item.label}
                          </p>
                          <p className="text-sm text-gray-500 dark:text-gray-400">
                            {index === 0 && getText(`${attendedWorkshops}/${totalWorkshops} مكتملة`, `${attendedWorkshops}/${totalWorkshops} completed`)}
                            {index === 1 && getText(`${completedTasks}/${totalTasks} مكتملة`, `${completedTasks}/${totalTasks} completed`)}
                            {index === 2 && getText(`${completedCourses}/${totalCourses} مكتملة`, `${completedCourses}/${totalCourses} completed`)}
                          </p>
                        </div>
                      </div>
                      <div className="text-right">
                        <p className="font-bold text-lg text-gray-900 dark:text-white">
                          {item.value}%
                        </p>
                        <p className="text-sm text-gray-500 dark:text-gray-400">
                          {getText('الأداء', 'Performance')}
                        </p>
                      </div>
                    </div>
                  </div>
                  ))}
                </div>
              )}
            </div>
          )}

          {activeTab === 'courses' && (
            <div className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {traineeCourses.length > 0 ? traineeCourses.map((course, index) => (
                  <div key={index} className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                    <div className="flex items-center justify-between mb-3">
                      <h4 className="font-medium text-gray-900 dark:text-white">
                        {course.courseName || course.title || course.name}
                      </h4>
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                        course.status === 'completed'
                          ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                          : course.status === 'active'
                          ? 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200'
                          : 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200'
                      }`}>
                        {course.status === 'completed' ? getText('مكتملة', 'Completed') :
                         course.status === 'active' ? getText('نشطة', 'Active') :
                         getText('قادمة', 'Upcoming')}
                      </span>
                    </div>
                    <div className="space-y-2 text-sm text-gray-600 dark:text-gray-400">
                      <div className="flex items-center">
                        <FiCalendar className="w-4 h-4 mr-2 rtl:ml-2 rtl:mr-0" />
                        <span>
                          {course.startDate ? new Date(course.startDate).toLocaleDateString(
                            language === 'ar' ? 'ar-SA' : 'en-US'
                          ) : getText('غير محدد', 'Not specified')}
                        </span>
                      </div>
                      {course.department && (
                        <div className="flex items-center">
                          <FiBook className="w-4 h-4 mr-2 rtl:ml-2 rtl:mr-0" />
                          <span>{course.department}</span>
                        </div>
                      )}
                    </div>
                  </div>
                )) : (
                  <div className="col-span-2 text-center py-8">
                    <FiBook className="w-12 h-12 text-gray-400 mx-auto mb-3" />
                    <p className="text-gray-500 dark:text-gray-400">
                      {getText('لا توجد دورات مسجلة', 'No courses registered')}
                    </p>
                  </div>
                )}
              </div>
            </div>
          )}

          {activeTab === 'workshops' && (
            <div className="space-y-6">
              {/* عرض ورش العمل الحقيقية */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {traineeWorkshops.length > 0 ? traineeWorkshops.map((workshop, index) => (
                  <div key={index} className="bg-white dark:bg-gray-700 rounded-lg p-4 border border-gray-200 dark:border-gray-600">
                    <div className="flex items-start justify-between mb-3">
                      <h4 className="font-semibold text-gray-900 dark:text-white text-sm">
                        {workshop.title || workshop.name}
                      </h4>
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                        workshop.status === 'completed'
                          ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                          : workshop.status === 'active'
                          ? 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200'
                          : 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200'
                      }`}>
                        {workshop.status === 'completed' ? getText('مكتملة', 'Completed') :
                         workshop.status === 'active' ? getText('نشطة', 'Active') :
                         getText('قادمة', 'Upcoming')}
                      </span>
                    </div>
                    <div className="space-y-2 text-sm text-gray-600 dark:text-gray-400">
                      <div className="flex items-center">
                        <FiCalendar className="w-4 h-4 mr-2 rtl:ml-2 rtl:mr-0" />
                        <span>
                          {workshop.startDate ? new Date(workshop.startDate).toLocaleDateString(
                            language === 'ar' ? 'ar-SA' : 'en-US'
                          ) : getText('غير محدد', 'Not specified')}
                        </span>
                      </div>
                      <div className="flex items-center">
                        <FiClock className="w-4 h-4 mr-2 rtl:ml-2 rtl:mr-0" />
                        <span>
                          {workshop.duration ? `${workshop.duration} ${getText('ساعة', 'hours')}` :
                           getText('غير محدد', 'Not specified')}
                        </span>
                      </div>
                    </div>
                  </div>
                )) : (
                  <div className="col-span-2 text-center py-8">
                    <FiBook className="w-12 h-12 text-gray-400 mx-auto mb-3" />
                    <p className="text-gray-500 dark:text-gray-400">
                      {getText('لا توجد ورش عمل مسجلة', 'No workshops registered')}
                    </p>
                  </div>
                )}

              </div>
            </div>
          )}
        </div>
      </motion.div>
    </div>
  )
}

export default TraineeDetailModal