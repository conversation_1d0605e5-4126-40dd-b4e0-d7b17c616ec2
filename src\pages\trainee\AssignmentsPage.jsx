import React, { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { useNavigate } from 'react-router-dom'
import { useAuth } from '../../contexts/AuthContext'
import { useLanguage } from '../../contexts/LanguageContext'
import toast from 'react-hot-toast'
import TaskSubmissionModal from '../../components/modals/TaskSubmissionModal'
import TraineeHeader from '../../components/trainee/TraineeHeader'
import StatsCard from '../../components/trainee/StatsCard'
import SearchAndFilter from '../../components/trainee/SearchAndFilter'
import Button from '../../components/ui/Button'
import {
  CheckSquare,
  Clock,
  Calendar,
  FileText,
  Upload,
  Download,
  Play,
  Eye,
  AlertCircle,
  Award,
  Filter,
  Search,
  BookOpen,
  User,
  Video,
  Mic,
  ArrowLeft,
  CheckCircle,
  Timer,
  Target,
  Code,
  Database,
  Palette,
  Globe,
  Smartphone,
  Cpu,
  Briefcase,
  GraduationCap,
  Lightbulb,
  Presentation,
  Zap,
  PenTool,
  Calculator,
  Brain,
  Layers
} from 'lucide-react'

const AssignmentsPage = () => {
  const { user } = useAuth()
  const { t, language } = useLanguage()
  const navigate = useNavigate()
  const [selectedTab, setSelectedTab] = useState('all')
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedAssignment, setSelectedAssignment] = useState(null)
  const [assignments, setAssignments] = useState([])
  const [isLoading, setIsLoading] = useState(true)
  const [stats, setStats] = useState({
    totalTasks: 0,
    completedTasks: 0,
    pendingTasks: 0,
    overdueTasks: 0
  })

  // تحميل البيانات الحقيقية من localStorage
  useEffect(() => {
    // تحميل المهام الحقيقية من localStorage
    const allTasks = JSON.parse(localStorage.getItem('app_tasks') || '[]')
    const userTasks = allTasks.filter(task =>
      task.assignedUsers?.includes(user?.name) ||
      task.assignedUsers?.includes(user?.id) ||
      task.assignedUsers?.includes('الكل')
    )

    setAssignments(userTasks)

    // حساب الإحصائيات
    const completedTasks = userTasks.filter(task => task.status === 'graded' || task.status === 'completed').length
    const pendingTasks = userTasks.filter(task => task.status === 'pending').length
    const overdueTasks = userTasks.filter(task => {
      const dueDate = new Date(task.dueDate)
      const now = new Date()
      return dueDate < now && task.status !== 'graded' && task.status !== 'completed'
    }).length

    setStats({
      totalTasks: userTasks.length,
      completedTasks,
      pendingTasks,
      overdueTasks
    })

    setIsLoading(false)
  }, [user])

  const getDaysUntilDue = (dueDate) => {
    const due = new Date(dueDate)
    const now = new Date()
    const diffTime = due - now
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24))
  }

  const getPriorityColor = (priority) => {
    switch (priority) {
      case 'high':
        return 'border-l-4 border-red-500'
      case 'medium':
        return 'border-l-4 border-yellow-500'
      case 'low':
        return 'border-l-4 border-green-500'
      default:
        return 'border-l-4 border-gray-500'
    }
  }

  const getStatusColor = (status) => {
    switch (status) {
      case 'pending':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400'
      case 'in_progress':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400'
      case 'submitted':
        return 'bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-400'
      case 'graded':
        return 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400'
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-400'
    }
  }

  const getStatusIcon = (status) => {
    switch (status) {
      case 'pending':
        return <Clock className="w-3 h-3 ml-1" />
      case 'in_progress':
        return <Play className="w-3 h-3 ml-1" />
      case 'submitted':
        return <Upload className="w-3 h-3 ml-1" />
      case 'graded':
        return <CheckCircle className="w-3 h-3 ml-1" />
      default:
        return <AlertCircle className="w-3 h-3 ml-1" />
    }
  }

  const getTaskIcon = (title, course) => {
    const titleLower = (title || '').toLowerCase()
    const courseLower = (course || '').toLowerCase()

    // أيقونات حسب نوع المهمة
    if (titleLower.includes('برمجة') || titleLower.includes('كود') || titleLower.includes('تطوير') || courseLower.includes('programming')) {
      return <Code className="w-4 h-4" />
    }
    if (titleLower.includes('تصميم') || titleLower.includes('جرافيك') || courseLower.includes('design')) {
      return <Palette className="w-4 h-4" />
    }
    if (titleLower.includes('بيانات') || titleLower.includes('قاعدة') || titleLower.includes('تحليل') || courseLower.includes('data')) {
      return <Database className="w-4 h-4" />
    }
    if (titleLower.includes('ويب') || titleLower.includes('موقع') || courseLower.includes('web')) {
      return <Globe className="w-4 h-4" />
    }
    if (titleLower.includes('جوال') || titleLower.includes('تطبيق') || titleLower.includes('موبايل') || courseLower.includes('mobile')) {
      return <Smartphone className="w-4 h-4" />
    }
    if (titleLower.includes('ذكي') || titleLower.includes('ai') || titleLower.includes('machine') || courseLower.includes('artificial')) {
      return <Brain className="w-4 h-4" />
    }
    if (titleLower.includes('إدارة') || titleLower.includes('أعمال') || courseLower.includes('business')) {
      return <Briefcase className="w-4 h-4" />
    }
    if (titleLower.includes('تقرير') || titleLower.includes('كتابة') || titleLower.includes('مقال')) {
      return <PenTool className="w-4 h-4" />
    }
    if (titleLower.includes('حساب') || titleLower.includes('رياضيات') || titleLower.includes('إحصاء')) {
      return <Calculator className="w-4 h-4" />
    }
    if (titleLower.includes('تقديم') || titleLower.includes('عرض') || titleLower.includes('presentation')) {
      return <Presentation className="w-4 h-4" />
    }
    if (titleLower.includes('مشروع') || titleLower.includes('project')) {
      return <Layers className="w-4 h-4" />
    }

    // أيقونة افتراضية للمهام
    return <Target className="w-4 h-4" />
  }

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600 dark:text-gray-400">{t('tasks.loading')}</p>
        </div>
      </div>
    )
  }

  // تصفية المهام بناءً على البحث والفلتر
  const filteredAssignments = assignments.filter(assignment => {
    const matchesSearch = assignment.title?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         assignment.course?.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesTab = selectedTab === 'all' || assignment.status === selectedTab
    return matchesSearch && matchesTab
  })

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        
        {/* Header */}
        <TraineeHeader 
          title={t('tasks.myTasks')}
          subtitle={t('tasks.myTasksSubtitle')}
          icon={CheckSquare}
          iconBgColor="bg-green-100 dark:bg-green-900/30"
          iconColor="text-green-600 dark:text-green-400"
        />

        {/* Stats Cards */}
        <div className="grid grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
          <StatsCard
            icon={FileText}
            value={stats.totalTasks}
            label={t('tasks.totalTasksCount')}
            subtitle={t('tasks.assignedTask')}
            iconBgColor="bg-blue-100 dark:bg-blue-900/30"
            iconColor="text-blue-600 dark:text-blue-400"
            onClick={() => setSelectedTab('all')}
            delay={0.1}
          />
          <StatsCard
            icon={CheckCircle}
            value={stats.completedTasks}
            label="مهام مكتملة"
            subtitle="مهمة منجزة"
            iconBgColor="bg-green-100 dark:bg-green-900/30"
            iconColor="text-green-600 dark:text-green-400"
            onClick={() => setSelectedTab('graded')}
            delay={0.2}
          />
          <StatsCard
            icon={Clock}
            value={stats.pendingTasks}
            label="مهام معلقة"
            subtitle="مهمة متبقية"
            iconBgColor="bg-orange-100 dark:bg-orange-900/30"
            iconColor="text-orange-600 dark:text-orange-400"
            onClick={() => setSelectedTab('pending')}
            delay={0.3}
          />
          <StatsCard
            icon={AlertCircle}
            value={stats.overdueTasks}
            label="مهام متأخرة"
            subtitle="مهمة متأخرة"
            iconBgColor="bg-red-100 dark:bg-red-900/30"
            iconColor="text-red-600 dark:text-red-400"
            delay={0.4}
          />
        </div>

        {/* Search and Filter */}
        <SearchAndFilter
          searchPlaceholder={t('tasks.searchTasks')}
          filterOptions={[
            { value: "all", label: t('tasks.allTasks') },
            { value: "pending", label: t('tasks.pendingTasks') },
            { value: "in_progress", label: t('tasks.inProgressTasks') },
            { value: "graded", label: t('tasks.completedTasks') }
          ]}
          onSearch={setSearchTerm}
          onFilter={setSelectedTab}
          searchValue={searchTerm}
          filterValue={selectedTab}
        />

        {/* Assignments Grid */}
        <div className="mb-8">
          {filteredAssignments.length > 0 ? (
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {filteredAssignments.map((assignment, index) => {
                const daysUntilDue = getDaysUntilDue(assignment.dueDate)
                const isOverdue = daysUntilDue < 0 && assignment.status !== 'submitted' && assignment.status !== 'graded'

                return (
                  <motion.div
                    key={assignment.id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: index * 0.1 }}
                    className={`bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700 hover:shadow-md transition-all duration-300 ${getPriorityColor(assignment.priority)} ${isOverdue ? 'ring-2 ring-red-500' : ''}`}
                  >
                    {/* Assignment Header */}
                    <div className="flex items-center justify-between mb-4">
                      <div className="flex items-center space-x-2 rtl:space-x-reverse">
                        <div className="p-2 bg-blue-100 dark:bg-blue-900/30 rounded-lg">
                          <div className="text-blue-600 dark:text-blue-400">
                            {getTaskIcon(assignment.title, assignment.course)}
                          </div>
                        </div>
                        <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(assignment.status)}`}>
                          {getStatusIcon(assignment.status)}
                          <span className="mr-1 rtl:mr-0 rtl:ml-1">
                            {assignment.status === 'pending' ? 'معلقة' :
                             assignment.status === 'in_progress' ? 'جارية' :
                             assignment.status === 'submitted' ? 'مُسلمة' :
                             assignment.status === 'graded' ? 'مُقيمة' : assignment.status}
                          </span>
                        </span>
                      </div>
                      {assignment.priority && (
                        <div className={`text-xs px-2 py-1 rounded-full ${
                          assignment.priority === 'high' ? 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400' :
                          assignment.priority === 'medium' ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400' :
                          'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400'
                        }`}>
                          {assignment.priority === 'high' ? 'عالية' :
                           assignment.priority === 'medium' ? 'متوسطة' : 'منخفضة'}
                        </div>
                      )}
                    </div>

                    {/* Assignment Title */}
                    <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2">
                      {assignment.title}
                    </h3>

                    {/* Assignment Description */}
                    <p className="text-sm text-gray-600 dark:text-gray-400 mb-4 line-clamp-2">
                      {assignment.description}
                    </p>

                    {/* Assignment Details */}
                    <div className="space-y-2 mb-4">
                      <div className="flex items-center text-sm text-gray-500 dark:text-gray-400">
                        <BookOpen className="w-4 h-4 ml-2" />
                        {assignment.course}
                      </div>
                      <div className="flex items-center text-sm text-gray-500 dark:text-gray-400">
                        <User className="w-4 h-4 ml-2" />
                        {assignment.instructor}
                      </div>
                      <div className="flex items-center text-sm text-gray-500 dark:text-gray-400">
                        <Calendar className="w-4 h-4 ml-2" />
                        {t('tasks.dueDate')}: {new Date(assignment.dueDate).toLocaleDateString(language === 'en' ? 'en-US' : 'ar-SA')}
                      </div>
                      {isOverdue && (
                        <div className="flex items-center text-red-600 dark:text-red-400 text-sm">
                          <AlertCircle className="w-4 h-4 ml-2" />
                          {t('tasks.overdueDays', { days: Math.abs(daysUntilDue) })}
                        </div>
                      )}
                    </div>

                    {/* Score Display */}
                    {assignment.score && (
                      <div className="mb-4 p-3 bg-green-50 dark:bg-green-900/20 rounded-lg">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center">
                            <Award className="w-4 h-4 text-green-600 dark:text-green-400 ml-2" />
                            <span className="text-green-800 dark:text-green-200 font-medium text-sm">
                              النتيجة: {assignment.score}/{assignment.maxScore}
                            </span>
                          </div>
                          <div className="text-green-600 dark:text-green-400 font-semibold text-sm">
                            {Math.round((assignment.score / assignment.maxScore) * 100)}%
                          </div>
                        </div>
                      </div>
                    )}

                    {/* Feedback */}
                    {assignment.feedback && (
                      <div className="mb-4 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                        <p className="text-blue-800 dark:text-blue-200 text-sm">
                          <strong>تعليقات المدرب:</strong> {assignment.feedback}
                        </p>
                      </div>
                    )}

                    {/* Action Buttons */}
                    <div className="flex gap-2">
                      <Button
                        variant="primary"
                        size="sm"
                        onClick={() => navigate(`/task/${assignment.id}`)}
                        className="flex-1"
                        icon={Eye}
                      >
                        عرض التفاصيل
                      </Button>
                      {assignment.status === 'pending' && (
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => {
                            // بدء المهمة
                            const updatedAssignments = assignments.map(a => 
                              a.id === assignment.id ? { ...a, status: 'in_progress' } : a
                            )
                            setAssignments(updatedAssignments)
                            toast.success('تم بدء المهمة!')
                          }}
                          icon={Play}
                        >
                          بدء
                        </Button>
                      )}
                      {assignment.status === 'in_progress' && (
                        <Button
                          variant="success"
                          size="sm"
                          onClick={() => setSelectedAssignment(assignment)}
                          icon={Upload}
                        >
                          {t('tasks.submit')}
                        </Button>
                      )}
                    </div>
                  </motion.div>
                )
              })}
            </div>
          ) : (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="bg-white dark:bg-gray-800 rounded-xl p-12 shadow-sm border border-gray-200 dark:border-gray-700 text-center"
            >
              <div className="flex justify-center mb-4">
                <div className="p-4 bg-gray-100 dark:bg-gray-700 rounded-full">
                  <CheckSquare className="h-12 w-12 text-gray-400" />
                </div>
              </div>
              <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
                لا توجد مهام
              </h3>
              <p className="text-gray-500 dark:text-gray-400 mb-6">
                {selectedTab === 'all' ? 'لم يتم تكليفك بأي مهام بعد.' : `لا توجد مهام ${selectedTab}.`}
              </p>
              <Button
                onClick={() => navigate('/timeline')}
                variant="primary"
                icon={Calendar}
                className="mt-4"
              >
                عرض التايم لاين
              </Button>
            </motion.div>
          )}
        </div>

        {/* Task Submission Modal */}
        {selectedAssignment && (
          <TaskSubmissionModal
            isOpen={true}
            onClose={() => setSelectedAssignment(null)}
            task={selectedAssignment}
            onSubmissionComplete={(submission) => {
              console.log('✅ تم تسليم المهمة:', submission)
              // تحديث حالة المهمة
              const updatedAssignments = assignments.map(a => 
                a.id === selectedAssignment.id ? { ...a, status: 'submitted' } : a
              )
              setAssignments(updatedAssignments)
              setSelectedAssignment(null)
              toast.success(t('tasks.submitSuccess'))
            }}
          />
        )}
      </div>
    </div>
  )
}

export default AssignmentsPage
