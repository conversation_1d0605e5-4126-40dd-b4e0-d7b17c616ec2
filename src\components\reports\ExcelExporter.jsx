import React, { useState, useEffect } from 'react'
import * as XLSX from 'xlsx'
import { Download, Users, User, FileSpreadsheet, Palette } from 'lucide-react'
import Button from '../ui/Button'
import toast from 'react-hot-toast'

const ExcelExporter = () => {
  const [isExporting, setIsExporting] = useState(false)

  // الحصول على ألوان النظام المحسنة
  const getSystemColors = () => {
    try {
      const root = document.documentElement
      const computedStyle = getComputedStyle(root)

      // محاولة الحصول على الألوان من CSS variables
      let primaryColor = computedStyle.getPropertyValue('--color-primary-500').trim()
      let secondaryColor = computedStyle.getPropertyValue('--color-secondary-500').trim()

      // إذا لم نجد الألوان، استخدم ألوان افتراضية جميلة
      if (!primaryColor || primaryColor === '') {
        primaryColor = '#3B82F6' // أزرق جميل
      }
      if (!secondaryColor || secondaryColor === '') {
        secondaryColor = '#8B5CF6' // بنفسجي جميل
      }

      // تحويل ألوان CSS إلى hex للاستخدام في Excel
      const convertToHex = (color) => {
        if (!color) return '3B82F6'

        if (color.startsWith('#')) {
          return color.substring(1).toUpperCase()
        }

        if (color.startsWith('rgb')) {
          const matches = color.match(/\d+/g)
          if (matches && matches.length >= 3) {
            const r = parseInt(matches[0]).toString(16).padStart(2, '0')
            const g = parseInt(matches[1]).toString(16).padStart(2, '0')
            const b = parseInt(matches[2]).toString(16).padStart(2, '0')
            return (r + g + b).toUpperCase()
          }
        }

        // ألوان افتراضية جميلة
        return '3B82F6'
      }

      return {
        primary: convertToHex(primaryColor),
        secondary: convertToHex(secondaryColor),
        success: '10B981',
        warning: 'F59E0B',
        error: 'EF4444',
        info: '06B6D4',
        light: 'F8FAFC',
        dark: '1E293B'
      }
    } catch (error) {
      console.error('خطأ في الحصول على الألوان:', error)
      // ألوان افتراضية جميلة في حالة الخطأ
      return {
        primary: '3B82F6',
        secondary: '8B5CF6',
        success: '10B981',
        warning: 'F59E0B',
        error: 'EF4444',
        info: '06B6D4',
        light: 'F8FAFC',
        dark: '1E293B'
      }
    }
  }

  // تطبيق التنسيق المتقدم على الورقة
  const applyAdvancedFormatting = (worksheet, colors) => {
    if (!worksheet['!ref']) return

    const range = XLSX.utils.decode_range(worksheet['!ref'])

    // تنسيق العناوين (الصف الأول)
    for (let col = range.s.c; col <= range.e.c; col++) {
      const cellAddress = XLSX.utils.encode_cell({ r: 0, c: col })
      if (!worksheet[cellAddress]) continue

      worksheet[cellAddress].s = {
        fill: { fgColor: { rgb: colors.primary } },
        font: {
          bold: true,
          color: { rgb: 'FFFFFF' },
          sz: 14,
          name: 'Arial'
        },
        alignment: {
          horizontal: 'center',
          vertical: 'center',
          wrapText: true
        },
        border: {
          top: { style: 'medium', color: { rgb: colors.primary } },
          bottom: { style: 'medium', color: { rgb: colors.primary } },
          left: { style: 'medium', color: { rgb: colors.primary } },
          right: { style: 'medium', color: { rgb: colors.primary } }
        }
      }
    }

    // تنسيق البيانات (باقي الصفوف)
    for (let row = 1; row <= range.e.r; row++) {
      for (let col = range.s.c; col <= range.e.c; col++) {
        const cellAddress = XLSX.utils.encode_cell({ r: row, c: col })
        if (!worksheet[cellAddress]) continue

        const isEvenRow = row % 2 === 0
        worksheet[cellAddress].s = {
          fill: {
            fgColor: {
              rgb: isEvenRow ? colors.light : 'FFFFFF'
            }
          },
          font: {
            sz: 11,
            name: 'Arial',
            color: { rgb: colors.dark }
          },
          alignment: {
            horizontal: col === 0 ? 'right' : 'center',
            vertical: 'center',
            wrapText: true
          },
          border: {
            top: { style: 'thin', color: { rgb: 'D1D5DB' } },
            bottom: { style: 'thin', color: { rgb: 'D1D5DB' } },
            left: { style: 'thin', color: { rgb: 'D1D5DB' } },
            right: { style: 'thin', color: { rgb: 'D1D5DB' } }
          }
        }
      }
    }

    // تعيين ارتفاع الصفوف
    if (!worksheet['!rows']) worksheet['!rows'] = []
    worksheet['!rows'][0] = { hpt: 30 } // ارتفاع صف العناوين
    for (let i = 1; i <= range.e.r; i++) {
      worksheet['!rows'][i] = { hpt: 22 } // ارتفاع صفوف البيانات
    }

    // إضافة تجميد للصف الأول
    worksheet['!freeze'] = { xSplit: 0, ySplit: 1 }
  }

  // تحميل البيانات الحقيقية
  const loadRealData = () => {
    try {
      const users = JSON.parse(localStorage.getItem('app_users') || '[]')
      const courses = JSON.parse(localStorage.getItem('app_courses') || '[]')
      const workshops = JSON.parse(localStorage.getItem('workshops_data') || '[]')
      const tasks = JSON.parse(localStorage.getItem('tasks_data') || '[]')
      const timelineEvents = JSON.parse(localStorage.getItem('timeline_events') || '[]')

      return {
        users,
        courses,
        workshops,
        tasks,
        timelineEvents
      }
    } catch (error) {
      console.error('خطأ في تحميل البيانات:', error)
      return {
        users: [],
        courses: [],
        workshops: [],
        tasks: [],
        timelineEvents: []
      }
    }
  }

  // تصدير متدرب واحد
  const exportSingleTrainee = async (trainee) => {
    setIsExporting(true)
    try {
      const data = loadRealData()
      const colors = getSystemColors()

      // إنشاء workbook جديد
      const workbook = XLSX.utils.book_new()

      // حساب الإحصائيات المفصلة
      const traineeStats = {
        courses: data.courses.filter(c => c.selectedTrainees?.includes(trainee.name)),
        workshops: data.workshops.filter(w => w.participants?.includes(trainee.name)),
        tasks: data.tasks.filter(t => t.assignedUsers?.includes(trainee.name)),
        completedTasks: data.tasks.filter(t => t.assignedUsers?.includes(trainee.name) && t.status === 'completed'),
        grades: JSON.parse(localStorage.getItem('task_grades') || '{}')
      }

      // حساب المعدل التراكمي
      const calculateGPA = () => {
        const gradingSettings = JSON.parse(localStorage.getItem('global_grading_settings') || '{}')
        const maxGrade = gradingSettings.maxGrade || 100

        let totalGrades = 0
        let gradeCount = 0

        traineeStats.tasks.forEach(task => {
          const taskGrades = traineeStats.grades[task.id] || {}
          Object.values(taskGrades).forEach(grade => {
            if (grade && !isNaN(grade)) {
              totalGrades += (parseFloat(grade) / maxGrade) * 100
              gradeCount++
            }
          })
        })

        return gradeCount > 0 ? (totalGrades / gradeCount).toFixed(2) : 'غير محدد'
      }

      // ورقة المعلومات الأساسية
      const basicInfo = [
        ['تقرير شامل للمتدرب', ''],
        ['', ''],
        ['المعلومات الشخصية', ''],
        ['الاسم الكامل', trainee.name || 'غير محدد'],
        ['البريد الإلكتروني', trainee.email || 'غير محدد'],
        ['رقم الهاتف', trainee.phone || 'غير محدد'],
        ['القسم/الإدارة', trainee.department || 'غير محدد'],
        ['تاريخ التسجيل', trainee.createdAt ? new Date(trainee.createdAt).toLocaleDateString('ar-SA') : 'غير محدد'],
        ['حالة الحساب', trainee.status === 'active' ? 'نشط' : 'غير نشط'],
        ['', ''],
        ['الإحصائيات العامة', ''],
        ['إجمالي الدورات المسجل بها', traineeStats.courses.length],
        ['إجمالي ورش العمل المشارك بها', traineeStats.workshops.length],
        ['إجمالي المهام المخصصة', traineeStats.tasks.length],
        ['المهام المكتملة', traineeStats.completedTasks.length],
        ['نسبة إنجاز المهام', traineeStats.tasks.length > 0 ? `${Math.round((traineeStats.completedTasks.length / traineeStats.tasks.length) * 100)}%` : '0%'],
        ['المعدل التراكمي', `${calculateGPA()}%`],
        ['', ''],
        ['تاريخ إنشاء التقرير', new Date().toLocaleDateString('ar-SA')],
        ['وقت إنشاء التقرير', new Date().toLocaleTimeString('ar-SA')]
      ]

      const basicSheet = XLSX.utils.aoa_to_sheet(basicInfo)

      // تنسيق ورقة المعلومات الأساسية
      basicSheet['!cols'] = [{ width: 30 }, { width: 35 }]

      // تطبيق التنسيق المتقدم
      const range = XLSX.utils.decode_range(basicSheet['!ref'])

      // تنسيق العناوين الرئيسية
      const headerCells = ['A1', 'A3', 'A11', 'A18']
      headerCells.forEach(cell => {
        if (basicSheet[cell]) {
          basicSheet[cell].s = {
            fill: { fgColor: { rgb: colors.primary } },
            font: { bold: true, color: { rgb: 'FFFFFF' }, sz: 14, name: 'Arial' },
            alignment: { horizontal: 'center', vertical: 'center' }
          }
        }
      })

      // دمج خلايا العناوين
      if (!basicSheet['!merges']) basicSheet['!merges'] = []
      basicSheet['!merges'].push(
        { s: { r: 0, c: 0 }, e: { r: 0, c: 1 } }, // تقرير شامل للمتدرب
        { s: { r: 2, c: 0 }, e: { r: 2, c: 1 } }, // المعلومات الشخصية
        { s: { r: 10, c: 0 }, e: { r: 10, c: 1 } }, // الإحصائيات العامة
        { s: { r: 17, c: 0 }, e: { r: 17, c: 1 } }  // تاريخ التقرير
      )

      XLSX.utils.book_append_sheet(workbook, basicSheet, 'المعلومات الأساسية')

      // ورقة الدورات
      if (traineeStats.courses.length > 0) {
        const coursesData = [
          ['اسم الدورة', 'تاريخ البداية', 'تاريخ النهاية', 'المدرب', 'القسم', 'المدة (ساعات)', 'الحالة', 'نسبة الحضور'],
          ...traineeStats.courses.map(course => [
            course.courseName || course.title || 'غير محدد',
            course.startDate || 'غير محدد',
            course.endDate || 'غير محدد',
            course.trainer || 'غير محدد',
            course.department || 'غير محدد',
            course.duration || 'غير محدد',
            course.status === 'active' ? 'نشط' : course.status === 'completed' ? 'مكتمل' : course.status === 'pending' ? 'معلق' : 'غير محدد',
            course.attendance ? `${course.attendance}%` : 'غير محدد'
          ])
        ]

        const coursesSheet = XLSX.utils.aoa_to_sheet(coursesData)
        coursesSheet['!cols'] = [
          { width: 30 }, { width: 15 }, { width: 15 },
          { width: 20 }, { width: 15 }, { width: 15 }, { width: 12 }, { width: 15 }
        ]

        // تطبيق التنسيق المتقدم
        applyAdvancedFormatting(coursesSheet, colors)

        XLSX.utils.book_append_sheet(workbook, coursesSheet, 'الدورات التدريبية')
      }

      // ورقة ورش العمل
      if (traineeStats.workshops.length > 0) {
        const workshopsData = [
          ['اسم ورشة العمل', 'التاريخ', 'الوقت', 'المدة (دقيقة)', 'المدرب', 'المكان', 'الحالة', 'التقييم'],
          ...traineeStats.workshops.map(workshop => [
            workshop.title || 'غير محدد',
            workshop.date || 'غير محدد',
            workshop.time || 'غير محدد',
            workshop.duration || 'غير محدد',
            workshop.trainer || 'غير محدد',
            workshop.location || 'غير محدد',
            workshop.status === 'completed' ? 'مكتمل' : workshop.status === 'active' ? 'نشط' : workshop.status === 'cancelled' ? 'ملغي' : 'مجدول',
            workshop.rating ? `${workshop.rating}/5` : 'غير مقيم'
          ])
        ]

        const workshopsSheet = XLSX.utils.aoa_to_sheet(workshopsData)
        workshopsSheet['!cols'] = [
          { width: 30 }, { width: 15 }, { width: 10 },
          { width: 15 }, { width: 20 }, { width: 15 }, { width: 12 }, { width: 12 }
        ]

        // تطبيق التنسيق المتقدم
        applyAdvancedFormatting(workshopsSheet, colors)

        XLSX.utils.book_append_sheet(workbook, workshopsSheet, 'ورش العمل')
      }

      // ورقة المهام والدرجات
      if (traineeStats.tasks.length > 0) {
        const gradingSettings = JSON.parse(localStorage.getItem('global_grading_settings') || '{}')
        const maxGrade = gradingSettings.maxGrade || 100

        const tasksData = [
          ['اسم المهمة', 'تاريخ الإنشاء', 'تاريخ الاستحقاق', 'تاريخ التسليم', 'الحالة', 'الأولوية', 'الدرجة', 'النسبة المئوية', 'التقدير', 'ملاحظات'],
          ...traineeStats.tasks.map(task => {
            const taskGrades = traineeStats.grades[task.id] || {}
            const userGrade = taskGrades[trainee.id] || taskGrades[trainee.name]
            const percentage = userGrade ? Math.round((userGrade / maxGrade) * 100) : 0

            let gradeText = 'غير مقيم'
            if (userGrade) {
              if (gradingSettings.gradingScale === 'letter') {
                if (percentage >= 90) gradeText = 'A+'
                else if (percentage >= 85) gradeText = 'A'
                else if (percentage >= 80) gradeText = 'B+'
                else if (percentage >= 75) gradeText = 'B'
                else if (percentage >= 70) gradeText = 'C+'
                else if (percentage >= 65) gradeText = 'C'
                else if (percentage >= 60) gradeText = 'D+'
                else if (percentage >= 55) gradeText = 'D'
                else gradeText = 'F'
              } else if (gradingSettings.gradingScale === 'points') {
                gradeText = `${userGrade}/${maxGrade}`
              } else {
                gradeText = `${percentage}%`
              }
            }

            return [
              task.title || 'غير محدد',
              task.createdAt ? new Date(task.createdAt).toLocaleDateString('ar-SA') : 'غير محدد',
              task.dueDate || 'غير محدد',
              task.submittedAt ? new Date(task.submittedAt).toLocaleDateString('ar-SA') : 'لم يتم التسليم',
              task.status === 'completed' ? 'مكتمل' : task.status === 'in_progress' ? 'قيد التنفيذ' : task.status === 'submitted' ? 'تم التسليم' : 'مجدول',
              task.priority === 'high' ? 'عالية' : task.priority === 'medium' ? 'متوسطة' : 'منخفضة',
              userGrade || 'غير مقيم',
              userGrade ? `${percentage}%` : 'غير مقيم',
              gradeText,
              task.feedback || 'لا توجد ملاحظات'
            ]
          })
        ]

        const tasksSheet = XLSX.utils.aoa_to_sheet(tasksData)
        tasksSheet['!cols'] = [
          { width: 25 }, { width: 15 }, { width: 15 }, { width: 15 },
          { width: 12 }, { width: 12 }, { width: 10 }, { width: 15 },
          { width: 12 }, { width: 30 }
        ]

        // تطبيق التنسيق المتقدم
        applyAdvancedFormatting(tasksSheet, colors)

        XLSX.utils.book_append_sheet(workbook, tasksSheet, 'المهام والدرجات')
      }

      // ورقة الإحصائيات المفصلة
      const detailedStats = [
        ['الإحصائيات التفصيلية للمتدرب', ''],
        ['', ''],
        ['إحصائيات الدورات', ''],
        ['إجمالي الدورات المسجل بها', traineeStats.courses.length],
        ['الدورات النشطة', traineeStats.courses.filter(c => c.status === 'active').length],
        ['الدورات المكتملة', traineeStats.courses.filter(c => c.status === 'completed').length],
        ['الدورات المعلقة', traineeStats.courses.filter(c => c.status === 'pending').length],
        ['', ''],
        ['إحصائيات ورش العمل', ''],
        ['إجمالي ورش العمل', traineeStats.workshops.length],
        ['ورش العمل المكتملة', traineeStats.workshops.filter(w => w.status === 'completed').length],
        ['ورش العمل النشطة', traineeStats.workshops.filter(w => w.status === 'active').length],
        ['ورش العمل المجدولة', traineeStats.workshops.filter(w => w.status === 'scheduled').length],
        ['', ''],
        ['إحصائيات المهام', ''],
        ['إجمالي المهام', traineeStats.tasks.length],
        ['المهام المكتملة', traineeStats.completedTasks.length],
        ['المهام قيد التنفيذ', traineeStats.tasks.filter(t => t.status === 'in_progress').length],
        ['المهام المجدولة', traineeStats.tasks.filter(t => t.status === 'pending').length],
        ['نسبة إنجاز المهام', traineeStats.tasks.length > 0 ? `${Math.round((traineeStats.completedTasks.length / traineeStats.tasks.length) * 100)}%` : '0%'],
        ['', ''],
        ['إحصائيات الدرجات', ''],
        ['المعدل التراكمي', `${calculateGPA()}%`],
        ['عدد المهام المقيمة', Object.keys(traineeStats.grades).length],
        ['أعلى درجة', 'سيتم حسابها'],
        ['أقل درجة', 'سيتم حسابها'],
        ['', ''],
        ['معلومات إضافية', ''],
        ['آخر نشاط', trainee.lastActivity ? new Date(trainee.lastActivity).toLocaleDateString('ar-SA') : 'غير محدد'],
        ['إجمالي ساعات التدريب', 'سيتم حسابها'],
        ['عدد الشهادات المحصل عليها', traineeStats.courses.filter(c => c.status === 'completed').length]
      ]

      const statsSheet = XLSX.utils.aoa_to_sheet(detailedStats)
      statsSheet['!cols'] = [{ width: 35 }, { width: 25 }]

      // تنسيق ورقة الإحصائيات
      const statsHeaderCells = ['A1', 'A3', 'A8', 'A14', 'A21', 'A27']
      statsHeaderCells.forEach(cell => {
        if (statsSheet[cell]) {
          statsSheet[cell].s = {
            fill: { fgColor: { rgb: colors.secondary } },
            font: { bold: true, color: { rgb: 'FFFFFF' }, sz: 12, name: 'Arial' },
            alignment: { horizontal: 'center', vertical: 'center' }
          }
        }
      })

      // دمج خلايا العناوين
      if (!statsSheet['!merges']) statsSheet['!merges'] = []
      statsSheet['!merges'].push(
        { s: { r: 0, c: 0 }, e: { r: 0, c: 1 } },
        { s: { r: 2, c: 0 }, e: { r: 2, c: 1 } },
        { s: { r: 7, c: 0 }, e: { r: 7, c: 1 } },
        { s: { r: 13, c: 0 }, e: { r: 13, c: 1 } },
        { s: { r: 20, c: 0 }, e: { r: 20, c: 1 } },
        { s: { r: 26, c: 0 }, e: { r: 26, c: 1 } }
      )

      XLSX.utils.book_append_sheet(workbook, statsSheet, 'الإحصائيات التفصيلية')

      // حفظ الملف
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-').split('T')[0]
      const fileName = `تقرير_شامل_${trainee.name.replace(/\s+/g, '_')}_${timestamp}.xlsx`
      XLSX.writeFile(workbook, fileName)

      toast.success(`✅ تم تصدير التقرير الشامل للمتدرب ${trainee.name} بنجاح!\n📊 يحتوي على: المعلومات الأساسية، الدورات، ورش العمل، المهام والدرجات، والإحصائيات التفصيلية`)
    } catch (error) {
      console.error('خطأ في التصدير:', error)
      toast.error('فشل في تصدير التقرير')
    } finally {
      setIsExporting(false)
    }
  }

  // تصدير جميع المتدربين
  const exportAllTrainees = async () => {
    setIsExporting(true)
    try {
      const data = loadRealData()
      const colors = getSystemColors()
      const trainees = data.users.filter(user => user.role === 'trainee')

      if (trainees.length === 0) {
        toast.error('لا يوجد متدربين للتصدير')
        return
      }

      // إنشاء workbook جديد
      const workbook = XLSX.utils.book_new()

      // ورقة ملخص جميع المتدربين
      const summaryData = [
        ['الاسم', 'البريد الإلكتروني', 'القسم', 'عدد الدورات', 'عدد ورش العمل', 'عدد المهام', 'تاريخ التسجيل', 'الحالة'],
        ...trainees.map(trainee => [
          trainee.name || 'غير محدد',
          trainee.email || 'غير محدد',
          trainee.department || 'غير محدد',
          data.courses.filter(c => c.selectedTrainees?.includes(trainee.name)).length,
          data.workshops.filter(w => w.participants?.includes(trainee.name)).length,
          data.tasks.filter(t => t.assignedUsers?.includes(trainee.name)).length,
          trainee.createdAt ? new Date(trainee.createdAt).toLocaleDateString('ar-SA') : 'غير محدد',
          trainee.status === 'active' ? 'نشط' : 'غير نشط'
        ])
      ]

      const summarySheet = XLSX.utils.aoa_to_sheet(summaryData)
      summarySheet['!cols'] = [
        { width: 25 }, { width: 30 }, { width: 20 }, { width: 15 },
        { width: 18 }, { width: 15 }, { width: 18 }, { width: 15 }
      ]

      // تطبيق التنسيق المتقدم
      applyAdvancedFormatting(summarySheet, colors)

      XLSX.utils.book_append_sheet(workbook, summarySheet, 'ملخص المتدربين')

      // ورقة تفاصيل الدورات
      if (data.courses.length > 0) {
        const coursesData = [
          ['اسم الدورة', 'تاريخ البداية', 'تاريخ النهاية', 'المدرب', 'القسم', 'عدد المتدربين', 'الحالة'],
          ...data.courses.map(course => [
            course.courseName || course.title || 'غير محدد',
            course.startDate || 'غير محدد',
            course.endDate || 'غير محدد',
            course.trainer || 'غير محدد',
            course.department || 'غير محدد',
            course.selectedTrainees?.length || 0,
            course.status === 'active' ? 'نشط' : course.status === 'completed' ? 'مكتمل' : 'غير محدد'
          ])
        ]

        const coursesSheet = XLSX.utils.aoa_to_sheet(coursesData)
        coursesSheet['!cols'] = [
          { width: 30 }, { width: 18 }, { width: 18 }, { width: 25 },
          { width: 20 }, { width: 18 }, { width: 15 }
        ]

        // تطبيق التنسيق المتقدم
        applyAdvancedFormatting(coursesSheet, colors)

        XLSX.utils.book_append_sheet(workbook, coursesSheet, 'جميع الدورات')
      }

      // ورقة ورش العمل
      if (data.workshops.length > 0) {
        const workshopsData = [
          ['اسم ورشة العمل', 'التاريخ', 'الوقت', 'المدة', 'المدرب', 'عدد المشاركين', 'الحالة'],
          ...data.workshops.map(workshop => [
            workshop.title || 'غير محدد',
            workshop.date || 'غير محدد',
            workshop.time || 'غير محدد',
            workshop.duration ? `${workshop.duration} دقيقة` : 'غير محدد',
            workshop.trainer || 'غير محدد',
            workshop.participants?.length || 0,
            workshop.status === 'completed' ? 'مكتمل' : workshop.status === 'active' ? 'نشط' : 'مجدول'
          ])
        ]

        const workshopsSheet = XLSX.utils.aoa_to_sheet(workshopsData)
        workshopsSheet['!cols'] = [
          { width: 30 }, { width: 18 }, { width: 12 }, { width: 15 },
          { width: 25 }, { width: 18 }, { width: 15 }
        ]

        // تطبيق التنسيق المتقدم
        applyAdvancedFormatting(workshopsSheet, colors)

        XLSX.utils.book_append_sheet(workbook, workshopsSheet, 'جميع ورش العمل')
      }

      // حفظ الملف
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-').split('T')[0]
      const fileName = `تقرير_شامل_جميع_المتدربين_${timestamp}.xlsx`
      XLSX.writeFile(workbook, fileName)

      toast.success(`✅ تم تصدير التقرير الشامل لجميع المتدربين بنجاح!\n👥 عدد المتدربين: ${trainees.length}\n📊 يحتوي على: ملخص المتدربين، جميع الدورات، وجميع ورش العمل`)
    } catch (error) {
      console.error('خطأ في التصدير:', error)
      toast.error('فشل في تصدير التقرير')
    } finally {
      setIsExporting(false)
    }
  }

  return (
    <div className="space-y-4">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {/* تصدير متدرب واحد */}
        <div className="card p-6">
          <div className="flex items-center mb-4">
            <User className="w-6 h-6 text-blue-500 mr-3 rtl:mr-0 rtl:ml-3" />
            <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
              تصدير متدرب واحد
            </h3>
          </div>
          <p className="text-sm text-gray-600 dark:text-gray-400 mb-4">
            اختر متدرب محدد لتصدير تقريره الشخصي مع جميع تفاصيل دوراته ومهامه
          </p>
          <TraineeSelector onSelect={exportSingleTrainee} isLoading={isExporting} />
        </div>

        {/* تصدير جميع المتدربين */}
        <div className="card p-6">
          <div className="flex items-center mb-4">
            <Users className="w-6 h-6 text-green-500 mr-3 rtl:mr-0 rtl:ml-3" />
            <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
              تصدير جميع المتدربين
            </h3>
          </div>
          <p className="text-sm text-gray-600 dark:text-gray-400 mb-4">
            تصدير تقرير شامل لجميع المتدربين مع ملخص الأنشطة والإحصائيات
          </p>
          <Button
            variant="primary"
            onClick={exportAllTrainees}
            loading={isExporting}
            icon={FileSpreadsheet}
            className="w-full"
          >
            تصدير جميع المتدربين
          </Button>
        </div>
      </div>
    </div>
  )
}

// مكون اختيار المتدرب
const TraineeSelector = ({ onSelect, isLoading }) => {
  const [selectedTrainee, setSelectedTrainee] = useState('')
  const [trainees, setTrainees] = useState([])

  useEffect(() => {
    try {
      const users = JSON.parse(localStorage.getItem('app_users') || '[]')
      // التأكد من أن users مصفوفة صحيحة
      const safeUsers = Array.isArray(users) ? users : []
      const traineeUsers = safeUsers.filter(user => user.role === 'trainee')
      setTrainees(traineeUsers)
    } catch (error) {
      console.error('خطأ في تحميل المتدربين:', error)
      setTrainees([])
    }
  }, [])

  const handleExport = () => {
    if (!selectedTrainee) {
      toast.error('يرجى اختيار متدرب')
      return
    }

    const trainee = trainees.find(t => t.id === selectedTrainee)
    if (trainee) {
      onSelect(trainee)
    }
  }

  return (
    <div className="space-y-3">
      <select
        value={selectedTrainee}
        onChange={(e) => setSelectedTrainee(e.target.value)}
        className="input-field w-full"
        disabled={isLoading}
      >
        <option value="">اختر متدرب...</option>
        {trainees.map(trainee => (
          <option key={trainee.id} value={trainee.id}>
            {trainee.name} - {trainee.department || 'غير محدد'}
          </option>
        ))}
      </select>
      
      <Button
        variant="primary"
        onClick={handleExport}
        loading={isLoading}
        disabled={!selectedTrainee}
        icon={Download}
        className="w-full"
      >
        تصدير تقرير المتدرب
      </Button>
    </div>
  )
}

// دالة الحصول على ألوان النظام
const getSystemColors = () => {
  // الحصول على الألوان من CSS variables أو استخدام ألوان افتراضية
  const root = document.documentElement
  const primaryColor = getComputedStyle(root).getPropertyValue('--color-primary')?.trim() || '#3B82F6'
  const secondaryColor = getComputedStyle(root).getPropertyValue('--color-secondary')?.trim() || '#10B981'

  // تحويل الألوان إلى hex للاستخدام في Excel
  const hexToRgb = (hex) => {
    const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex)
    return result ? {
      r: parseInt(result[1], 16),
      g: parseInt(result[2], 16),
      b: parseInt(result[3], 16)
    } : { r: 59, g: 130, b: 246 }
  }

  const primary = hexToRgb(primaryColor)
  const secondary = hexToRgb(secondaryColor)

  return {
    primary: `${primary.r.toString(16).padStart(2, '0')}${primary.g.toString(16).padStart(2, '0')}${primary.b.toString(16).padStart(2, '0')}`,
    secondary: `${secondary.r.toString(16).padStart(2, '0')}${secondary.g.toString(16).padStart(2, '0')}${secondary.b.toString(16).padStart(2, '0')}`,
    success: '10B981',
    warning: 'F59E0B',
    error: 'EF4444',
    info: '3B82F6',
    light: 'F8FAFC',
    dark: '1E293B'
  }
}

// دالة تطبيق التنسيق المتقدم (نسخة للدوال الخارجية)
const applyAdvancedFormattingExternal = (sheet, data, colors) => {
  if (!sheet || !data || data.length === 0) return

  // تنسيق صف العناوين
  const headerRow = 1
  for (let col = 0; col < data[0].length; col++) {
    const cellRef = XLSX.utils.encode_cell({ r: headerRow - 1, c: col })
    if (sheet[cellRef]) {
      sheet[cellRef].s = {
        fill: { fgColor: { rgb: colors.primary } },
        font: { bold: true, color: { rgb: 'FFFFFF' }, sz: 12, name: 'Arial' },
        alignment: { horizontal: 'center', vertical: 'center' },
        border: {
          top: { style: 'thin', color: { rgb: colors.dark } },
          bottom: { style: 'thin', color: { rgb: colors.dark } },
          left: { style: 'thin', color: { rgb: colors.dark } },
          right: { style: 'thin', color: { rgb: colors.dark } }
        }
      }
    }
  }

  // تنسيق باقي الصفوف
  for (let row = 1; row < data.length; row++) {
    for (let col = 0; col < data[row].length; col++) {
      const cellRef = XLSX.utils.encode_cell({ r: row, c: col })
      if (sheet[cellRef]) {
        const isEvenRow = row % 2 === 0
        sheet[cellRef].s = {
          fill: { fgColor: { rgb: isEvenRow ? colors.light : 'FFFFFF' } },
          font: { sz: 10, name: 'Arial', color: { rgb: colors.dark } },
          alignment: { horizontal: 'center', vertical: 'center' },
          border: {
            top: { style: 'thin', color: { rgb: 'E2E8F0' } },
            bottom: { style: 'thin', color: { rgb: 'E2E8F0' } },
            left: { style: 'thin', color: { rgb: 'E2E8F0' } },
            right: { style: 'thin', color: { rgb: 'E2E8F0' } }
          }
        }

        // تنسيق خاص للخلايا التي تحتوي على حالات
        const cellValue = sheet[cellRef].v
        if (typeof cellValue === 'string') {
          if (cellValue.includes('✅') || cellValue.includes('مكتمل')) {
            sheet[cellRef].s.fill = { fgColor: { rgb: colors.success + '20' } }
          } else if (cellValue.includes('❌') || cellValue.includes('ملغي')) {
            sheet[cellRef].s.fill = { fgColor: { rgb: colors.error + '20' } }
          } else if (cellValue.includes('⏳') || cellValue.includes('معلق')) {
            sheet[cellRef].s.fill = { fgColor: { rgb: colors.warning + '20' } }
          } else if (cellValue.includes('🟢') || cellValue.includes('نشط')) {
            sheet[cellRef].s.fill = { fgColor: { rgb: colors.success + '20' } }
          }
        }
      }
    }
  }

  // إضافة تجميد للصف الأول
  sheet['!freeze'] = { xSplit: 0, ySplit: 1 }
}

// دالة تطبيق التنسيق المحسن للمعلومات الأساسية
const applyEnhancedFormatting = (sheet, data, colors, XLSX) => {
  const range = XLSX.utils.decode_range(sheet['!ref'])

  // تطبيق التنسيق الأساسي
  for (let R = range.s.r; R <= range.e.r; ++R) {
    for (let C = range.s.c; C <= range.e.c; ++C) {
      const cellAddress = XLSX.utils.encode_cell({ r: R, c: C })
      if (!sheet[cellAddress]) continue

      // تنسيق افتراضي
      sheet[cellAddress].s = {
        font: { name: 'Arial', sz: 11 },
        alignment: { horizontal: 'right', vertical: 'center' },
        border: {
          top: { style: 'thin', color: { rgb: 'E0E0E0' } },
          bottom: { style: 'thin', color: { rgb: 'E0E0E0' } },
          left: { style: 'thin', color: { rgb: 'E0E0E0' } },
          right: { style: 'thin', color: { rgb: 'E0E0E0' } }
        }
      }

      // تنسيق العناوين الرئيسية
      if (C === 0 && data[R] && (
        data[R][0] === 'تقرير شامل للمتدرب' ||
        data[R][0] === 'المعلومات الشخصية' ||
        data[R][0] === 'الإحصائيات العامة' ||
        data[R][0] === 'معلومات التقرير'
      )) {
        sheet[cellAddress].s = {
          fill: { fgColor: { rgb: colors.primary.replace('#', '') } },
          font: { bold: true, color: { rgb: 'FFFFFF' }, sz: 14, name: 'Arial' },
          alignment: { horizontal: 'center', vertical: 'center' },
          border: {
            top: { style: 'medium', color: { rgb: colors.primary.replace('#', '') } },
            bottom: { style: 'medium', color: { rgb: colors.primary.replace('#', '') } },
            left: { style: 'medium', color: { rgb: colors.primary.replace('#', '') } },
            right: { style: 'medium', color: { rgb: colors.primary.replace('#', '') } }
          }
        }
      }
      // تنسيق التسميات
      else if (C === 0 && data[R] && data[R][0] && data[R][0].trim() !== '' && data[R][0] !== '') {
        sheet[cellAddress].s.fill = { fgColor: { rgb: colors.secondary.replace('#', '') } }
        sheet[cellAddress].s.font = { ...sheet[cellAddress].s.font, bold: true, color: { rgb: '333333' } }
      }
      // تنسيق القيم
      else if (C === 1 && data[R] && data[R][1] && data[R][1] !== '') {
        sheet[cellAddress].s.fill = { fgColor: { rgb: 'F8F9FA' } }
        sheet[cellAddress].s.font = { ...sheet[cellAddress].s.font, color: { rgb: '495057' } }
      }
    }
  }

  // دمج خلايا العناوين الرئيسية
  if (!sheet['!merges']) sheet['!merges'] = []

  // البحث عن صفوف العناوين ودمجها
  for (let R = 0; R < data.length; R++) {
    if (data[R] && (
      data[R][0] === 'تقرير شامل للمتدرب' ||
      data[R][0] === 'المعلومات الشخصية' ||
      data[R][0] === 'الإحصائيات العامة' ||
      data[R][0] === 'معلومات التقرير'
    )) {
      sheet['!merges'].push({ s: { r: R, c: 0 }, e: { r: R, c: 2 } })
    }
  }
}

// دالة لعرض الجنس بشكل صحيح (محسنة)
const getGenderDisplayName = (gender, language = 'ar') => {
  console.log('🔍 معالجة الجنس في ExcelExporter:', { gender, language, type: typeof gender })

  // التحقق من وجود القيمة أولاً
  if (!gender || gender === null || gender === undefined || gender === '') {
    const result = language === 'en' ? 'Not specified' : 'غير محدد'
    console.log('⚠️ لا توجد قيمة للجنس، استخدام القيمة الافتراضية:', result)
    return result
  }

  // تنظيف وتوحيد قيم الجنس
  const normalizedGender = gender.toString().toLowerCase().trim()
  console.log('🧹 الجنس بعد التنظيف:', normalizedGender)

  const genderNames = {
    en: {
      'ذكر': 'Male',
      'male': 'Male',
      'm': 'Male',
      'man': 'Male',
      'boy': 'Male',
      'أنثى': 'Female',
      'female': 'Female',
      'f': 'Female',
      'woman': 'Female',
      'girl': 'Female',
      'انثى': 'Female',
      'انثي': 'Female'
    },
    ar: {
      'ذكر': 'ذكر',
      'male': 'ذكر',
      'm': 'ذكر',
      'man': 'ذكر',
      'boy': 'ذكر',
      'أنثى': 'أنثى',
      'female': 'أنثى',
      'f': 'أنثى',
      'woman': 'أنثى',
      'girl': 'أنثى',
      'انثى': 'أنثى',
      'انثي': 'أنثى'
    }
  }

  // البحث في القيم المطابقة
  let result = genderNames[language]?.[normalizedGender] ||
               genderNames[language]?.[gender] ||
               null

  // إذا لم نجد مطابقة، جرب البحث في اللغة الأخرى
  if (!result) {
    const otherLanguage = language === 'en' ? 'ar' : 'en'
    result = genderNames[otherLanguage]?.[normalizedGender]
    if (result) {
      // ترجم النتيجة للغة المطلوبة
      result = genderNames[language]?.[result.toLowerCase()] || result
    }
  }

  // إذا لم نجد مطابقة، نعرض القيمة الافتراضية
  if (!result || result === 'undefined' || result === 'null' || result === '') {
    result = language === 'en' ? 'Not specified' : 'غير محدد'
    console.log('⚠️ لم يتم العثور على مطابقة للجنس، استخدام القيمة الافتراضية:', result)
  } else {
    console.log('✅ تم العثور على مطابقة للجنس:', result)
  }

  return result
}

// دالة إنشاء ورقة المعلومات الأساسية
const createBasicInfoSheet = async (workbook, trainee, traineeData, colors, XLSX) => {
  const basicInfo = [
    ['تقرير شامل للمتدرب', '', ''],
    ['', '', ''],
    ['المعلومات الشخصية', '', ''],
    ['الاسم الكامل', trainee.name || 'غير محدد', ''],
    ['البريد الإلكتروني', trainee.email || 'غير محدد', ''],
    ['رقم الهاتف', trainee.phone || 'غير محدد', ''],
    ['الجنس', getGenderDisplayName(trainee.gender, 'ar'), ''],
    ['القسم/الإدارة', trainee.department || 'غير محدد', ''],
    ['تاريخ التسجيل', trainee.createdAt ? new Date(trainee.createdAt).toLocaleDateString('ar-SA') : 'غير محدد', ''],
    ['حالة الحساب', trainee.status === 'active' ? 'نشط ✅' : 'غير نشط ❌', ''],
    ['صورة البروفايل', trainee.profileImage ? 'متوفرة' : 'غير متوفرة', ''],
    ['', '', ''],
    ['الإحصائيات العامة', '', ''],
    ['إجمالي الدورات المسجل بها', traineeData.courses.length, ''],
    ['إجمالي ورش العمل المشارك بها', traineeData.workshops.length, ''],
    ['إجمالي المهام المخصصة', traineeData.tasks.length, ''],
    ['المهام المكتملة', traineeData.tasks.filter(t => t.status === 'completed').length, ''],
    ['المهام الشخصية', traineeData.personalTasks.length, ''],
    ['المهام الشخصية المكتملة', traineeData.personalTasks.filter(pt => pt.completed).length, ''],
    ['نسبة إنجاز المهام', traineeData.tasks.length > 0 ? `${Math.round((traineeData.tasks.filter(t => t.status === 'completed').length / traineeData.tasks.length) * 100)}%` : '0%', ''],
    ['', '', ''],
    ['معلومات التقرير', '', ''],
    ['تاريخ إنشاء التقرير', new Date().toLocaleDateString('ar-SA'), ''],
    ['وقت إنشاء التقرير', new Date().toLocaleTimeString('ar-SA'), '']
  ]

  const basicSheet = XLSX.utils.aoa_to_sheet(basicInfo)
  basicSheet['!cols'] = [{ width: 35 }, { width: 30 }, { width: 15 }]

  // تطبيق التنسيق المحسن
  applyEnhancedFormatting(basicSheet, basicInfo, colors, XLSX)

  XLSX.utils.book_append_sheet(workbook, basicSheet, 'المعلومات الأساسية')
}

// دالة إنشاء ورقة الدورات (الروتيشن)
const createCoursesSheet = async (workbook, courses, colors, XLSX) => {
  if (courses.length === 0) {
    const noCourses = [['لا توجد دورات مسجل بها المتدرب']]
    const coursesSheet = XLSX.utils.aoa_to_sheet(noCourses)
    XLSX.utils.book_append_sheet(workbook, coursesSheet, 'الدورات (الروتيشن)')
    return
  }

  const coursesData = [
    ['اسم الدورة', 'تاريخ البداية', 'تاريخ النهاية', 'المدرب', 'القسم', 'المدة (ساعات)', 'الحالة', 'نسبة الحضور', 'التقييم', 'الدرجة النهائية'],
    ...courses.map(course => [
      course.courseName || course.title || 'غير محدد',
      course.startDate || 'غير محدد',
      course.endDate || 'غير محدد',
      course.trainer || 'غير محدد',
      course.department || 'غير محدد',
      course.duration || 'غير محدد',
      course.status === 'active' ? 'نشط 🟢' : course.status === 'completed' ? 'مكتمل ✅' : course.status === 'pending' ? 'معلق ⏳' : 'غير محدد',
      course.attendance ? `${course.attendance}%` : 'غير محدد',
      course.rating ? `${course.rating}/5 ⭐` : 'غير مقيم',
      course.finalGrade ? `${course.finalGrade}%` : 'غير محدد'
    ])
  ]

  const coursesSheet = XLSX.utils.aoa_to_sheet(coursesData)
  coursesSheet['!cols'] = [
    { width: 30 }, { width: 15 }, { width: 15 }, { width: 20 },
    { width: 15 }, { width: 15 }, { width: 15 }, { width: 15 },
    { width: 15 }, { width: 15 }
  ]

  // تطبيق التنسيق المتقدم
  applyAdvancedFormattingExternal(coursesSheet, coursesData, colors)

  XLSX.utils.book_append_sheet(workbook, coursesSheet, 'الدورات (الروتيشن)')
}

// دالة إنشاء ورقة المهام
const createTasksSheet = async (workbook, tasks, trainee, grades, gradingSettings, colors, XLSX) => {
  if (tasks.length === 0) {
    const noTasks = [['لا توجد مهام مخصصة للمتدرب']]
    const tasksSheet = XLSX.utils.aoa_to_sheet(noTasks)
    XLSX.utils.book_append_sheet(workbook, tasksSheet, 'المهام')
    return
  }

  const maxGrade = gradingSettings.maxGrade || 100

  const tasksData = [
    ['اسم المهمة', 'تاريخ الإنشاء', 'تاريخ الاستحقاق', 'تاريخ التسليم', 'الحالة', 'الأولوية', 'الدرجة', 'النسبة المئوية', 'التقدير', 'ملاحظات المدرب'],
    ...tasks.map(task => {
      const taskGrades = grades[task.id] || {}
      const userGrade = taskGrades[trainee.id] || taskGrades[trainee.name]
      const percentage = userGrade ? Math.round((userGrade / maxGrade) * 100) : 0

      let gradeText = 'غير مقيم'
      let statusIcon = ''

      if (userGrade) {
        if (gradingSettings.gradingScale === 'letter') {
          if (percentage >= 90) gradeText = 'A+ 🏆'
          else if (percentage >= 85) gradeText = 'A ⭐'
          else if (percentage >= 80) gradeText = 'B+ 👍'
          else if (percentage >= 75) gradeText = 'B ✅'
          else if (percentage >= 70) gradeText = 'C+ 📝'
          else if (percentage >= 65) gradeText = 'C 📋'
          else if (percentage >= 60) gradeText = 'D+ ⚠️'
          else if (percentage >= 55) gradeText = 'D 🔄'
          else gradeText = 'F ❌'
        } else {
          gradeText = `${percentage}%`
        }
      }

      // أيقونات الحالة
      switch(task.status) {
        case 'completed': statusIcon = 'مكتمل ✅'; break
        case 'in_progress': statusIcon = 'قيد التنفيذ 🔄'; break
        case 'submitted': statusIcon = 'تم التسليم 📤'; break
        case 'pending': statusIcon = 'معلق ⏳'; break
        default: statusIcon = 'غير محدد ❓'
      }

      // أيقونات الأولوية
      let priorityIcon = ''
      switch(task.priority) {
        case 'high': priorityIcon = 'عالية 🔴'; break
        case 'medium': priorityIcon = 'متوسطة 🟡'; break
        case 'low': priorityIcon = 'منخفضة 🟢'; break
        default: priorityIcon = 'غير محدد'
      }

      return [
        task.title || 'غير محدد',
        task.createdAt ? new Date(task.createdAt).toLocaleDateString('ar-SA') : 'غير محدد',
        task.dueDate || 'غير محدد',
        task.submittedAt ? new Date(task.submittedAt).toLocaleDateString('ar-SA') : 'لم يتم التسليم',
        statusIcon,
        priorityIcon,
        userGrade || 'غير مقيم',
        userGrade ? `${percentage}%` : 'غير مقيم',
        gradeText,
        task.feedback || 'لا توجد ملاحظات'
      ]
    })
  ]

  const tasksSheet = XLSX.utils.aoa_to_sheet(tasksData)
  tasksSheet['!cols'] = [
    { width: 25 }, { width: 15 }, { width: 15 }, { width: 15 },
    { width: 15 }, { width: 15 }, { width: 10 }, { width: 15 },
    { width: 15 }, { width: 30 }
  ]

  // تطبيق التنسيق المتقدم
  applyAdvancedFormattingExternal(tasksSheet, tasksData, colors)

  XLSX.utils.book_append_sheet(workbook, tasksSheet, 'المهام')
}

// دالة إنشاء ورقة ورش العمل
const createWorkshopsSheet = async (workbook, workshops, colors, XLSX) => {
  if (workshops.length === 0) {
    const noWorkshops = [['لا توجد ورش عمل مشارك بها المتدرب']]
    const workshopsSheet = XLSX.utils.aoa_to_sheet(noWorkshops)
    XLSX.utils.book_append_sheet(workbook, workshopsSheet, 'ورش العمل')
    return
  }

  const workshopsData = [
    ['اسم ورشة العمل', 'التاريخ', 'الوقت', 'المدة (دقيقة)', 'المدرب', 'المكان', 'الحالة', 'التقييم الشخصي', 'نتيجة المجموعة', 'ملاحظات'],
    ...workshops.map(workshop => {
      let statusIcon = ''
      switch(workshop.status) {
        case 'completed': statusIcon = 'مكتمل ✅'; break
        case 'active': statusIcon = 'نشط 🟢'; break
        case 'scheduled': statusIcon = 'مجدول 📅'; break
        case 'cancelled': statusIcon = 'ملغي ❌'; break
        default: statusIcon = 'غير محدد ❓'
      }

      let groupResult = 'لم يتم التقييم'
      if (workshop.groupResult) {
        switch(workshop.groupResult) {
          case 'won': groupResult = 'فازت المجموعة 🏆'; break
          case 'lost': groupResult = 'لم تفز المجموعة 😔'; break
          case 'draw': groupResult = 'تعادل 🤝'; break
          default: groupResult = 'لم يتم التقييم ⏳'
        }
      }

      return [
        workshop.title || 'غير محدد',
        workshop.date || 'غير محدد',
        workshop.time || 'غير محدد',
        workshop.duration || 'غير محدد',
        workshop.trainer || 'غير محدد',
        workshop.location || 'غير محدد',
        statusIcon,
        workshop.rating ? `${workshop.rating}/5 ⭐` : 'غير مقيم',
        groupResult,
        workshop.notes || 'لا توجد ملاحظات'
      ]
    })
  ]

  const workshopsSheet = XLSX.utils.aoa_to_sheet(workshopsData)
  workshopsSheet['!cols'] = [
    { width: 30 }, { width: 15 }, { width: 10 }, { width: 15 },
    { width: 20 }, { width: 15 }, { width: 15 }, { width: 15 },
    { width: 20 }, { width: 25 }
  ]

  // تطبيق التنسيق المتقدم
  applyAdvancedFormattingExternal(workshopsSheet, workshopsData, colors)

  XLSX.utils.book_append_sheet(workbook, workshopsSheet, 'ورش العمل')
}

// دالة إنشاء ورقة المهام الشخصية
const createPersonalTasksSheet = async (workbook, personalTasks, colors, XLSX) => {
  if (personalTasks.length === 0) {
    const noPersonalTasks = [['لا توجد مهام شخصية مضافة من قبل المتدرب']]
    const personalTasksSheet = XLSX.utils.aoa_to_sheet(noPersonalTasks)
    XLSX.utils.book_append_sheet(workbook, personalTasksSheet, 'المهام الشخصية')
    return
  }

  const personalTasksData = [
    ['اسم المهمة', 'الوصف', 'تاريخ الإنشاء', 'تاريخ الاستحقاق', 'تاريخ الإكمال', 'الحالة', 'الأولوية', 'التقدم', 'ملاحظات شخصية'],
    ...personalTasks.map(task => {
      let statusIcon = task.completed ? 'مكتمل ✅' : 'غير مكتمل ⏳'

      let priorityIcon = ''
      switch(task.priority) {
        case 'high': priorityIcon = 'عالية 🔴'; break
        case 'medium': priorityIcon = 'متوسطة 🟡'; break
        case 'low': priorityIcon = 'منخفضة 🟢'; break
        default: priorityIcon = 'غير محدد'
      }

      const progress = task.progress || 0
      const progressIcon = progress === 100 ? '100% 🎯' : progress >= 75 ? `${progress}% 🚀` : progress >= 50 ? `${progress}% 📈` : progress >= 25 ? `${progress}% 📊` : `${progress}% 🔄`

      return [
        task.title || 'غير محدد',
        task.description || 'لا يوجد وصف',
        task.createdAt ? new Date(task.createdAt).toLocaleDateString('ar-SA') : 'غير محدد',
        task.dueDate || 'غير محدد',
        task.completedAt ? new Date(task.completedAt).toLocaleDateString('ar-SA') : 'لم يتم الإكمال',
        statusIcon,
        priorityIcon,
        progressIcon,
        task.notes || 'لا توجد ملاحظات'
      ]
    })
  ]

  const personalTasksSheet = XLSX.utils.aoa_to_sheet(personalTasksData)
  personalTasksSheet['!cols'] = [
    { width: 25 }, { width: 30 }, { width: 15 }, { width: 15 },
    { width: 15 }, { width: 15 }, { width: 15 }, { width: 15 },
    { width: 25 }
  ]

  // تطبيق التنسيق المتقدم
  applyAdvancedFormattingExternal(personalTasksSheet, personalTasksData, colors)

  XLSX.utils.book_append_sheet(workbook, personalTasksSheet, 'المهام الشخصية')
}

// دالة تصدير تقرير متقدم للمتدرب مع جميع الصفحات والتنسيق
export const exportAdvancedTraineeReport = async (trainee) => {
  try {
    // تحميل البيانات
    const courses = JSON.parse(localStorage.getItem('app_courses') || '[]')
    const workshops = JSON.parse(localStorage.getItem('workshops_data') || '[]')
    const tasks = JSON.parse(localStorage.getItem('tasks_data') || '[]')
    const personalTasks = JSON.parse(localStorage.getItem('personal_tasks') || '[]')
    const grades = JSON.parse(localStorage.getItem('task_grades') || '{}')
    const gradingSettings = JSON.parse(localStorage.getItem('global_grading_settings') || '{}')

    // استيراد XLSX
    const XLSX = await import('xlsx')

    // الحصول على ألوان النظام
    const colors = getSystemColors()

    // إنشاء workbook جديد
    const workbook = XLSX.utils.book_new()

    // فلترة البيانات الخاصة بالمتدرب
    const traineeData = {
      courses: courses.filter(c =>
        c.selectedTrainees?.includes(trainee.name) ||
        c.participants?.includes(trainee.name)
      ),
      workshops: workshops.filter(w =>
        w.selectedTrainees?.includes(trainee.name) ||
        w.participants?.includes(trainee.name)
      ),
      tasks: tasks.filter(t =>
        t.assignedUsers?.includes(trainee.name) ||
        t.assignedUsers?.includes(trainee.id)
      ),
      personalTasks: personalTasks.filter(pt =>
        pt.userId === trainee.id ||
        pt.userName === trainee.name
      )
    }

    // 1. ورقة المعلومات الأساسية
    await createBasicInfoSheet(workbook, trainee, traineeData, colors, XLSX)

    // 2. ورقة الدورات (الروتيشن)
    await createCoursesSheet(workbook, traineeData.courses, colors, XLSX)

    // 3. ورقة المهام
    await createTasksSheet(workbook, traineeData.tasks, trainee, grades, gradingSettings, colors, XLSX)

    // 4. ورقة ورش العمل
    await createWorkshopsSheet(workbook, traineeData.workshops, colors, XLSX)

    // 5. ورقة المهام الشخصية
    await createPersonalTasksSheet(workbook, traineeData.personalTasks, colors, XLSX)

    // 6. ورقة شاملة تحتوي على جميع البيانات
    await createComprehensiveSheet(workbook, trainee, traineeData, grades, gradingSettings, colors, XLSX)

    // حفظ الملف
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-').split('T')[0]
    const fileName = `تقرير_شامل_${trainee.name.replace(/\s+/g, '_')}_${timestamp}.xlsx`
    XLSX.writeFile(workbook, fileName)

    toast.success(`✅ تم تصدير التقرير الشامل للمتدرب ${trainee.name} بنجاح!\n📊 يحتوي على: المعلومات الأساسية، الدورات (الروتيشن)، المهام والدرجات، ورش العمل، المهام الشخصية، وملخص شامل`)

  } catch (error) {
    console.error('خطأ في تصدير التقرير:', error)
    toast.error('فشل في تصدير التقرير')
  }
}

// دالة إنشاء الورقة الشاملة
const createComprehensiveSheet = async (workbook, trainee, traineeData, grades, gradingSettings, colors, XLSX) => {
  const data = []

  // العنوان الرئيسي
  data.push(['التقرير الشامل للمتدرب'])
  data.push([])

  // المعلومات الأساسية
  data.push(['المعلومات الأساسية'])
  data.push(['الاسم', trainee.name])
  data.push(['البريد الإلكتروني', trainee.email || 'غير محدد'])
  data.push(['الهاتف', trainee.phone || 'غير محدد'])
  data.push(['القسم', trainee.department || 'غير محدد'])
  data.push(['الجنس', trainee.gender || 'غير محدد'])
  data.push([])

  // إحصائيات عامة
  data.push(['الإحصائيات العامة'])
  data.push(['عدد الدورات (الروتيشن)', traineeData.courses.length])
  data.push(['عدد ورش العمل', traineeData.workshops.length])
  data.push(['عدد المهام', traineeData.tasks.length])
  data.push(['عدد المهام الشخصية', traineeData.personalTasks.length])

  // حساب الدرجات
  const traineeGrades = Object.values(grades).filter(g =>
    g.traineeName === trainee.name || g.traineeId === trainee.id
  )
  const averageGrade = traineeGrades.length > 0
    ? traineeGrades.reduce((sum, g) => sum + g.grade, 0) / traineeGrades.length
    : 0

  data.push(['متوسط الدرجات', `${averageGrade.toFixed(1)}%`])
  data.push(['عدد المهام المكتملة', traineeData.tasks.filter(t => t.status === 'completed').length])
  data.push([])

  // تفاصيل الدورات
  if (traineeData.courses.length > 0) {
    data.push(['تفاصيل الدورات (الروتيشن)'])
    data.push(['اسم الدورة', 'تاريخ البداية', 'تاريخ النهاية', 'الحالة'])
    traineeData.courses.forEach(course => {
      data.push([
        course.title || course.name,
        course.startDate || 'غير محدد',
        course.endDate || 'غير محدد',
        course.status || 'نشط'
      ])
    })
    data.push([])
  }

  // تفاصيل ورش العمل
  if (traineeData.workshops.length > 0) {
    data.push(['تفاصيل ورش العمل'])
    data.push(['اسم الورشة', 'تاريخ البداية', 'تاريخ النهاية', 'الساعات اليومية', 'الحالة'])
    traineeData.workshops.forEach(workshop => {
      data.push([
        workshop.title || workshop.name,
        workshop.startDate || 'غير محدد',
        workshop.endDate || 'غير محدد',
        workshop.dailyHours || 'غير محدد',
        workshop.status || 'نشط'
      ])
    })
    data.push([])
  }

  // تفاصيل المهام والدرجات
  if (traineeData.tasks.length > 0) {
    data.push(['تفاصيل المهام والدرجات'])
    data.push(['اسم المهمة', 'الحالة', 'تاريخ الاستحقاق', 'الدرجة', 'التقدير'])
    traineeData.tasks.forEach(task => {
      const taskGrade = traineeGrades.find(g => g.taskId === task.id)
      const grade = taskGrade ? taskGrade.grade : 'لم يتم التقييم'
      const letterGrade = taskGrade ? getLetterGrade(taskGrade.grade, gradingSettings) : '-'

      data.push([
        task.title || task.name,
        task.status || 'معلق',
        task.dueDate || 'غير محدد',
        grade,
        letterGrade
      ])
    })
    data.push([])
  }

  // المهام الشخصية
  if (traineeData.personalTasks.length > 0) {
    data.push(['المهام الشخصية'])
    data.push(['اسم المهمة', 'الحالة', 'تاريخ الإنشاء', 'الأولوية'])
    traineeData.personalTasks.forEach(task => {
      data.push([
        task.title || task.name,
        task.status || 'معلق',
        task.createdAt || 'غير محدد',
        task.priority || 'متوسط'
      ])
    })
  }

  // إنشاء الورقة
  const ws = XLSX.utils.aoa_to_sheet(data)

  // تطبيق التنسيق
  applyAdvancedFormatting(ws, colors)

  XLSX.utils.book_append_sheet(workbook, ws, 'الملخص الشامل')
}

// دالة مساعدة لحساب التقدير
const getLetterGrade = (grade, settings) => {
  if (!settings || !settings.scale) {
    // نظام افتراضي
    if (grade >= 90) return 'A+'
    if (grade >= 85) return 'A'
    if (grade >= 80) return 'B+'
    if (grade >= 75) return 'B'
    if (grade >= 70) return 'C+'
    if (grade >= 65) return 'C'
    if (grade >= 60) return 'D'
    return 'F'
  }

  // استخدام نظام التقدير المخصص
  for (const level of settings.scale) {
    if (grade >= level.min && grade <= level.max) {
      return level.grade
    }
  }
  return 'F'
}

export default ExcelExporter
