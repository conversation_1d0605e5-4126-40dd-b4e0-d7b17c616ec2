import React, { useState, useEffect } from 'react'
import { usePara<PERSON>, useNavigate } from 'react-router-dom'
import { motion } from 'framer-motion'
import toast from 'react-hot-toast'
import {
  ArrowLeft,
  Calendar,
  Clock,
  Users,
  MapPin,
  FileText,
  Edit,
  Trash2,
  UserPlus,
  Shuffle,
  MessageSquare,
  Award,
  Download
} from 'lucide-react'
import Button from '../../components/ui/Button'
import ContentManager from '../../components/workshop/ContentManager'
import { useLanguage } from '../../contexts/LanguageContext'

const WorkshopDetails = () => {
  const { id } = useParams()
  const navigate = useNavigate()
  const { t } = useLanguage()
  const [workshop, setWorkshop] = useState(null)
  const [loading, setLoading] = useState(true)
  const [activeTab, setActiveTab] = useState('overview')

  useEffect(() => {
    const loadWorkshopData = async () => {
      try {
        setLoading(true)

        // تحميل ورش العمل من مصادر متعددة
        const workshopsFromStorage = JSON.parse(localStorage.getItem('workshops_data') || '[]')

        // محاولة تحميل من dataStorage أيضاً
        let workshopsFromDataStorage = []
        try {
          const { default: dataStorage } = await import('../../utils/dataStorage')
          workshopsFromDataStorage = dataStorage.loadWorkshops() || []
        } catch (error) {
          console.log('⚠️ Cannot load from dataStorage:', error.message)
        }

        // دمج ورش العمل من المصادر المختلفة
        const allWorkshops = [...workshopsFromStorage, ...workshopsFromDataStorage]

        // البحث عن ورشة العمل المطلوبة
        const foundWorkshop = allWorkshops.find(w => w.id === id || w.id === parseInt(id))

        if (foundWorkshop) {
          // تحميل المجموعات والنقاط إذا كانت متوفرة
          try {
            const { default: dataStorage } = await import('../../utils/dataStorage')
            const groups = dataStorage.loadGroups(foundWorkshop.id) || []
            const points = dataStorage.loadPoints(foundWorkshop.id) || {}

            // إضافة البيانات الإضافية لورشة العمل
            const enrichedWorkshop = {
              ...foundWorkshop,
              groups: groups,
              groupsCount: groups.length,
              enrolledCount: foundWorkshop.selectedTrainees ? foundWorkshop.selectedTrainees.length : 0,
              maxParticipants: foundWorkshop.maxParticipants || 30,
              materials: foundWorkshop.materials || [],
              participants: foundWorkshop.selectedTrainees || [],
              location: foundWorkshop.location || t('common.notSpecified'),
              instructor: foundWorkshop.trainer || foundWorkshop.instructor || t('common.notSpecified')
            }

            setWorkshop(enrichedWorkshop)
          } catch (error) {
            console.error('Error loading additional data:', error)
            setWorkshop(foundWorkshop)
          }
        } else {
          console.error('Workshop not found:', id)
          toast.error('Workshop not found')
          navigate('/admin/workshop-management')
        }
      } catch (error) {
        console.error('Error loading workshop:', error)
        toast.error('Failed to load workshop')
        navigate('/admin/workshop-management')
      } finally {
        setLoading(false)
      }
    }

    loadWorkshopData()
  }, [id, navigate])

  const handleEdit = () => {
    toast.info(t('toast.editWorkshop'))
  }

  const handleDelete = () => {
    toast.error(t('toast.deleteWorkshop'))
  }

  const handleAssignParticipants = () => {
    navigate(`/workshops/${id}/assign`)
  }

  const handleGenerateGroups = () => {
    toast.info('Create groups - Coming soon!')
  }

  const handleManageGroups = () => {
    navigate(`/workshops/${id}/groups`)
  }

  const handleManageMaterials = () => {
    navigate(`/workshops/${id}/materials`)
  }

  const handleGradeWorkshop = () => {
    navigate(`/workshops/${id}/grade`)
  }

  const handleDownloadMaterial = (material) => {
    toast.success(`Download ${material.name}`)
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary-600"></div>
      </div>
    )
  }

  if (!workshop) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-4">
            {t('workshops.notFound')}
          </h2>
          <Button onClick={() => navigate('/admin/workshop-management')} icon={ArrowLeft}>
            {t('workshops.backToList')}
          </Button>
        </div>
      </div>
    )
  }

  const getStatusColor = (status) => {
    switch (status) {
      case 'scheduled': return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200'
      case 'ongoing': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
      case 'completed': return 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200'
      case 'cancelled': return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200'
    }
  }

  const getStatusText = (status) => {
    switch (status) {
      case 'scheduled': return t('workshops.scheduled')
      case 'ongoing': return t('workshops.ongoing')
      case 'completed': return t('workshops.completed')
      case 'cancelled': return t('workshops.cancelled')
      default: return status
    }
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4 rtl:space-x-reverse">
          <Button
            variant="outline"
            onClick={() => navigate('/admin/workshop-management')}
            icon={ArrowLeft}
          >
            {t('common.back')}
          </Button>
          <div>
            <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100">
              {workshop.title}
            </h1>
            <div className="flex items-center space-x-2 rtl:space-x-reverse mt-1">
              <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(workshop.status)}`}>
                {getStatusText(workshop.status)}
              </span>
            </div>
          </div>
        </div>

        <div className="flex space-x-2 rtl:space-x-reverse">
          <Button variant="outline" onClick={handleEdit} icon={Edit}>
            {t('common.edit')}
          </Button>
          <Button variant="outline" onClick={handleDelete} icon={Trash2}>
            {t('common.delete')}
          </Button>
        </div>
      </div>

      {/* Tabs */}
      <div className="border-b border-gray-200 dark:border-gray-700">
        <nav className="-mb-px flex space-x-8 rtl:space-x-reverse">
          {[
            { id: 'overview', name: t('workshops.overview'), icon: FileText },
            { id: 'content', name: t('workshops.contentManagement'), icon: FileText },
            { id: 'participants', name: t('workshops.participants'), icon: Users },
            { id: 'materials', name: t('workshops.materials'), icon: Download }
          ].map((tab) => {
            const Icon = tab.icon
            return (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`${
                  activeTab === tab.id
                    ? 'border-primary-500 text-primary-600 dark:text-primary-400'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300'
                } whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm flex items-center space-x-2 rtl:space-x-reverse`}
              >
                <Icon className="w-4 h-4" />
                <span>{tab.name}</span>
              </button>
            )
          })}
        </nav>
      </div>

      {/* Tab Content */}
      <div className="mt-6">
        {activeTab === 'overview' && (
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Main Info */}
            <div className="lg:col-span-2 space-y-6">
              {/* Description */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="bg-white dark:bg-gray-800 rounded-lg shadow p-6"
              >
                <h2 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">
                  {t('workshops.workshopDescription')}
                </h2>
                <p className="text-gray-600 dark:text-gray-400 leading-relaxed">
                  {workshop.description}
                </p>
              </motion.div>

              {/* Schedule */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.1 }}
                className="bg-white dark:bg-gray-800 rounded-lg shadow p-6"
              >
                <h2 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">
                  {t('timeline.schedule')}
                </h2>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="flex items-center space-x-3 rtl:space-x-reverse">
                    <Calendar className="h-5 w-5 text-gray-400" />
                    <div>
                      <p className="text-sm text-gray-500 dark:text-gray-400">{t('timeline.startDate')}</p>
                      <p className="font-medium text-gray-900 dark:text-gray-100">
                        {new Date(workshop.startDate).toLocaleDateString('ar-SA')}
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-3 rtl:space-x-reverse">
                    <Clock className="h-5 w-5 text-gray-400" />
                    <div>
                      <p className="text-sm text-gray-500 dark:text-gray-400">وقت البداية</p>
                      <p className="font-medium text-gray-900 dark:text-gray-100">{workshop.startTime}</p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-3 rtl:space-x-reverse">
                    <Calendar className="h-5 w-5 text-gray-400" />
                    <div>
                      <p className="text-sm text-gray-500 dark:text-gray-400">تاريخ النهاية</p>
                      <p className="font-medium text-gray-900 dark:text-gray-100">
                        {new Date(workshop.endDate).toLocaleDateString('ar-SA')}
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-3 rtl:space-x-reverse">
                    <Clock className="h-5 w-5 text-gray-400" />
                    <div>
                      <p className="text-sm text-gray-500 dark:text-gray-400">وقت النهاية</p>
                      <p className="font-medium text-gray-900 dark:text-gray-100">{workshop.endTime}</p>
                    </div>
                  </div>
                </div>
              </motion.div>
            </div>

            {/* Sidebar */}
            <div className="space-y-6">
              {/* Quick Actions */}
              <motion.div
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                className="bg-white dark:bg-gray-800 rounded-lg shadow p-6"
              >
                <h2 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">
                  الإجراءات السريعة
                </h2>
                <div className="space-y-3">
                  <Button
                    variant="primary"
                    className="w-full justify-start"
                    onClick={() => setActiveTab('content')}
                    icon={FileText}
                  >
                    إدارة المحتوى
                  </Button>
                  <Button
                    variant="outline"
                    className="w-full justify-start"
                    onClick={handleManageMaterials}
                    icon={FileText}
                  >
                    إدارة المواد
                  </Button>
                  {workshop.status === 'scheduled' && (
                    <Button
                      variant="outline"
                      className="w-full justify-start"
                      onClick={handleAssignParticipants}
                      icon={UserPlus}
                    >
                      تعيين المشاركين
                    </Button>
                  )}
                  {workshop.status === 'scheduled' && workshop.enrolledCount > 0 && (
                    <Button
                      variant="outline"
                      className="w-full justify-start"
                      onClick={handleGenerateGroups}
                      icon={Shuffle}
                    >
                      إنشاء مجموعات
                    </Button>
                  )}
                  {workshop.groupsCount > 0 && (
                    <Button
                      variant="outline"
                      className="w-full justify-start"
                      onClick={handleManageGroups}
                      icon={MessageSquare}
                    >
                      إدارة المجموعات
                    </Button>
                  )}
                  {(workshop.status === 'ongoing' || workshop.status === 'completed') && (
                    <Button
                      variant="primary"
                      className="w-full justify-start"
                      onClick={handleGradeWorkshop}
                      icon={Award}
                    >
                      تقييم ورشة العمل
                    </Button>
                  )}
                </div>
              </motion.div>
            </div>
          </div>
        )}

        {activeTab === 'content' && (
          <ContentManager
            workshopId={parseInt(id)}
            workshopTitle={workshop.title}
          />
        )}

        {activeTab === 'participants' && (
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
            <h2 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">
              المشاركين في ورشة العمل
            </h2>
            {workshop.participants && workshop.participants.length > 0 ? (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {workshop.participants.map((participant, index) => (
                  <div key={index} className="flex items-center p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                    <div className="w-8 h-8 bg-primary-100 dark:bg-primary-900 rounded-full flex items-center justify-center mr-3 rtl:mr-0 rtl:ml-3">
                      <span className="text-primary-600 dark:text-primary-400 font-medium text-sm">
                        {typeof participant === 'string' ? participant.charAt(0) : participant.name?.charAt(0) || 'م'}
                      </span>
                    </div>
                    <span className="text-gray-900 dark:text-gray-100">
                      {typeof participant === 'string' ? participant : participant.name || 'مشارك'}
                    </span>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-8">
                <Users className="mx-auto h-12 w-12 text-gray-400" />
                <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-gray-100">لا يوجد مشاركين</h3>
                <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                  لم يتم تعيين أي مشاركين لهذه الورشة بعد
                </p>
              </div>
            )}
          </div>
        )}

        {activeTab === 'materials' && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
            className="bg-white dark:bg-gray-800 rounded-lg shadow p-6"
          >
            <h2 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">
              المواد التدريبية
            </h2>
            <div className="space-y-3">
              {workshop.materials && workshop.materials.length > 0 ? (
                workshop.materials.map((material, index) => (
                  <div key={material.id || index} className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                    <div className="flex items-center space-x-3 rtl:space-x-reverse">
                      <FileText className="h-5 w-5 text-gray-400" />
                      <div>
                        <p className="font-medium text-gray-900 dark:text-gray-100">{material.name || material.title || 'ملف غير محدد'}</p>
                        <p className="text-sm text-gray-500 dark:text-gray-400">{material.size || 'حجم غير محدد'}</p>
                      </div>
                    </div>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleDownloadMaterial(material)}
                      icon={Download}
                    >
                      تحميل
                    </Button>
                  </div>
                ))
              ) : (
                <div className="text-center py-8">
                  <FileText className="mx-auto h-12 w-12 text-gray-400" />
                  <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-gray-100">لا توجد مواد</h3>
                  <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                    لم يتم إضافة أي مواد تدريبية لهذه الورشة بعد
                  </p>
                  <div className="mt-4">
                    <Button
                      variant="primary"
                      onClick={() => setActiveTab('content')}
                      icon={FileText}
                    >
                      إضافة محتوى
                    </Button>
                  </div>
                </div>
              )}
            </div>
          </motion.div>
        )}
      </div>
    </div>
  )
}

export default WorkshopDetails
