import React, { useState, useEffect } from 'react'
import { useNavigate } from 'react-router-dom'
import { motion } from 'framer-motion'
import { useLanguage } from '../../contexts/LanguageContext'
import { useAuth } from '../../contexts/AuthContext'
import {
  Calendar,
  Clock,
  CheckCircle,
  CalendarDays,
  RotateCw,
  Zap,
  CheckSquare,
  Plus,
  Download,
  Upload,
  Eye,
  Settings,
  GraduationCap,
  Trash2,
  Search,
  Filter,
  Target,
  Star,
  Activity,
  Bell,
  ChevronRight,
  BarChart3,
  Timer,
  CheckCircle2,
  AlertCircle,
  Users,
  Award
} from 'lucide-react'
import TraineeHeader from '../../components/trainee/TraineeHeader'
import StatsCard from '../../components/trainee/StatsCard'
import SearchAndFilter from '../../components/trainee/SearchAndFilter'
import Button from '../../components/ui/Button'
import toast from 'react-hot-toast'

const TrainerTimelinePage = () => {
  const { user } = useAuth()
  const { language, t } = useLanguage()
  const navigate = useNavigate()
  const [selectedTab, setSelectedTab] = useState('all')
  const [searchTerm, setSearchTerm] = useState('')
  const [timelineEvents, setTimelineEvents] = useState([])
  const [loading, setLoading] = useState(true)
  const [stats, setStats] = useState({
    totalEvents: 0,
    totalWorkshops: 0,
    totalTasks: 0,
    upcomingEvents: 0,
    completedEvents: 0
  })

  // Function to select appropriate icon for event
  const getEventIcon = (event) => {
    const type = event.type || ''

    switch (type) {
      case 'workshop':
        return <Zap className="w-5 h-5" />
      case 'task':
        return <CheckSquare className="w-5 h-5" />
      case 'evaluation':
        return <Award className="w-5 h-5" />
      case 'meeting':
        return <Users className="w-5 h-5" />
      default:
        return <Calendar className="w-5 h-5" />
    }
  }

  // Load data when page loads
  useEffect(() => {
    loadTimelineData()
  }, [user])

  // Load timeline data
  const loadTimelineData = async () => {
    if (!user) return

    try {
      setLoading(true)
      console.log('🔄 Starting to load trainer timeline data...')

      // تحميل البيانات من localStorage
      const allTasks = JSON.parse(localStorage.getItem('app_tasks') || '[]')
      const allWorkshops = JSON.parse(localStorage.getItem('app_workshops') || '[]')

      // فلترة البيانات للمدرب الحالي
      const trainerTasks = allTasks.filter(task =>
        task.createdBy === user.name || task.trainer === user.name
      )

      const trainerWorkshops = allWorkshops.filter(workshop =>
        workshop.trainer === user.name || workshop.trainerId === user.id
      )

      // تحويل البيانات إلى أحداث تايم لاين
      const events = []

      // إضافة المهام
      trainerTasks.forEach(task => {
        events.push({
          id: `task_${task.id}`,
          title: task.title,
          description: task.description,
          type: 'task',
          date: task.dueDate,
          time: task.dueTime || '23:59',
          status: task.status || 'pending',
          priority: task.priority || 'medium',
          assignedCount: task.assignedUsers?.length || 0
        })
      })

      // إضافة ورش العمل
      trainerWorkshops.forEach(workshop => {
        events.push({
          id: `workshop_${workshop.id}`,
          title: workshop.title,
          description: workshop.description,
          type: 'workshop',
          date: workshop.startDate,
          time: workshop.startTime || '09:00',
          status: workshop.status || 'active',
          participantsCount: workshop.selectedTrainees?.length || 0
        })
      })

      // ترتيب الأحداث حسب التاريخ
      events.sort((a, b) => new Date(a.date) - new Date(b.date))

      setTimelineEvents(events)

      // حساب الإحصائيات
      const now = new Date()
      const upcomingEvents = events.filter(event => {
        const eventDate = new Date(event.date)
        return eventDate > now && event.status !== 'completed'
      }).length

      const completedEvents = events.filter(event =>
        event.status === 'completed' || event.status === 'graded'
      ).length

      setStats({
        totalEvents: events.length,
        totalWorkshops: trainerWorkshops.length,
        totalTasks: trainerTasks.length,
        upcomingEvents,
        completedEvents
      })

      console.log('✅ Trainer timeline data loaded successfully')
    } catch (error) {
      console.error('❌ Error loading timeline data:', error)
      toast.error('Failed to load timeline data')
    } finally {
      setLoading(false)
    }
  }

  // دالة الفلترة والبحث
  const getFilteredEvents = () => {
    let filtered = timelineEvents

    // فلترة حسب التبويب المحدد
    const now = new Date()
    if (selectedTab === 'upcoming') {
      filtered = filtered.filter(event => {
        const eventDate = new Date(event.date)
        return eventDate > now && event.status !== 'completed'
      })
    } else if (selectedTab === 'completed') {
      filtered = filtered.filter(event =>
        event.status === 'completed' || event.status === 'graded'
      )
    } else if (selectedTab === 'workshop') {
      filtered = filtered.filter(event => event.type === 'workshop')
    } else if (selectedTab === 'task') {
      filtered = filtered.filter(event => event.type === 'task')
    }

    // فلترة حسب البحث
    if (searchTerm) {
      filtered = filtered.filter(event =>
        event.title?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        event.description?.toLowerCase().includes(searchTerm.toLowerCase())
      )
    }

    return filtered.sort((a, b) => new Date(a.date) - new Date(b.date))
  }

  const filteredEvents = getFilteredEvents()

  // تبويبات الفلترة
  const tabs = [
    { id: 'all', label: t('timeline.allEvents'), count: stats.totalEvents },
    { id: 'upcoming', label: t('timeline.upcoming'), count: stats.upcomingEvents },
    { id: 'completed', label: t('timeline.completed'), count: stats.completedEvents },
    { id: 'workshop', label: t('timeline.workshops'), count: stats.totalWorkshops },
    { id: 'task', label: t('timeline.tasks'), count: stats.totalTasks }
  ]

  // دالة للحصول على لون الحالة
  const getStatusColor = (status) => {
    switch (status) {
      case 'completed':
      case 'graded':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
      case 'in_progress':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200'
      case 'pending':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'
      case 'overdue':
        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200'
    }
  }

  // دالة للحصول على لون النوع
  const getTypeColor = (type) => {
    switch (type) {
      case 'workshop':
        return 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200'
      case 'task':
        return 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200'
      case 'evaluation':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200'
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200'
    }
  }



  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600 dark:text-gray-400">جاري تحميل التايم لاين...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 py-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <TraineeHeader
          title="الجدول الزمني للمدرب"
          subtitle="تتبع جميع أنشطتك التدريبية في مكان واحد"
          icon={CalendarDays}
          iconBgColor="bg-blue-100 dark:bg-blue-900/30"
          iconColor="text-blue-600 dark:text-blue-400"
        />

        {/* Stats Cards */}
        <div className="grid grid-cols-2 lg:grid-cols-5 gap-4 mb-8">
          <StatsCard
            icon={CalendarDays}
            value={stats.totalEvents}
            label="إجمالي الأحداث"
            subtitle="حدث مجدول"
            iconBgColor="bg-blue-100 dark:bg-blue-900/30"
            iconColor="text-blue-600 dark:text-blue-400"
            onClick={() => setSelectedTab('all')}
            delay={0.1}
          />
          <StatsCard
            icon={Zap}
            value={stats.totalWorkshops}
            label="ورش العمل"
            subtitle="ورشة تدريبية"
            iconBgColor="bg-purple-100 dark:bg-purple-900/30"
            iconColor="text-purple-600 dark:text-purple-400"
            onClick={() => navigate('/trainer-workshops')}
            delay={0.2}
          />
          <StatsCard
            icon={CheckSquare}
            value={stats.totalTasks}
            label="المهام"
            subtitle="مهمة تدريبية"
            iconBgColor="bg-orange-100 dark:bg-orange-900/30"
            iconColor="text-orange-600 dark:text-orange-400"
            onClick={() => navigate('/trainer/evaluation')}
            delay={0.3}
          />
          <StatsCard
            icon={Timer}
            value={stats.upcomingEvents}
            label="أحداث قادمة"
            subtitle="حدث قادم"
            iconBgColor="bg-green-100 dark:bg-green-900/30"
            iconColor="text-green-600 dark:text-green-400"
            onClick={() => setSelectedTab('upcoming')}
            delay={0.4}
          />
          <StatsCard
            icon={CheckCircle}
            value={stats.completedEvents}
            label="أحداث مكتملة"
            subtitle="حدث مكتمل"
            iconBgColor="bg-indigo-100 dark:bg-indigo-900/30"
            iconColor="text-indigo-600 dark:text-indigo-400"
            onClick={() => setSelectedTab('completed')}
            delay={0.5}
          />
        </div>

        {/* Search and Filter */}
        <SearchAndFilter
          searchTerm={searchTerm}
          setSearchTerm={setSearchTerm}
          selectedTab={selectedTab}
          setSelectedTab={setSelectedTab}
          tabs={tabs}
          searchPlaceholder={t('timeline.search')}
        />

        {/* Action Buttons */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.7 }}
          className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700 mb-8"
        >
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 flex items-center">
              <Settings className="w-5 h-5 ml-2 text-primary-600 dark:text-primary-400" />
              إدارة التايم لاين
            </h3>
            <div className="text-sm text-gray-500 dark:text-gray-400">
              أدوات لتنظيم وإدارة جدولك الزمني
            </div>
          </div>

          <div className="flex flex-wrap gap-3">
            <Button
              variant="primary"
              onClick={() => navigate('/trainer-workshops')}
              icon={Plus}
              className="bg-gradient-to-r from-primary-600 to-secondary-600 hover:from-primary-700 hover:to-secondary-700 text-white"
            >
              إنشاء ورشة جديدة
            </Button>

            <Button
              variant="outline"
              onClick={() => navigate('/trainer-workshops')}
              icon={Eye}
              className="border-2 border-primary-600 text-primary-600 hover:bg-primary-50 dark:border-primary-400 dark:text-primary-400 dark:hover:bg-primary-900/20"
            >
              عرض ورش العمل
            </Button>

            <Button
              variant="success"
              onClick={() => navigate('/trainer/evaluation')}
              icon={Award}
              className="bg-gradient-to-r from-secondary-600 to-accent-600 hover:from-secondary-700 hover:to-accent-700 text-white"
            >
              تقييم المهام
            </Button>
          </div>
        </motion.div>

        {/* Timeline Events */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.8 }}
        >
          {filteredEvents.length === 0 ? (
            <div className="text-center py-16">
              <div className="bg-white dark:bg-gray-800 rounded-xl p-8 shadow-sm border border-gray-200 dark:border-gray-700 max-w-md mx-auto">
                <CalendarDays className="mx-auto h-16 w-16 text-gray-400 mb-4" />
                <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2">
                  {t('timeline.noEvents')}
                </h3>
                <p className="text-gray-500 dark:text-gray-400 text-sm mb-6">
                  {searchTerm || selectedTab !== 'all'
                    ? t('timeline.noMatchingEvents')
                    : t('timeline.noScheduledEvents')
                  }
                </p>
                <Button
                  onClick={() => navigate('/trainer-workshops')}
                  variant="primary"
                  icon={Plus}
                  size="sm"
                >
                  إنشاء ورشة جديدة
                </Button>
              </div>
            </div>
          ) : (
            <div className="space-y-6">
              {filteredEvents.map((event, index) => (
                <motion.div
                  key={event.id}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: index * 0.1 }}
                  className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700 hover:shadow-md transition-all duration-300"
                >
                  <div className="flex items-start justify-between">
                    <div className="flex items-start space-x-4 rtl:space-x-reverse flex-1">
                      <div className={`p-3 rounded-xl ${getTypeColor(event.type)} flex-shrink-0`}>
                        {getEventIcon(event)}
                      </div>
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center space-x-3 rtl:space-x-reverse mb-2">
                          <h4 className="text-lg font-semibold text-gray-900 dark:text-gray-100 truncate">
                            {event.title}
                          </h4>
                          <span className={`px-2 py-1 rounded-full text-xs font-medium ${getTypeColor(event.type)}`}>
                            {event.type === 'workshop' ? 'ورشة عمل' :
                             event.type === 'task' ? 'مهمة' :
                             event.type === 'evaluation' ? 'تقييم' : 'حدث'}
                          </span>
                        </div>
                        <p className="text-gray-600 dark:text-gray-400 text-sm mb-3 line-clamp-2">
                          {event.description}
                        </p>
                        <div className="flex items-center space-x-6 rtl:space-x-reverse text-sm text-gray-500 dark:text-gray-400">
                          <div className="flex items-center space-x-1 rtl:space-x-reverse">
                            <Calendar className="w-4 h-4" />
                            <span>{new Date(event.date).toLocaleDateString(language === 'ar' ? 'ar-SA' : 'en-US')}</span>
                          </div>
                          <div className="flex items-center space-x-1 rtl:space-x-reverse">
                            <Clock className="w-4 h-4" />
                            <span>{event.time}</span>
                          </div>
                          {event.type === 'workshop' && event.participantsCount && (
                            <div className="flex items-center space-x-1 rtl:space-x-reverse">
                              <Users className="w-4 h-4" />
                              <span>{event.participantsCount} participants</span>
                            </div>
                          )}
                          {event.type === 'task' && event.assignedCount && (
                            <div className="flex items-center space-x-1 rtl:space-x-reverse">
                              <Target className="w-4 h-4" />
                              <span>{event.assignedCount} متدرب</span>
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                    <div className="flex items-center space-x-3 rtl:space-x-reverse flex-shrink-0">
                      {event.status && (
                        <span className={`px-3 py-1 rounded-full text-xs font-medium ${getStatusColor(event.status)}`}>
                          {event.status === 'completed' ? 'مكتمل' :
                           event.status === 'graded' ? 'مقيم' :
                           event.status === 'in_progress' ? 'جاري' : 'معلق'}
                        </span>
                      )}
                      <Button
                        variant="outline"
                        size="sm"
                        icon={Eye}
                        onClick={() => {
                          if (event.type === 'workshop') navigate('/trainer-workshops')
                          else if (event.type === 'task') navigate('/trainer/evaluation')
                          else toast.success('سيتم إضافة صفحة التفاصيل قريباً')
                        }}
                      >
                        عرض التفاصيل
                      </Button>
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>
          )}
        </motion.div>
      </div>
    </div>
  )
}

export default TrainerTimelinePage
