import React, { useState, useEffect } from 'react'
import { useNavigate, useLocation } from 'react-router-dom'
import { motion } from 'framer-motion'
import toast from 'react-hot-toast'
import {
  Plus,
  Search,
  Filter,
  Download,
  Upload,
  Edit,
  Trash2,
  Eye,
  Users,
  Calendar,
  Clock,
  CheckSquare,
  User,
  FileText,
  Bell,
  Target,
  Award,
  AlertCircle,
  Send,
  XCircle,
  Settings,
  Save
} from 'lucide-react'
import Button from '../../components/ui/Button'
import CreateTaskModal from '../../components/modals/CreateTaskModal'
import { useLanguage } from '../../contexts/LanguageContext'

// Separate component for buttons
const TaskActionButtons = ({ task, onView, onEdit, onSubmissions, onDelete, onGrade, onAssign }) => {
  return (
    <>
      <div className="flex flex-wrap gap-2">
        <button
          type="button"
          onClick={onView}
          className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:text-gray-200 dark:hover:bg-gray-600"
        >
          <Eye className="w-4 h-4 mr-2 rtl:mr-0 rtl:ml-2" />
          {t('common.view')}
        </button>
        <button
          type="button"
          onClick={onEdit}
          className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:text-gray-200 dark:hover:bg-gray-600"
        >
          <Edit className="w-4 h-4 mr-2 rtl:mr-0 rtl:ml-2" />
          {t('common.edit')}
        </button>
        <button
          type="button"
          onClick={onSubmissions}
          className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:text-gray-200 dark:hover:bg-gray-600"
        >
          <Upload className="w-4 h-4 mr-2 rtl:mr-0 rtl:ml-2" />
          {t('tasks.submissions')} ({task.submittedCount || 0})
        </button>
        <button
          type="button"
          onClick={onDelete}
          className="inline-flex items-center px-3 py-2 border border-red-300 shadow-sm text-sm leading-4 font-medium rounded-md text-red-700 bg-white hover:bg-red-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 dark:bg-gray-700 dark:border-red-600 dark:text-red-400 dark:hover:bg-red-900/20"
        >
          <Trash2 className="w-4 h-4 mr-2 rtl:mr-0 rtl:ml-2" />
          {t('common.delete')}
        </button>
      </div>

      <div className="flex flex-wrap gap-2">
        {(task.submittedCount || 0) > (task.gradedCount || 0) && (
          <button
            type="button"
            onClick={onGrade}
            className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
          >
            <Award className="w-4 h-4 mr-2 rtl:mr-0 rtl:ml-2" />
            {t('tasks.grade')} ({Math.max(0, (task.submittedCount || 0) - (task.gradedCount || 0))})
          </button>
        )}
        <button
          type="button"
          onClick={onAssign}
          className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:text-gray-200 dark:hover:bg-gray-600"
        >
          <Users className="w-4 h-4 mr-2 rtl:mr-0 rtl:ml-2" />
          {t('tasks.assign')}
        </button>
      </div>
    </>
  )
}

const TaskManagement = () => {
  const { t, language } = useLanguage()
  const navigate = useNavigate()
  const location = useLocation()
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedFilter, setSelectedFilter] = useState('all')
  const [showCreateModal, setShowCreateModal] = useState(false)

  const [isExporting, setIsExporting] = useState(false)
  const [tasks, setTasks] = useState([])

  // States for modals
  const [editModal, setEditModal] = useState({ show: false, task: null })
  const [assignModal, setAssignModal] = useState({ show: false, task: null })
  const [editForm, setEditForm] = useState({
    title: '',
    description: '',
    content: '',
    dueDate: '',
    course: '',
    trainer: '',
    files: []
  })
  const [availableUsers, setAvailableUsers] = useState([])
  const [selectedUsers, setSelectedUsers] = useState([])

  // Global grading settings
  const [showGradingSettings, setShowGradingSettings] = useState(false)
  const [globalGradingSettings, setGlobalGradingSettings] = useState({
    maxGrade: 100,
    passingGrade: 60,
    gradingScale: 'percentage', // percentage, points, letter
    autoCalculateGPA: true,
    allowPartialGrading: true
  })

  // Load tasks from localStorage on startup
  useEffect(() => {
    const loadTasks = () => {
      try {
        // Load tasks from app_tasks first
        let allTasks = JSON.parse(localStorage.getItem('app_tasks') || '[]')

        // If no tasks in app_tasks, try tasks_data
        if (allTasks.length === 0) {
          allTasks = JSON.parse(localStorage.getItem('tasks_data') || '[]')
        }

        // Load submissions to calculate statistics
        const allSubmissions = JSON.parse(localStorage.getItem('task_submissions') || '[]')

        // Add submission and grading statistics for each task
        const enrichedTasks = allTasks.map(task => {
          const taskSubmissions = allSubmissions.filter(sub => sub.taskId === task.id)
          const gradedSubmissions = taskSubmissions.filter(sub => sub.graded === true)

          // Calculate average grades with conversion to percentage
          let avgScore = 0
          if (gradedSubmissions.length > 0) {
            // Get grading settings
            const gradingSettings = JSON.parse(localStorage.getItem('global_grading_settings') || '{}')
            const maxGrade = gradingSettings.maxGrade || 100

            // Calculate average
            const totalGrades = gradedSubmissions.reduce((sum, sub) => sum + (sub.grade || 0), 0)
            const avgRawGrade = totalGrades / gradedSubmissions.length

            // Convert to percentage
            avgScore = Math.round((avgRawGrade / maxGrade) * 100)
          }

          return {
            ...task,
            submittedCount: taskSubmissions.length,
            gradedCount: gradedSubmissions.length,
            assignedCount: task.assignedUsers ? task.assignedUsers.length : 0,
            avgScore: avgScore
          }
        })

        console.log('📋 Loading tasks in admin page:', enrichedTasks.length)
        console.log('📋 Task details:', enrichedTasks.map(t => ({
          id: t.id,
          title: t.title,
          status: t.status,
          submitted: t.submittedCount,
          graded: t.gradedCount,
          assigned: t.assignedCount
        })))

        setTasks(enrichedTasks)
      } catch (error) {
        console.error('Error loading tasks:', error)
        setTasks([])
      }
    }

    loadTasks()
  }, [])

  // Load available users for assignment
  useEffect(() => {
    const loadUsers = async () => {
      try {
        const { authService } = await import('../../services/authService')
        const users = await authService.getAllUsers()
        const trainees = users.filter(user => user.role === 'trainee')
        setAvailableUsers(trainees)
      } catch (error) {
        console.error('Error loading users:', error)
        setAvailableUsers([])
      }
    }
    loadUsers()
  }, [])

  // Load global grading settings
  useEffect(() => {
    const loadGradingSettings = () => {
      try {
        const savedSettings = JSON.parse(localStorage.getItem('global_grading_settings') || '{}')
        if (Object.keys(savedSettings).length > 0) {
          setGlobalGradingSettings(prev => ({ ...prev, ...savedSettings }))
        }
      } catch (error) {
        console.error('Error loading grading settings:', error)
      }
    }
    loadGradingSettings()
  }, [])

  // Open grading settings automatically when coming from task details page
  useEffect(() => {
    if (location.state?.openGradingSettings) {
      setShowGradingSettings(true)
      // Clear state to avoid opening settings again on reload
      window.history.replaceState({}, document.title)
    }
  }, [location.state])


  const filteredTasks = tasks.filter(task => {
    const matchesSearch = task.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         (task.course || '').toLowerCase().includes(searchTerm.toLowerCase()) ||
                         (task.trainer || '').toLowerCase().includes(searchTerm.toLowerCase())

    let matchesFilter = true
    switch (selectedFilter) {
      case 'all':
        matchesFilter = true
        break
      case 'submitted':
        matchesFilter = (task.submittedCount || 0) > 0
        break
      case 'not_submitted':
        matchesFilter = (task.submittedCount || 0) === 0
        break
      case 'graded':
        matchesFilter = (task.gradedCount || 0) > 0
        break
      case 'not_graded':
        matchesFilter = (task.submittedCount || 0) > (task.gradedCount || 0)
        break
      case 'pending_grading':
        matchesFilter = (task.submittedCount || 0) > (task.gradedCount || 0)
        break
      default:
        matchesFilter = task.status === selectedFilter
    }

    return matchesSearch && matchesFilter
  })

  const getStatusColor = (status) => {
    switch (status) {
      case 'published': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
      case 'draft': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'
      case 'archived': return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200'
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200'
    }
  }

  const getPriorityColor = (priority) => {
    switch (priority) {
      case 'high': return 'border-l-4 border-red-500 bg-red-50 dark:bg-red-900/10'
      case 'medium': return 'border-l-4 border-yellow-500 bg-yellow-50 dark:bg-yellow-900/10'
      case 'low': return 'border-l-4 border-green-500 bg-green-50 dark:bg-green-900/10'
      default: return 'border-l-4 border-gray-300'
    }
  }

  const getDaysUntilDue = (dueDate) => {
    const today = new Date()
    const due = new Date(dueDate)
    const diffTime = due - today
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
    return diffDays
  }

  // Button handlers
  const handleCreateTask = () => {
    setShowCreateModal(true)
  }

  const handleTaskCreated = (newTask) => {
    setTasks(prev => [...prev, newTask])
    setShowCreateModal(false)
    toast.success(t('tasks.createSuccess'))
  }



  const handleExportTasks = async () => {
    setIsExporting(true)
    try {
      // Import ExcelJS
      const ExcelJS = await import('exceljs')
      const { saveAs } = await import('file-saver')

      // Get user custom colors
      const getUserThemeColors = () => {
        const currentUser = JSON.parse(localStorage.getItem('currentUser') || '{}')
        const userId = currentUser.id || 'default'

        return {
          primary: localStorage.getItem(`primaryColor_${userId}`) || '#4F46E5',
          secondary: localStorage.getItem(`secondaryColor_${userId}`) || '#7C3AED'
        }
      }

      const colors = getUserThemeColors()

      // Create new workbook
      const workbook = new ExcelJS.Workbook()
      const worksheet = workbook.addWorksheet(t('tasks.trainingTasks'), {
        rightToLeft: true
      })

      // Add main title
      worksheet.mergeCells('A1:L1')
      const titleCell = worksheet.getCell('A1')
      titleCell.value = `📋 ${t('tasks.taskReport')}`
      titleCell.font = { size: 16, bold: true, color: { argb: 'FFFFFFFF' } }
      titleCell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: colors.primary.replace('#', 'FF') }
      }
      titleCell.alignment = { horizontal: 'center', vertical: 'middle' }

      // Add report information
      worksheet.mergeCells('A2:L2')
      const infoCell = worksheet.getCell('A2')
      infoCell.value = `${t('tasks.reportDate')}: ${new Date().toLocaleDateString(language === 'ar' ? 'ar-SA' : 'en-US')} | ${t('tasks.totalTasks')}: ${filteredTasks.length}`
      infoCell.font = { size: 10, italic: true }
      infoCell.alignment = { horizontal: 'center' }

      // Column headers
      const headers = [
        t('tasks.taskTitle'), t('common.description'), t('tasks.course'), t('tasks.instructor'), t('tasks.createdDate'),
        t('tasks.dueDate'), t('tasks.priority'), t('tasks.status'), t('tasks.maxGrade'),
        t('tasks.traineeCount'), t('tasks.assignedTrainees'), t('tasks.notes')
      ]

      // Add column headers
      headers.forEach((header, index) => {
        const cell = worksheet.getCell(4, index + 1)
        cell.value = header
        cell.font = { bold: true, color: { argb: 'FFFFFFFF' } }
        cell.fill = {
          type: 'pattern',
          pattern: 'solid',
          fgColor: { argb: colors.secondary.replace('#', 'FF') }
        }
        cell.alignment = { horizontal: 'center', vertical: 'middle' }
        cell.border = {
          top: { style: 'thin' },
          left: { style: 'thin' },
          bottom: { style: 'thin' },
          right: { style: 'thin' }
        }
      })

      // Add task data
      filteredTasks.forEach((task, index) => {
        const rowNum = index + 5

        // Prepare trainee list
        const assignedUsers = task.assignedUsers?.join(', ') || t('tasks.noTrainees')

        // Determine task status with icons
        let statusDisplay = ''
        let statusColor = 'FF000000'
        switch(task.status) {
          case 'pending':
            statusDisplay = `⏳ ${t('tasks.pending')}`
            statusColor = 'FFFF6600'
            break
          case 'in_progress':
            statusDisplay = `🔄 ${t('common.inProgress')}`
            statusColor = 'FF0066CC'
            break
          case 'completed':
            statusDisplay = `✅ ${t('common.completed')}`
            statusColor = 'FF00AA00'
            break
          case 'overdue':
            statusDisplay = `🔴 ${t('tasks.overdue')}`
            statusColor = 'FFCC0000'
            break
          default:
            statusDisplay = `❓ ${t('common.notSpecified')}`
            statusColor = 'FF666666'
        }

        // Determine priority with icons
        let priorityDisplay = ''
        let priorityColor = 'FF000000'
        switch(task.priority) {
          case 'high':
            priorityDisplay = `🔴 ${t('tasks.high')}`
            priorityColor = 'FFCC0000'
            break
          case 'medium':
            priorityDisplay = `🟡 ${t('tasks.medium')}`
            priorityColor = 'FFFF6600'
            break
          case 'low':
            priorityDisplay = `🟢 ${t('tasks.low')}`
            priorityColor = 'FF00AA00'
            break
          default:
            priorityDisplay = `❓ ${t('common.notSpecified')}`
            priorityColor = 'FF666666'
        }

        const rowData = [
          task.title || t('common.notSpecified'),
          task.description || t('tasks.noDescription'),
          task.course || t('common.notSpecified'),
          task.instructor || t('common.notSpecified'),
          task.createdAt ? new Date(task.createdAt).toLocaleDateString(language === 'ar' ? 'ar-SA' : 'en-US') : t('common.notSpecified'),
          task.dueDate ? new Date(task.dueDate).toLocaleDateString(language === 'ar' ? 'ar-SA' : 'en-US') : t('common.notSpecified'),
          priorityDisplay,
          statusDisplay,
          task.maxScore || 100,
          task.assignedUsers?.length || 0,
          assignedUsers,
          task.notes || t('tasks.noNotes')
        ]

        rowData.forEach((value, colIndex) => {
          const cell = worksheet.getCell(rowNum, colIndex + 1)
          cell.value = value

          // Special formatting for status and priority columns
          if (colIndex === 6) { // Priority
            cell.font = { bold: true, color: { argb: priorityColor } }
          } else if (colIndex === 7) { // Status
            cell.font = { bold: true, color: { argb: statusColor } }
          }

          // Cell borders
          cell.border = {
            top: { style: 'thin' },
            left: { style: 'thin' },
            bottom: { style: 'thin' },
            right: { style: 'thin' }
          }

          // Alternate row coloring
          if (index % 2 === 0) {
            cell.fill = {
              type: 'pattern',
              pattern: 'solid',
              fgColor: { argb: 'FFF8F9FA' }
            }
          }
        })
      })

      // Set column widths
      const columnWidths = [25, 35, 20, 20, 15, 15, 15, 15, 12, 12, 40, 30]
      columnWidths.forEach((width, index) => {
        worksheet.getColumn(index + 1).width = width
      })

      // Save file
      const buffer = await workbook.xlsx.writeBuffer()
      const blob = new Blob([buffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' })
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-').split('T')[0]
      const fileName = language === 'ar' ? `المهام_التدريبية_${timestamp}.xlsx` : `training_tasks_${timestamp}.xlsx`

      saveAs(blob, fileName)

      toast.success(t('tasks.exportSuccess', { count: filteredTasks.length }))
      console.log('✅ Tasks exported with advanced formatting')

    } catch (error) {
      console.error('❌ Export error:', error)
      toast.error(t('tasks.exportError') + ': ' + error.message)
    } finally {
      setIsExporting(false)
    }
  }

  const handleViewTask = (taskId) => {
    console.log('👁️ View task details:', taskId)
    console.log('🔄 Navigating to:', `/admin/tasks/${taskId}`)
    toast.success(t('tasks.navigatingToDetails'))

    // استخدام navigate مباشرة بدون setTimeout
    try {
      navigate(`/admin/tasks/${taskId}`)
    } catch (error) {
      console.error('Navigation error:', error)
      // fallback to window.location
      window.location.href = `/admin/tasks/${taskId}`
    }
  }

  const handleEditTask = (taskId) => {
    console.log('✏️ Edit task:', taskId)
    const task = tasks.find(t => t.id === taskId)
    if (task) {
      setEditForm({
        title: task.title || '',
        description: task.description || '',
        content: task.content || '',
        dueDate: task.dueDate ? task.dueDate.split('T')[0] : '',
        course: task.course || '',
        trainer: task.trainer || '',
        files: task.files || []
      })
      setEditModal({ show: true, task })
    }
  }

  const handleManageFiles = (taskId) => {
    console.log('📁 View submissions and files:', taskId)
    console.log('🔄 Navigating to:', `/admin/tasks/${taskId}/submissions`)
    toast.success('🔄 Navigating to submissions page...')

    try {
      navigate(`/admin/tasks/${taskId}/submissions`)
    } catch (error) {
      console.error('Navigation error:', error)
      window.location.href = `/admin/tasks/${taskId}/submissions`
    }
  }

  const handleGradeTask = (taskId) => {
    console.log('📊 Evaluate task:', taskId)
    console.log('🔄 Navigating to:', `/admin/tasks/${taskId}/grading`)
    toast.success('🔄 Navigating to grading page...')

    try {
      navigate(`/admin/tasks/${taskId}/grading`)
    } catch (error) {
      console.error('Navigation error:', error)
      window.location.href = `/admin/tasks/${taskId}/grading`
    }
  }

  const handleAssignTask = (taskId) => {
    console.log('👥 Assign task:', taskId)
    const task = tasks.find(t => t.id === taskId)
    if (task) {
      setSelectedUsers(task.assignedUsers || [])
      setAssignModal({ show: true, task })
    }
  }

  const handleDeleteTask = async (e, taskId) => {
    e.preventDefault()
    e.stopPropagation()
    console.log('🗑️ Delete task:', taskId)
    if (window.confirm(t('tasks.confirmDelete'))) {
      try {
        // Delete task from localStorage
        const allTasks = JSON.parse(localStorage.getItem('app_tasks') || '[]')
        const updatedTasks = allTasks.filter(task => task.id !== taskId)
        localStorage.setItem('app_tasks', JSON.stringify(updatedTasks))
        localStorage.setItem('tasks_data', JSON.stringify(updatedTasks))

        // Update local list
        setTasks(prev => prev.filter(task => task.id !== taskId))

        toast.success(t('tasks.deleteSuccess'))
      } catch (error) {
        console.error('Error deleting task:', error)
        toast.error(t('tasks.deleteError'))
      }
    }
  }

  const handleMoreFilters = () => {
    toast.info(t('tasks.moreFiltersComingSoon'))
  }

  const handleSendNotification = () => {
    toast.info(t('tasks.notificationComingSoon'))
  }

  // Save global grading settings
  const handleSaveGradingSettings = () => {
    try {
      const settingsToSave = {
        ...globalGradingSettings,
        updatedAt: new Date().toISOString()
      }
      localStorage.setItem('global_grading_settings', JSON.stringify(settingsToSave))

      // Trigger custom event to notify other pages of update
      const customEvent = new CustomEvent('localStorageChange', {
        detail: {
          key: 'global_grading_settings',
          value: settingsToSave
        }
      })
      window.dispatchEvent(customEvent)

      toast.success(t('tasks.gradingSettingsSaved'))
      console.log('🔄 Grading settings saved and notification sent to other pages:', settingsToSave)
      setShowGradingSettings(false)
    } catch (error) {
      console.error('Error saving grading settings:', error)
      toast.error(t('tasks.gradingSettingsError'))
    }
  }

  // File management in editing
  const handleFileUpload = (e) => {
    const files = Array.from(e.target.files)
    const fileObjects = files.map(file => ({
      name: file.name,
      size: file.size,
      type: file.type,
      url: URL.createObjectURL(file),
      file: file
    }))
    setEditForm(prev => ({
      ...prev,
      files: [...prev.files, ...fileObjects]
    }))
  }

  const removeFile = (index) => {
    setEditForm(prev => ({
      ...prev,
      files: prev.files.filter((_, i) => i !== index)
    }))
  }

  // Save task edit
  const handleSaveEdit = async () => {
    if (!editForm.title.trim()) {
      toast.error(t('tasks.enterTaskTitleError'))
      return
    }

    try {
      const updatedTask = {
        ...editModal.task,
        title: editForm.title.trim(),
        description: editForm.description.trim(),
        content: editForm.content.trim(),
        dueDate: editForm.dueDate,
        course: editForm.course.trim(),
        trainer: editForm.trainer.trim(),
        files: editForm.files,
        updatedAt: new Date().toISOString()
      }

      // Update in localStorage
      const allTasks = JSON.parse(localStorage.getItem('app_tasks') || '[]')
      const updatedTasks = allTasks.map(task =>
        task.id === editModal.task.id ? updatedTask : task
      )
      localStorage.setItem('app_tasks', JSON.stringify(updatedTasks))
      localStorage.setItem('tasks_data', JSON.stringify(updatedTasks))

      // Update local state
      setTasks(prev => prev.map(task =>
        task.id === editModal.task.id ? { ...task, ...updatedTask } : task
      ))

      setEditModal({ show: false, task: null })
      setEditForm({ title: '', description: '', content: '', dueDate: '', course: '', trainer: '', files: [] })
      toast.success(t('tasks.updateSuccess'))
    } catch (error) {
      console.error('Error updating task:', error)
      toast.error(t('tasks.updateError'))
    }
  }

  // Save user assignment
  const handleSaveAssignment = async () => {
    try {
      const updatedTask = {
        ...assignModal.task,
        assignedUsers: selectedUsers,
        assignedCount: selectedUsers.length,
        updatedAt: new Date().toISOString()
      }

      // Update in localStorage
      const allTasks = JSON.parse(localStorage.getItem('app_tasks') || '[]')
      const updatedTasks = allTasks.map(task =>
        task.id === assignModal.task.id ? updatedTask : task
      )
      localStorage.setItem('app_tasks', JSON.stringify(updatedTasks))
      localStorage.setItem('tasks_data', JSON.stringify(updatedTasks))

      // Update local state
      setTasks(prev => prev.map(task =>
        task.id === assignModal.task.id ? { ...task, ...updatedTask } : task
      ))

      setAssignModal({ show: false, task: null })
      setSelectedUsers([])
      toast.success(t('tasks.assignSuccess', { count: selectedUsers.length }))
    } catch (error) {
      console.error('Error assigning task:', error)
      toast.error(t('tasks.assignError'))
    }
  }



  const stats = [
    { name: t('tasks.totalTasks'), value: tasks.length, icon: CheckSquare, color: 'bg-blue-500' },
    { name: t('tasks.submitted'), value: tasks.reduce((sum, t) => sum + (t.submittedCount || 0), 0), icon: Upload, color: 'bg-green-500' },
    { name: t('tasks.graded'), value: tasks.reduce((sum, t) => sum + (t.gradedCount || 0), 0), icon: Award, color: 'bg-purple-500' },
    { name: t('tasks.pendingGrading'), value: tasks.reduce((sum, t) => sum + Math.max(0, (t.submittedCount || 0) - (t.gradedCount || 0)), 0), icon: Clock, color: 'bg-orange-500' }
  ]

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100">
            {t('tasks.title')}
          </h1>
          <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
            {t('tasks.subtitle')}
          </p>
        </div>
        <div className="mt-4 sm:mt-0 flex flex-col sm:flex-row gap-3">
          <Button
            variant="outline"
            onClick={() => setShowGradingSettings(!showGradingSettings)}
            icon={Settings}
          >
            {t('tasks.gradingSettings')}
          </Button>
          <Button
            variant="outline"
            onClick={handleExportTasks}
            loading={isExporting}
            icon={Download}
          >
            {t('common.export')}
          </Button>
          <Button
            variant="primary"
            onClick={handleCreateTask}
            icon={Plus}
          >
            {t('tasks.create')}
          </Button>
        </div>
      </div>

      {/* Grading Settings */}
      {showGradingSettings && (
        <motion.div
          initial={{ opacity: 0, height: 0 }}
          animate={{ opacity: 1, height: 'auto' }}
          exit={{ opacity: 0, height: 0 }}
          className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6"
        >
          <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4 flex items-center">
            <Settings className="w-5 h-5 mr-2 rtl:mr-0 rtl:ml-2" />
            {t('tasks.globalGradingSettings')}
          </h3>
          <p className="text-sm text-gray-600 dark:text-gray-400 mb-6">
            {t('tasks.gradingSettingsDescription')}
          </p>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                {t('tasks.defaultMaxGrade')}
              </label>
              <input
                type="number"
                min="1"
                max="1000"
                value={globalGradingSettings.maxGrade}
                onChange={(e) => setGlobalGradingSettings(prev => ({
                  ...prev,
                  maxGrade: parseInt(e.target.value) || 100
                }))}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent dark:bg-gray-700 dark:text-gray-100"
                placeholder={t('tasks.enterMaxGrade')}
              />
              <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                {t('tasks.gradeExample')}
              </p>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                {t('tasks.passingGrade')}
              </label>
              <input
                type="number"
                min="1"
                max={globalGradingSettings.maxGrade}
                value={globalGradingSettings.passingGrade}
                onChange={(e) => setGlobalGradingSettings(prev => ({
                  ...prev,
                  passingGrade: parseInt(e.target.value) || 60
                }))}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent dark:bg-gray-700 dark:text-gray-100"
                placeholder={t('tasks.enterPassingGrade')}
              />
              <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                {t('tasks.minimumToPass')}
              </p>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                {t('tasks.gradingSystem')}
              </label>
              <select
                value={globalGradingSettings.gradingScale}
                onChange={(e) => setGlobalGradingSettings(prev => ({
                  ...prev,
                  gradingScale: e.target.value
                }))}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent dark:bg-gray-700 dark:text-gray-100"
              >
                <option value="percentage">{t('tasks.percentage')}</option>
                <option value="points">{t('tasks.points')}</option>
                <option value="letter">{t('tasks.letters')}</option>
              </select>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-6">
            <div className="flex items-center">
              <input
                type="checkbox"
                id="autoCalculateGPA"
                checked={globalGradingSettings.autoCalculateGPA}
                onChange={(e) => setGlobalGradingSettings(prev => ({
                  ...prev,
                  autoCalculateGPA: e.target.checked
                }))}
                className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
              />
              <label htmlFor="autoCalculateGPA" className="mr-2 rtl:mr-0 rtl:ml-2 block text-sm text-gray-900 dark:text-gray-100">
                {t('tasks.autoCalculateGPA')}
              </label>
            </div>

            <div className="flex items-center">
              <input
                type="checkbox"
                id="allowPartialGrading"
                checked={globalGradingSettings.allowPartialGrading}
                onChange={(e) => setGlobalGradingSettings(prev => ({
                  ...prev,
                  allowPartialGrading: e.target.checked
                }))}
                className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
              />
              <label htmlFor="allowPartialGrading" className="mr-2 rtl:mr-0 rtl:ml-2 block text-sm text-gray-900 dark:text-gray-100">
                {t('tasks.allowPartialGrading')}
              </label>
            </div>
          </div>

          <div className="mt-6 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
            <h4 className="text-sm font-medium text-blue-900 dark:text-blue-100 mb-2">
              {t('tasks.currentGradingSystemPreview')}
            </h4>
            <div className="grid grid-cols-2 md:grid-cols-5 gap-2 text-xs">
              <div className="text-green-600">
                {t('tasks.excellent')}: {Math.round(globalGradingSettings.maxGrade * 0.9)}-{globalGradingSettings.maxGrade}
              </div>
              <div className="text-blue-600">
                {t('tasks.veryGood')}: {Math.round(globalGradingSettings.maxGrade * 0.8)}-{Math.round(globalGradingSettings.maxGrade * 0.89)}
              </div>
              <div className="text-yellow-600">
                {t('tasks.good')}: {Math.round(globalGradingSettings.maxGrade * 0.7)}-{Math.round(globalGradingSettings.maxGrade * 0.79)}
              </div>
              <div className="text-orange-600">
                {t('tasks.acceptable')}: {globalGradingSettings.passingGrade}-{Math.round(globalGradingSettings.maxGrade * 0.69)}
              </div>
              <div className="text-red-600">
                {t('tasks.weak')}: 0-{globalGradingSettings.passingGrade - 1}
              </div>
            </div>
          </div>

          <div className="flex items-center justify-end space-x-3 rtl:space-x-reverse mt-6">
            <Button
              variant="outline"
              onClick={() => setShowGradingSettings(false)}
            >
              {t('common.cancel')}
            </Button>
            <Button
              variant="primary"
              onClick={handleSaveGradingSettings}
              icon={Save}
            >
              {t('common.saveSettings')}
            </Button>
          </div>
        </motion.div>
      )}

      {/* Stats */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
        {stats.map((stat, index) => {
          const Icon = stat.icon
          return (
            <motion.div
              key={stat.name}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
              className="card"
            >
              <div className="flex items-center">
                <div className={`p-3 rounded-xl ${stat.color}`}>
                  <Icon className="h-6 w-6 text-white" />
                </div>
                <div className="ml-4 rtl:ml-0 rtl:mr-4">
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                    {stat.name}
                  </p>
                  <p className="text-2xl font-semibold text-gray-900 dark:text-gray-100">
                    {stat.value}
                  </p>
                </div>
              </div>
            </motion.div>
          )
        })}
      </div>

      {/* Filters and Search */}
      <div className="card">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0">
          <div className="flex flex-col sm:flex-row gap-4">
            {/* Search */}
            <div className="relative">
              <Search className="absolute left-3 rtl:left-auto rtl:right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <input
                type="text"
                placeholder={t('tasks.searchTasks')}
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 rtl:pl-4 rtl:pr-10 input-field w-full sm:w-64"
              />
            </div>

            {/* Status Filter */}
            <select
              value={selectedFilter}
              onChange={(e) => setSelectedFilter(e.target.value)}
              className="input-field w-full sm:w-auto"
            >
              <option value="all">{t('tasks.allTasks')}</option>
              <option value="submitted">{t('tasks.submitted')}</option>
              <option value="not_submitted">{t('tasks.notSubmitted')}</option>
              <option value="graded">{t('tasks.graded')}</option>
              <option value="pending_grading">{t('tasks.pendingGrading')}</option>
            </select>
          </div>

          <div className="flex flex-col sm:flex-row gap-2">
            <Button
              variant="outline"
              onClick={handleMoreFilters}
              icon={Filter}
            >
              {t('tasks.moreFilters')}
            </Button>
            <Button
              variant="outline"
              onClick={handleSendNotification}
              icon={Send}
            >
              {t('tasks.sendNotification')}
            </Button>
          </div>
        </div>
      </div>

      {/* Tasks Grid */}
      <div className="grid grid-cols-1 xl:grid-cols-2 gap-6 card-grid">
        {filteredTasks.map((task, index) => {
          const daysUntilDue = getDaysUntilDue(task.dueDate)
          const isOverdue = daysUntilDue < 0 && task.status === 'published'
          const completionRate = task.assignedCount > 0 ? Math.round((task.submittedCount / task.assignedCount) * 100) : 0

          return (
            <motion.div
              key={task.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
              className={`card h-full flex flex-col ${getPriorityColor(task.priority)} ${isOverdue ? 'ring-2 ring-red-500' : ''}`}
            >
              {/* Task Header */}
              <div className="flex items-start justify-between mb-4 flex-grow">
                <div className="flex-1 min-w-0">
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2 truncate">
                    {task.title}
                  </h3>
                  <p className="text-sm text-gray-600 dark:text-gray-400 line-clamp-2 mb-3 min-h-[2.5rem]">
                    {task.description}
                  </p>
                  <div className="flex flex-wrap items-center gap-4 text-sm text-gray-500 dark:text-gray-400">
                    <div className="flex items-center">
                      <User className="w-4 h-4 mr-1 rtl:mr-0 rtl:ml-1" />
                      {task.trainer}
                    </div>
                    <div className="flex items-center">
                      <FileText className="w-4 h-4 mr-1 rtl:mr-0 rtl:ml-1" />
                      {task.course}
                    </div>
                  </div>
                </div>
                <div className="flex flex-col items-end space-y-2">
                  <span className={`px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(task.status)}`}>
                    {task.status.charAt(0).toUpperCase() + task.status.slice(1)}
                  </span>
                  {task.reminderSet && (
                    <div className="flex items-center text-xs text-blue-600 dark:text-blue-400">
                      <Bell className="w-3 h-3 mr-1 rtl:mr-0 rtl:ml-1" />
                      {t('tasks.reminderSet')}
                    </div>
                  )}
                </div>
              </div>

              {/* Task Stats */}
              <div className="grid grid-cols-2 sm:grid-cols-4 gap-4 mb-4">
                <div className="text-center p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                  <div className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                    {task.assignedCount}
                  </div>
                  <div className="text-xs text-gray-500 dark:text-gray-400">{t('tasks.assigned')}</div>
                </div>
                <div className="text-center p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                  <div className="text-2xl font-bold text-green-600 dark:text-green-400">
                    {task.submittedCount || 0}
                  </div>
                  <div className="text-xs text-gray-500 dark:text-gray-400">{t('tasks.submitted')}</div>
                </div>
                <div className="text-center p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                  <div className="text-2xl font-bold text-blue-600 dark:text-blue-400">
                    {task.gradedCount || 0}
                  </div>
                  <div className="text-xs text-gray-500 dark:text-gray-400">{t('tasks.graded')}</div>
                </div>
                <div className="text-center p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                  <div className="text-2xl font-bold text-purple-600 dark:text-purple-400">
                    {(task.avgScore && task.avgScore > 0) ? `${task.avgScore}%` : '-'}
                  </div>
                  <div className="text-xs text-gray-500 dark:text-gray-400">{t('tasks.averageGrade')}</div>
                </div>
              </div>

              {/* Progress Bar */}
              {task.status === 'published' && (
                <div className="mb-4">
                  <div className="flex justify-between text-xs text-gray-500 dark:text-gray-400 mb-1">
                    <span>{t('tasks.completionRate')}</span>
                    <span>{completionRate}%</span>
                  </div>
                  <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                    <div
                      className="bg-green-500 h-2 rounded-full transition-all duration-300"
                      style={{ width: `${completionRate}%` }}
                    ></div>
                  </div>
                </div>
              )}

              {/* Due Date */}
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center text-sm">
                  <Calendar className="w-4 h-4 mr-2 rtl:mr-0 rtl:ml-2 text-gray-400" />
                  <span className="text-gray-600 dark:text-gray-400">
                    {t('tasks.dueDate')}: {new Date(task.dueDate).toLocaleDateString(language === 'ar' ? 'ar-SA' : 'en-US')}
                  </span>
                </div>
                {isOverdue && (
                  <div className="flex items-center text-red-600 dark:text-red-400 text-sm">
                    <AlertCircle className="w-4 h-4 mr-1 rtl:mr-0 rtl:ml-1" />
                    {t('tasks.overdue')}
                  </div>
                )}
                {daysUntilDue > 0 && daysUntilDue <= 3 && task.status === 'published' && (
                  <div className="flex items-center text-yellow-600 dark:text-yellow-400 text-sm">
                    <Clock className="w-4 h-4 mr-1 rtl:mr-0 rtl:ml-1" />
                    {t('tasks.daysRemaining', { days: daysUntilDue })}
                  </div>
                )}
              </div>

              {/* Action Buttons */}
              <div className="flex flex-col sm:flex-row items-stretch sm:items-center justify-between gap-3 pt-4 border-t border-gray-200 dark:border-gray-700 mt-auto">
                <div className="flex flex-wrap gap-2">
                  <button
                    type="button"
                    onClick={() => {
                      console.log('🔥 View button clicked:', task.id)
                      console.log('🔄 Navigating to:', `${task.id}`)
                      toast.success(t('tasks.navigatingToDetails'))

                      // استخدام navigate مباشرة بدون setTimeout
                      try {
                        navigate(`${task.id}`)
                      } catch (error) {
                        console.error('Navigation error:', error)
                        // fallback to window.location
                        window.location.href = `/admin/tasks/${task.id}`
                      }
                    }}
                    className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:text-gray-200 dark:hover:bg-gray-600"
                  >
                    <Eye className="w-4 h-4 mr-2 rtl:mr-0 rtl:ml-2" />
                    {t('common.view')}
                  </button>

                  <button
                    type="button"
                    onClick={() => {
                      console.log('🔥 Edit button clicked:', task.id)
                      const taskToEdit = tasks.find(t => t.id === task.id)
                      if (taskToEdit) {
                        setEditForm({
                          title: taskToEdit.title || '',
                          description: taskToEdit.description || '',
                          content: taskToEdit.content || '',
                          dueDate: taskToEdit.dueDate ? taskToEdit.dueDate.split('T')[0] : '',
                          course: taskToEdit.course || '',
                          trainer: taskToEdit.trainer || '',
                          files: taskToEdit.files || []
                        })
                        setEditModal({ show: true, task: taskToEdit })
                        toast.success(t('tasks.openingEditModal'))
                      }
                    }}
                    className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:text-gray-200 dark:hover:bg-gray-600"
                  >
                    <Edit className="w-4 h-4 mr-2 rtl:mr-0 rtl:ml-2" />
                    {t('common.edit')}
                  </button>

                  <button
                    type="button"
                    onClick={() => {
                      console.log('🔥 Submissions button clicked:', task.id)
                      console.log('🔄 Navigating to:', `${task.id}/submissions`)
                      toast.success(t('tasks.navigatingToSubmissions'))

                      // استخدام navigate مباشرة بدون setTimeout
                      try {
                        navigate(`${task.id}/submissions`)
                      } catch (error) {
                        console.error('Navigation error:', error)
                        // fallback to window.location
                        window.location.href = `/admin/tasks/${task.id}/submissions`
                      }
                    }}
                    className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:text-gray-200 dark:hover:bg-gray-600"
                  >
                    <Upload className="w-4 h-4 mr-2 rtl:mr-0 rtl:ml-2" />
                    {t('tasks.submissions')} ({task.submittedCount || 0})
                  </button>

                  <button
                    type="button"
                    onClick={() => {
                      console.log('🔥 Delete button clicked:', task.id)
                      if (window.confirm(t('tasks.confirmDelete'))) {
                        const allTasks = JSON.parse(localStorage.getItem('app_tasks') || '[]')
                        const updatedTasks = allTasks.filter(t => t.id !== task.id)
                        localStorage.setItem('app_tasks', JSON.stringify(updatedTasks))
                        localStorage.setItem('tasks_data', JSON.stringify(updatedTasks))
                        setTasks(prev => prev.filter(t => t.id !== task.id))
                        toast.success(t('tasks.deleteSuccess'))
                      }
                    }}
                    className="inline-flex items-center px-3 py-2 border border-red-300 shadow-sm text-sm leading-4 font-medium rounded-md text-red-700 bg-white hover:bg-red-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 dark:bg-gray-700 dark:border-red-600 dark:text-red-400 dark:hover:bg-red-900/20"
                  >
                    <Trash2 className="w-4 h-4 mr-2 rtl:mr-0 rtl:ml-2" />
                    {t('common.delete')}
                  </button>
                </div>

                <div className="flex flex-wrap gap-2">
                  {(task.submittedCount || 0) > (task.gradedCount || 0) && (
                    <button
                      type="button"
                      onClick={() => {
                        console.log('🔥 Grading button clicked:', task.id)
                        console.log('🔄 Navigating to:', `${task.id}/grading`)
                        toast.success(t('tasks.navigatingToGrading'))

                        // استخدام navigate مباشرة بدون setTimeout
                        try {
                          navigate(`${task.id}/grading`)
                        } catch (error) {
                          console.error('Navigation error:', error)
                          // fallback to window.location
                          window.location.href = `/admin/tasks/${task.id}/grading`
                        }
                      }}
                      className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                    >
                      <Award className="w-4 h-4 mr-2 rtl:mr-0 rtl:ml-2" />
                      {t('tasks.grade')} ({Math.max(0, (task.submittedCount || 0) - (task.gradedCount || 0))})
                    </button>
                  )}

                  <button
                    type="button"
                    onClick={() => {
                      console.log('🔥 Assignment button clicked:', task.id)
                      const taskToAssign = tasks.find(t => t.id === task.id)
                      if (taskToAssign) {
                        setSelectedUsers(taskToAssign.assignedUsers || [])
                        setAssignModal({ show: true, task: taskToAssign })
                        toast.success(t('tasks.openingAssignModal'))
                      }
                    }}
                    className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:text-gray-200 dark:hover:bg-gray-600"
                  >
                    <Users className="w-4 h-4 mr-2 rtl:mr-0 rtl:ml-2" />
                    {t('tasks.assign')}
                  </button>
                </div>
              </div>
            </motion.div>
          )
        })}
      </div>

      {filteredTasks.length === 0 && (
        <div className="text-center py-12">
          <CheckSquare className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-gray-100">{t('tasks.noTasks')}</h3>
          <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
            {selectedFilter === 'all' ? t('tasks.noTasksCreated') : t('tasks.noTasksForFilter')}
          </p>
          <div className="mt-6">
            <Button
              variant="primary"
              onClick={handleCreateTask}
              icon={Plus}
            >
              {t('tasks.createNewTask')}
            </Button>
          </div>
        </div>
      )}

      {/* Create Task Modal */}
      <CreateTaskModal
        isOpen={showCreateModal}
        onClose={() => setShowCreateModal(false)}
        onTaskCreated={handleTaskCreated}
      />

      {/* Edit Task Modal */}
      {editModal.show && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white dark:bg-gray-800 rounded-lg p-6 w-full max-w-md mx-4">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">
              {t('tasks.editTask')}: {editModal.task?.title}
            </h3>

            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  {t('tasks.taskTitle')} *
                </label>
                <input
                  type="text"
                  value={editForm.title}
                  onChange={(e) => setEditForm(prev => ({ ...prev, title: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent dark:bg-gray-700 dark:text-gray-100"
                  placeholder={t('tasks.enterTaskTitle')}
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  {t('common.description')}
                </label>
                <textarea
                  value={editForm.description}
                  onChange={(e) => setEditForm(prev => ({ ...prev, description: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent dark:bg-gray-700 dark:text-gray-100"
                  placeholder={t('tasks.enterTaskDescription')}
                  rows="3"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  {t('tasks.taskContent')}
                </label>
                <textarea
                  value={editForm.content}
                  onChange={(e) => setEditForm(prev => ({ ...prev, content: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent dark:bg-gray-700 dark:text-gray-100"
                  placeholder={t('tasks.enterTaskContent')}
                  rows="4"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  {t('tasks.dueDate')}
                </label>
                <input
                  type="date"
                  value={editForm.dueDate}
                  onChange={(e) => setEditForm(prev => ({ ...prev, dueDate: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent dark:bg-gray-700 dark:text-gray-100"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  {t('tasks.course')}
                </label>
                <input
                  type="text"
                  value={editForm.course}
                  onChange={(e) => setEditForm(prev => ({ ...prev, course: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent dark:bg-gray-700 dark:text-gray-100"
                  placeholder={t('tasks.enterCourseName')}
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  {t('tasks.instructor')}
                </label>
                <input
                  type="text"
                  value={editForm.trainer}
                  onChange={(e) => setEditForm(prev => ({ ...prev, trainer: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent dark:bg-gray-700 dark:text-gray-100"
                  placeholder={t('tasks.enterInstructorName')}
                />
              </div>

              {/* Files section */}
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  {t('tasks.attachedFiles')}
                </label>

                {/* Display current files */}
                {editForm.files.length > 0 && (
                  <div className="mb-3">
                    <div className="space-y-2">
                      {editForm.files.map((file, index) => (
                        <div key={index} className="flex items-center justify-between bg-gray-50 dark:bg-gray-700 rounded-lg p-3">
                          <div className="flex items-center space-x-3 rtl:space-x-reverse">
                            <FileText className="w-4 h-4 text-blue-600" />
                            <div>
                              <div className="text-sm font-medium text-gray-900 dark:text-gray-100">
                                {file.name}
                              </div>
                              <div className="text-xs text-gray-500 dark:text-gray-400">
                                {file.size ? `${(file.size / 1024).toFixed(1)} KB` : t('tasks.unknownSize')}
                              </div>
                            </div>
                          </div>
                          <button
                            type="button"
                            onClick={() => removeFile(index)}
                            className="text-red-600 hover:text-red-700 p-1"
                          >
                            <XCircle className="w-4 h-4" />
                          </button>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {/* Upload new files */}
                <div className="border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg p-4">
                  <input
                    type="file"
                    multiple
                    onChange={handleFileUpload}
                    className="hidden"
                    id="file-upload-edit"
                    accept=".pdf,.doc,.docx,.txt,.jpg,.jpeg,.png,.gif"
                  />
                  <label
                    htmlFor="file-upload-edit"
                    className="cursor-pointer flex flex-col items-center justify-center"
                  >
                    <Upload className="w-8 h-8 text-gray-400 mb-2" />
                    <span className="text-sm text-gray-600 dark:text-gray-400">
                      {t('tasks.clickToUpload')}
                    </span>
                    <span className="text-xs text-gray-500 dark:text-gray-500 mt-1">
                      {t('tasks.fileTypes')}
                    </span>
                  </label>
                </div>
              </div>
            </div>

            <div className="flex justify-end space-x-3 rtl:space-x-reverse mt-6">
              <Button
                variant="outline"
                onClick={() => {
                  setEditModal({ show: false, task: null })
                  setEditForm({ title: '', description: '', content: '', dueDate: '', course: '', trainer: '', files: [] })
                }}
              >
                {t('common.cancel')}
              </Button>
              <Button
                variant="primary"
                onClick={handleSaveEdit}
                icon={Edit}
                disabled={!editForm.title.trim()}
              >
                {t('common.saveChanges')}
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* Assign Task Modal */}
      {assignModal.show && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white dark:bg-gray-800 rounded-lg p-6 w-full max-w-lg mx-4">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">
              {t('tasks.assignTask')}: {assignModal.task?.title}
            </h3>

            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  {t('tasks.selectTrainees')} ({selectedUsers.length} {t('common.selected')})
                </label>
                <div className="max-h-60 overflow-y-auto border border-gray-300 dark:border-gray-600 rounded-lg p-3">
                  {availableUsers.length > 0 ? (
                    availableUsers.map(user => (
                      <label key={user.id} className="flex items-center space-x-3 rtl:space-x-reverse py-2">
                        <input
                          type="checkbox"
                          checked={selectedUsers.some(u => u.id === user.id)}
                          onChange={(e) => {
                            if (e.target.checked) {
                              setSelectedUsers(prev => [...prev, user])
                            } else {
                              setSelectedUsers(prev => prev.filter(u => u.id !== user.id))
                            }
                          }}
                          className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                        />
                        <div className="flex-1">
                          <div className="text-sm font-medium text-gray-900 dark:text-gray-100">
                            {user.name}
                          </div>
                          <div className="text-xs text-gray-500 dark:text-gray-400">
                            {user.email}
                          </div>
                        </div>
                      </label>
                    ))
                  ) : (
                    <div className="text-center py-4 text-gray-500 dark:text-gray-400">
                      {t('tasks.noTraineesAvailable')}
                    </div>
                  )}
                </div>
              </div>

              <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-3">
                <div className="text-sm text-blue-800 dark:text-blue-200">
                  <strong>{t('tasks.currentlySelected')}:</strong> {selectedUsers.length} {t('common.trainee')}
                </div>
                {selectedUsers.length > 0 && (
                  <div className="mt-2 flex flex-wrap gap-1">
                    {selectedUsers.slice(0, 3).map(user => (
                      <span key={user.id} className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-blue-100 text-blue-800 dark:bg-blue-800 dark:text-blue-100">
                        {user.name}
                      </span>
                    ))}
                    {selectedUsers.length > 3 && (
                      <span className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200">
                        +{selectedUsers.length - 3} {t('common.more')}
                      </span>
                    )}
                  </div>
                )}
              </div>
            </div>

            <div className="flex justify-end space-x-3 rtl:space-x-reverse mt-6">
              <Button
                variant="outline"
                onClick={() => {
                  setAssignModal({ show: false, task: null })
                  setSelectedUsers([])
                }}
              >
                {t('common.cancel')}
              </Button>
              <Button
                variant="primary"
                onClick={handleSaveAssignment}
                icon={Users}
              >
                {t('tasks.saveAssignment')} ({selectedUsers.length})
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default TaskManagement
