import React, { useState, useEffect } from 'react'
import { useParams, useNavigate } from 'react-router-dom'
import { motion } from 'framer-motion'
import { ArrowLeft } from 'lucide-react'
import Button from '../../components/ui/Button'
import { useLanguage } from '../../contexts/LanguageContext'
import toast from 'react-hot-toast'
import dataStorage from '../../utils/dataStorage'
import GroupManager from '../../components/workshop/GroupManager'

const ManageGroups = () => {
  const { id } = useParams()
  const navigate = useNavigate()
  const { t } = useLanguage()
  const [workshop, setWorkshop] = useState(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    console.log('ManageGroups component mounted with ID:', id)
    loadWorkshopDetails()
  }, [id])

  const loadWorkshopDetails = async () => {
    try {
      setLoading(true)

      // تحميل ورشة العمل من جميع المصادر
      const allWorkshops = dataStorage.loadWorkshops() || []
      console.log('🔍 جميع ورش العمل المحملة:', allWorkshops)
      console.log('🎯 البحث عن ورشة بالمعرف:', id, typeof id)

      const currentWorkshop = allWorkshops.find(w => {
        const workshopId = w.id
        const searchId = id
        console.log('🔍 مقارنة:', { workshopId, searchId, match: workshopId == searchId })
        return workshopId == searchId || workshopId === parseInt(searchId) || workshopId.toString() === searchId.toString()
      })

      console.log('🎯 ورشة العمل الموجودة:', currentWorkshop)

      if (!currentWorkshop) {
        console.log('❌ ورشة العمل غير موجودة بالمعرف:', id)
        console.log('📋 ورش العمل المتاحة:', allWorkshops.map(w => ({ id: w.id, title: w.title })))

        // إذا لم توجد ورش عمل، إنشاء ورشة تجريبية
        if (allWorkshops.length === 0) {
          const demoWorkshop = dataStorage.createDemoWorkshopIfNeeded()

          // إذا كان المعرف المطلوب هو الورشة التجريبية
          if (demoWorkshop && (id === 'demo-workshop-1' || id === '1')) {
            setWorkshop(demoWorkshop)
            setLoading(false)
            return
          }
        }

        toast.error('ورشة العمل غير موجودة')
        navigate('/workshops')
        return
      }

      console.log('🔍 ManageGroups - Workshop found:', {
        id: currentWorkshop.id,
        title: currentWorkshop.title,
        selectedTraineesCount: currentWorkshop.selectedTrainees?.length || 0,
        selectedTrainees: currentWorkshop.selectedTrainees?.slice(0, 3) // عرض أول 3 فقط
      })

      setWorkshop(currentWorkshop)
    } catch (error) {
      console.error('Error loading workshop details:', error)
      toast.error('فشل في تحميل بيانات ورشة العمل: ' + error.message)
    } finally {
      setLoading(false)
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary-600"></div>
      </div>
    )
  }

  if (!workshop) {
    return (
      <div className="text-center py-12">
        <div className="max-w-md mx-auto">
          <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
            ورشة العمل غير موجودة
          </h3>
          <p className="text-gray-500 dark:text-gray-400 mb-4">
            لم يتم العثور على ورشة العمل المطلوبة. يمكنك إنشاء ورشة عمل جديدة أو العودة إلى قائمة ورش العمل.
          </p>
          <div className="flex flex-col sm:flex-row gap-3 justify-center">
            <button
              onClick={() => navigate('/workshops')}
              className="px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors"
            >
              العودة إلى ورش العمل
            </button>
            <button
              onClick={() => navigate('/workshops?create=true')}
              className="px-4 py-2 bg-secondary-600 text-white rounded-lg hover:bg-secondary-700 transition-colors"
            >
              إنشاء ورشة عمل جديدة
            </button>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4 rtl:space-x-reverse">
          <Button
            variant="outline"
            onClick={() => navigate('/workshops')}
            icon={ArrowLeft}
          >
            العودة
          </Button>
          <div>
            <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100">
              إدارة المجموعات
            </h1>
            <p className="text-gray-600 dark:text-gray-400 mt-1">
              {workshop.title}
            </p>
          </div>
        </div>
      </div>

      {/* GroupManager Component */}
      <GroupManager
        workshopId={parseInt(id)}
        workshopTitle={workshop.title}
        participants={workshop.selectedTrainees || []}
      />
    </div>
  )
}

export default ManageGroups
