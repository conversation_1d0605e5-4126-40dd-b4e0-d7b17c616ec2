import React, { useState, useEffect } from 'react'
import { useParams, useNavigate } from 'react-router-dom'
import { motion } from 'framer-motion'
import { ArrowLeft } from 'lucide-react'
import Button from '../../components/ui/Button'
import { useLanguage } from '../../contexts/LanguageContext'
import toast from 'react-hot-toast'
import dataStorage from '../../utils/dataStorage'
import GroupManager from '../../components/workshop/GroupManager'

const ManageGroups = () => {
  const { id } = useParams()
  const navigate = useNavigate()
  const { t } = useLanguage()
  const [workshop, setWorkshop] = useState(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    console.log('ManageGroups component mounted with ID:', id)
    loadWorkshopDetails()
  }, [id])

  const loadWorkshopDetails = async () => {
    try {
      setLoading(true)

      // تحميل ورشة العمل من جميع المصادر
      const allWorkshops = dataStorage.loadWorkshops()
      const currentWorkshop = allWorkshops.find(w => w.id === parseInt(id) || w.id === id)

      if (!currentWorkshop) {
        console.log('Workshop not found with ID:', id)
        toast.error('ورشة العمل غير موجودة')
        navigate('/workshops')
        return
      }

      console.log('🔍 ManageGroups - Workshop found:', {
        id: currentWorkshop.id,
        title: currentWorkshop.title,
        selectedTraineesCount: currentWorkshop.selectedTrainees?.length || 0,
        selectedTrainees: currentWorkshop.selectedTrainees?.slice(0, 3) // عرض أول 3 فقط
      })

      setWorkshop(currentWorkshop)
    } catch (error) {
      console.error('Error loading workshop details:', error)
      toast.error('فشل في تحميل بيانات ورشة العمل: ' + error.message)
    } finally {
      setLoading(false)
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary-600"></div>
      </div>
    )
  }

  if (!workshop) {
    return (
      <div className="text-center py-12">
        <p className="text-gray-500 dark:text-gray-400">ورشة العمل غير موجودة</p>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4 rtl:space-x-reverse">
          <Button
            variant="outline"
            onClick={() => navigate('/workshops')}
            icon={ArrowLeft}
          >
            العودة
          </Button>
          <div>
            <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100">
              إدارة المجموعات
            </h1>
            <p className="text-gray-600 dark:text-gray-400 mt-1">
              {workshop.title}
            </p>
          </div>
        </div>
      </div>

      {/* GroupManager Component */}
      <GroupManager
        workshopId={parseInt(id)}
        workshopTitle={workshop.title}
        participants={workshop.selectedTrainees || []}
      />
    </div>
  )
}

export default ManageGroups
