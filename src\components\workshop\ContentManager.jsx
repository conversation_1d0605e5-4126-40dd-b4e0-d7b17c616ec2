import React, { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { toast } from 'react-hot-toast'
import { 
  FileText, 
  Plus, 
  Edit, 
  Trash2, 
  Download, 
  Upload, 
  Video, 
  Image, 
  Link,
  Save
} from 'lucide-react'
import Button from '../ui/Button'
import Modal from '../ui/Modal'
import { useLanguage } from '../../contexts/LanguageContext'

const ContentManager = ({ workshopId, workshopTitle }) => {
  const { t } = useLanguage()
  const [content, setContent] = useState({
    description: '',
    objectives: [],
    materials: [],
    videos: [],
    links: []
  })
  const [showAddModal, setShowAddModal] = useState(false)
  const [contentType, setContentType] = useState('material')
  const [newContent, setNewContent] = useState({
    title: '',
    description: '',
    url: '',
    file: null
  })
  const [loading, setLoading] = useState(false)

  useEffect(() => {
    loadWorkshopContent()
  }, [workshopId])

  const loadWorkshopContent = () => {
    try {
      const workshops = JSON.parse(localStorage.getItem('workshops_data') || '[]')
      const workshop = workshops.find(w => w.id === workshopId)
      
      if (workshop) {
        setContent({
          description: workshop.description || '',
          objectives: workshop.objectives || [],
          materials: workshop.materials || [],
          videos: workshop.videos || [],
          links: workshop.links || []
        })
      }
    } catch (error) {
      console.error('Error loading workshop content:', error)
      toast.error('Failed to load workshop content')
    }
  }

  const saveWorkshopContent = (updatedContent) => {
    try {
      const workshops = JSON.parse(localStorage.getItem('workshops_data') || '[]')
      const updatedWorkshops = workshops.map(workshop => {
        if (workshop.id === workshopId) {
          return {
            ...workshop,
            ...updatedContent,
            updatedAt: new Date().toISOString()
          }
        }
        return workshop
      })
      
      localStorage.setItem('workshops_data', JSON.stringify(updatedWorkshops))
      setContent(updatedContent)
      toast.success('تم حفظ المحتوى بنجاح')
    } catch (error) {
      console.error('Error saving workshop content:', error)
      toast.error('فشل في حفظ المحتوى')
    }
  }

  const handleDescriptionSave = () => {
    saveWorkshopContent({
      ...content,
      description: content.description
    })
  }

  const handleAddContent = () => {
    if (!newContent.title.trim()) {
      toast.error('يرجى إدخال العنوان')
      return
    }

    setLoading(true)
    try {
      const newItem = {
        id: Date.now(),
        title: newContent.title,
        description: newContent.description,
        url: newContent.url,
        addedAt: new Date().toISOString()
      }

      let updatedContent = { ...content }

      switch (contentType) {
        case 'material':
          updatedContent.materials = [...content.materials, newItem]
          break
        case 'video':
          updatedContent.videos = [...content.videos, newItem]
          break
        case 'link':
          updatedContent.links = [...content.links, newItem]
          break
        case 'objective':
          updatedContent.objectives = [...content.objectives, { 
            id: Date.now(), 
            text: newContent.title 
          }]
          break
      }

      saveWorkshopContent(updatedContent)
      handleCloseModal()
    } catch (error) {
      console.error('Error adding content:', error)
      toast.error('Failed to add content')
    } finally {
      setLoading(false)
    }
  }

  const handleDeleteContent = (type, itemId) => {
    if (confirm('هل أنت متأكد من حذف هذا العنصر؟')) {
      try {
        let updatedContent = { ...content }

        switch (type) {
          case 'material':
            updatedContent.materials = content.materials.filter(item => item.id !== itemId)
            break
          case 'video':
            updatedContent.videos = content.videos.filter(item => item.id !== itemId)
            break
          case 'link':
            updatedContent.links = content.links.filter(item => item.id !== itemId)
            break
          case 'objective':
            updatedContent.objectives = content.objectives.filter(item => item.id !== itemId)
            break
        }

        saveWorkshopContent(updatedContent)
      } catch (error) {
        console.error('Error deleting content:', error)
        toast.error('فشل في حذف العنصر')
      }
    }
  }

  const handleCloseModal = () => {
    setShowAddModal(false)
    setNewContent({
      title: '',
      description: '',
      url: '',
      file: null
    })
  }

  const getContentTypeIcon = (type) => {
    switch (type) {
      case 'material': return <FileText className="w-4 h-4" />
      case 'video': return <Video className="w-4 h-4" />
      case 'link': return <Link className="w-4 h-4" />
      default: return <FileText className="w-4 h-4" />
    }
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
            {t('workshops.workshopContent')}
          </h3>
          <p className="text-sm text-gray-500 dark:text-gray-400">
            {t('workshops.manageMaterialsResources')}
          </p>
        </div>
        <Button
          variant="primary"
          onClick={() => setShowAddModal(true)}
          icon={Plus}
          size="sm"
        >
          {t('workshops.addContent')}
        </Button>
      </div>

      {/* Description Section */}
      <div className="bg-white dark:bg-gray-800 rounded-lg p-6 border border-gray-200 dark:border-gray-700">
        <div className="flex items-center justify-between mb-4">
          <h4 className="text-md font-semibold text-gray-900 dark:text-gray-100">
            {t('workshops.workshopDescription')}
          </h4>
          <Button
            variant="outline"
            onClick={handleDescriptionSave}
            icon={Save}
            size="sm"
          >
            {t('common.save')}
          </Button>
        </div>
        <textarea
          value={content.description}
          onChange={(e) => setContent(prev => ({ ...prev, description: e.target.value }))}
          rows={4}
          className="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-primary-500 focus:border-transparent"
          placeholder={t('workshops.enterDescription')}
        />
      </div>

      {/* Objectives Section */}
      <ContentSection
        title={t('workshops.workshopObjectives')}
        items={content.objectives}
        type="objective"
        onDelete={handleDeleteContent}
        onAdd={() => {
          setContentType('objective')
          setShowAddModal(true)
        }}
        t={t}
      />

      {/* Materials Section */}
      <ContentSection
        title={t('workshops.materials')}
        items={content.materials}
        type="material"
        onDelete={handleDeleteContent}
        onAdd={() => {
          setContentType('material')
          setShowAddModal(true)
        }}
        t={t}
      />

      {/* Videos Section */}
      <ContentSection
        title={t('workshops.videos')}
        items={content.videos}
        type="video"
        onDelete={handleDeleteContent}
        onAdd={() => {
          setContentType('video')
          setShowAddModal(true)
        }}
        t={t}
      />

      {/* Links Section */}
      <ContentSection
        title={t('workshops.usefulLinks')}
        items={content.links}
        type="link"
        onDelete={handleDeleteContent}
        onAdd={() => {
          setContentType('link')
          setShowAddModal(true)
        }}
        t={t}
      />

      {/* Add Content Modal */}
      <Modal
        isOpen={showAddModal}
        onClose={handleCloseModal}
        title={`${t('common.add')} ${getContentTypeTitle(contentType, t)}`}
        size="md"
      >
        <div className="p-6 space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              {contentType === 'objective' ? t('workshops.objectives') : t('common.name')}
            </label>
            <input
              type="text"
              value={newContent.title}
              onChange={(e) => setNewContent(prev => ({ ...prev, title: e.target.value }))}
              className="input-field"
              placeholder={`${t('common.enterText')} ${contentType === 'objective' ? t('workshops.objectives') : t('common.name')}`}
            />
          </div>

          {contentType !== 'objective' && (
            <>
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  {t('common.description')} ({t('common.optional')})
                </label>
                <textarea
                  value={newContent.description}
                  onChange={(e) => setNewContent(prev => ({ ...prev, description: e.target.value }))}
                  rows={3}
                  className="input-field"
                  placeholder="أدخل وصف المحتوى"
                />
              </div>

              {(contentType === 'video' || contentType === 'link') && (
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    الرابط
                  </label>
                  <input
                    type="url"
                    value={newContent.url}
                    onChange={(e) => setNewContent(prev => ({ ...prev, url: e.target.value }))}
                    className="input-field"
                    placeholder="https://..."
                  />
                </div>
              )}
            </>
          )}

          <div className="flex gap-3 pt-4">
            <Button
              variant="outline"
              onClick={handleCloseModal}
              className="flex-1"
            >
              إلغاء
            </Button>
            <Button
              variant="primary"
              onClick={handleAddContent}
              loading={loading}
              className="flex-1"
            >
              إضافة
            </Button>
          </div>
        </div>
      </Modal>
    </div>
  )
}

// Content Section Component
const ContentSection = ({ title, items, type, onDelete, onAdd, t }) => {
  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg p-6 border border-gray-200 dark:border-gray-700">
      <div className="flex items-center justify-between mb-4">
        <h4 className="text-md font-semibold text-gray-900 dark:text-gray-100">
          {title}
        </h4>
        <Button
          variant="outline"
          onClick={onAdd}
          icon={Plus}
          size="sm"
        >
          {t('common.add')}
        </Button>
      </div>

      {items.length === 0 ? (
        <p className="text-gray-500 dark:text-gray-400 text-center py-4">
          {t('workshops.noContentYet')}
        </p>
      ) : (
        <div className="space-y-3">
          {items.map((item) => (
            <div
              key={item.id}
              className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg"
            >
              <div className="flex-1">
                <h5 className="font-medium text-gray-900 dark:text-gray-100">
                  {item.title || item.text}
                </h5>
                {item.description && (
                  <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                    {item.description}
                  </p>
                )}
                {item.url && (
                  <a
                    href={item.url}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-sm text-primary-600 hover:text-primary-700 mt-1 inline-block"
                  >
                    فتح الرابط
                  </a>
                )}
              </div>
              <button
                onClick={() => onDelete(type, item.id)}
                className="p-2 text-red-600 hover:bg-red-50 dark:hover:bg-red-900/20 rounded"
              >
                <Trash2 className="w-4 h-4" />
              </button>
            </div>
          ))}
        </div>
      )}
    </div>
  )
}

const getContentTypeTitle = (type, t) => {
  switch (type) {
    case 'objective': return t('workshops.objectives')
    case 'material': return t('workshops.materials')
    case 'video': return t('workshops.videos')
    case 'link': return t('workshops.usefulLinks')
    default: return t('workshops.workshopContent')
  }
}

export default ContentManager
