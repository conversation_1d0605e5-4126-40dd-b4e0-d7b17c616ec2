import React, { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { toast } from 'react-hot-toast'
import { Users, Plus, Edit, Trash2, Award, Target, Shuffle, Trophy, Star } from 'lucide-react'
import But<PERSON> from '../ui/Button'
import Modal from '../ui/Modal'
import QuickPointsManager from './QuickPointsManager'
import WinnerAnnouncement from './WinnerAnnouncement'
import CreateGroupModal from '../modals/CreateGroupModal'
import dataStorage from '../../utils/dataStorage'

const GroupManager = ({ workshopId, workshopTitle, participants = [] }) => {
  const [groups, setGroups] = useState([])
  const [showCreateModal, setShowCreateModal] = useState(false)
  const [showAdvancedGroupModal, setShowAdvancedGroupModal] = useState(false)
  const [editingGroup, setEditingGroup] = useState(null)
  const [newGroupName, setNewGroupName] = useState('')
  const [selectedMembers, setSelectedMembers] = useState([])
  const [loading, setLoading] = useState(false)
  const [showPointsManager, setShowPointsManager] = useState(false)
  const [selectedGroupForPoints, setSelectedGroupForPoints] = useState(null)
  const [showWinnerModal, setShowWinnerModal] = useState(false)

  // تشخيص بيانات المشاركين
  useEffect(() => {
    console.log('🔍 GroupManager - تشخيص بيانات المشاركين:', {
      workshopId,
      workshopTitle,
      participantsCount: participants.length,
      participants: participants.slice(0, 3), // عرض أول 3 مشاركين فقط
      participantsType: typeof participants,
      isArray: Array.isArray(participants)
    })
  }, [workshopId, participants])

  useEffect(() => {
    loadGroups()
  }, [workshopId])

  const loadGroups = () => {
    try {
      const workshopGroups = dataStorage.loadGroups(workshopId)
      setGroups(workshopGroups)
    } catch (error) {
      console.error('Error loading groups:', error)
      toast.error('فشل في تحميل المجموعات')
    }
  }

  const generateRandomGroups = (separateByGender = false) => {
    if (participants.length < 2) {
      toast.error('يجب أن يكون هناك متدربين على الأقل لإنشاء مجموعات')
      return
    }

    setLoading(true)
    try {
      let participantsToGroup = [...participants]
      const newGroups = []

      if (separateByGender) {
        // تقسيم المشاركين حسب الجنس
        const maleParticipants = participantsToGroup.filter(p => p.gender === 'ذكر')
        const femaleParticipants = participantsToGroup.filter(p => p.gender === 'أنثى')

        // إنشاء مجموعات للذكور
        if (maleParticipants.length > 0) {
          const maleGroups = createGroupsFromParticipants(maleParticipants, 'ذكور')
          newGroups.push(...maleGroups)
        }

        // إنشاء مجموعات للإناث
        if (femaleParticipants.length > 0) {
          const femaleGroups = createGroupsFromParticipants(femaleParticipants, 'إناث')
          newGroups.push(...femaleGroups)
        }
      } else {
        // خلط المشاركين عشوائياً
        const shuffledParticipants = participantsToGroup.sort(() => Math.random() - 0.5)
        const groups = createGroupsFromParticipants(shuffledParticipants, 'مختلط')
        newGroups.push(...groups)
      }

      // حفظ المجموعات
      dataStorage.saveGroups(workshopId, newGroups)
      setGroups(newGroups)

      toast.success(`تم إنشاء ${newGroups.length} مجموعة بنجاح`)
    } catch (error) {
      console.error('Error generating groups:', error)
      toast.error('فشل في إنشاء المجموعات')
    } finally {
      setLoading(false)
    }
  }

  const createGroupsFromParticipants = (participantsList, genderType) => {
    // تحديد عدد المجموعات (حد أقصى 6 أشخاص لكل مجموعة)
    const groupSize = Math.min(6, Math.max(2, Math.ceil(participantsList.length / 4)))
    const numberOfGroups = Math.ceil(participantsList.length / groupSize)
    const groups = []

    for (let i = 0; i < numberOfGroups; i++) {
      const startIndex = i * groupSize
      const endIndex = Math.min(startIndex + groupSize, participantsList.length)
      const groupMembers = participantsList.slice(startIndex, endIndex)

      const groupName = genderType === 'مختلط'
        ? `المجموعة ${i + 1}`
        : `مجموعة ${genderType} ${i + 1}`

      const group = {
        id: `group_${Date.now()}_${genderType}_${i}`,
        name: groupName,
        members: groupMembers,
        points: 0,
        color: getRandomColor(),
        genderType: genderType,
        createdAt: new Date().toISOString()
      }

      groups.push(group)
    }

    return groups
  }

  const getRandomColor = () => {
    const colors = [
      'bg-blue-100 text-blue-800 border-blue-200',
      'bg-green-100 text-green-800 border-green-200',
      'bg-purple-100 text-purple-800 border-purple-200',
      'bg-yellow-100 text-yellow-800 border-yellow-200',
      'bg-pink-100 text-pink-800 border-pink-200',
      'bg-indigo-100 text-indigo-800 border-indigo-200',
      'bg-red-100 text-red-800 border-red-200',
      'bg-orange-100 text-orange-800 border-orange-200'
    ]
    return colors[Math.floor(Math.random() * colors.length)]
  }

  const handleCreateGroup = () => {
    if (!newGroupName.trim()) {
      toast.error('يرجى إدخال اسم المجموعة')
      return
    }

    if (selectedMembers.length === 0) {
      toast.error('يرجى اختيار أعضاء المجموعة')
      return
    }

    try {
      const newGroup = {
        id: editingGroup ? editingGroup.id : `group_${Date.now()}`,
        name: newGroupName,
        members: selectedMembers,
        points: editingGroup ? editingGroup.points : 0,
        color: editingGroup ? editingGroup.color : getRandomColor(),
        createdAt: editingGroup ? editingGroup.createdAt : new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }

      let updatedGroups
      if (editingGroup) {
        updatedGroups = groups.map(group => 
          group.id === editingGroup.id ? newGroup : group
        )
      } else {
        updatedGroups = [...groups, newGroup]
      }

      dataStorage.saveGroups(workshopId, updatedGroups)
      setGroups(updatedGroups)
      
      toast.success(editingGroup ? 'تم تحديث المجموعة بنجاح' : 'تم إنشاء المجموعة بنجاح')
      handleCloseModal()
    } catch (error) {
      console.error('Error saving group:', error)
      toast.error('فشل في حفظ المجموعة')
    }
  }

  const handleEditGroup = (group) => {
    setEditingGroup(group)
    setNewGroupName(group.name)
    setSelectedMembers(group.members)
    setShowCreateModal(true)
  }

  const handleDeleteGroup = (groupId) => {
    if (confirm('هل أنت متأكد من حذف هذه المجموعة؟')) {
      try {
        const updatedGroups = groups.filter(group => group.id !== groupId)
        dataStorage.saveGroups(workshopId, updatedGroups)
        setGroups(updatedGroups)
        toast.success('تم حذف المجموعة بنجاح')
      } catch (error) {
        console.error('Error deleting group:', error)
        toast.error('فشل في حذف المجموعة')
      }
    }
  }

  const handleCloseModal = () => {
    setShowCreateModal(false)
    setEditingGroup(null)
    setNewGroupName('')
    setSelectedMembers([])
  }

  const getAvailableParticipants = () => {
    const assignedMemberIds = groups
      .filter(group => group.id !== editingGroup?.id)
      .flatMap(group => group.members.map(member => member.id))
    
    return participants.filter(participant => 
      !assignedMemberIds.includes(participant.id)
    )
  }

  const toggleMemberSelection = (participant) => {
    setSelectedMembers(prev => {
      const isSelected = prev.some(member => member.id === participant.id)
      if (isSelected) {
        return prev.filter(member => member.id !== participant.id)
      } else {
        return [...prev, participant]
      }
    })
  }

  const handleAddPoints = (groupId, points, reason) => {
    try {
      const updatedGroups = groups.map(group => {
        if (group.id === groupId) {
          return {
            ...group,
            points: (group.points || 0) + points,
            activities: [
              ...(group.activities || []),
              {
                id: Date.now(),
                points,
                reason,
                timestamp: new Date().toISOString()
              }
            ]
          }
        }
        return group
      })

      dataStorage.saveGroups(workshopId, updatedGroups)
      setGroups(updatedGroups)
      toast.success(`تم إضافة ${points} نقطة للمجموعة`)
    } catch (error) {
      console.error('Error adding points:', error)
      toast.error('فشل في إضافة النقاط')
    }
  }

  const handleShowWinner = () => {
    if (groups.length === 0) {
      toast.error('لا توجد مجموعات لإعلان الفائز')
      return
    }
    setShowWinnerModal(true)
  }

  const handleAdvancedGroupsCreated = (newGroups) => {
    try {
      // حفظ المجموعات الجديدة
      dataStorage.saveGroups(workshopId, newGroups)
      setGroups(newGroups)

      toast.success(`تم إنشاء ${newGroups.length} مجموعة بنجاح`)
      setShowAdvancedGroupModal(false)
    } catch (error) {
      console.error('Error saving advanced groups:', error)
      toast.error('فشل في حفظ المجموعات')
    }
  }

  // إنشاء كائن ورشة العمل للـ CreateGroupModal مع تحسين بيانات الجنس
  const workshopForModal = {
    id: workshopId,
    title: workshopTitle,
    selectedTrainees: participants.map(p => {
      // إذا كان المشارك كائن، استخدم بياناته
      if (typeof p === 'object' && p !== null) {
        return {
          id: p.id || Date.now() + Math.random(),
          name: p.name || 'مشارك غير محدد',
          gender: p.gender || 'غير محدد'
        }
      }

      // إذا كان المشارك نص، ابحث عن بياناته في المستخدمين أولاً
      const allUsers = JSON.parse(localStorage.getItem('users') || '{}')
      const foundUser = Object.values(allUsers).find(user =>
        user.name === p || user.email === p || user.id === p
      )

      if (foundUser) {
        return {
          id: foundUser.id,
          name: foundUser.name,
          gender: foundUser.gender || 'غير محدد'
        }
      }

      // إذا لم نجد في المستخدمين، ابحث في ورشة العمل
      const workshopData = JSON.parse(localStorage.getItem('app_workshops') || '[]')
        .concat(JSON.parse(localStorage.getItem('workshops_data') || '[]'))
        .find(w => w.id === workshopId)

      if (workshopData && workshopData.selectedTrainees) {
        const foundTrainee = workshopData.selectedTrainees.find(t =>
          (typeof t === 'object' && (t.name === p || t.id === p)) ||
          (typeof t === 'string' && t === p)
        )

        if (foundTrainee && typeof foundTrainee === 'object') {
          return {
            id: foundTrainee.id || Date.now() + Math.random(),
            name: foundTrainee.name || p,
            gender: foundTrainee.gender || 'غير محدد'
          }
        }
      }

      // إذا لم نجد بيانات، أنشئ كائن أساسي
      return {
        id: Date.now() + Math.random(),
        name: p || 'مشارك غير محدد',
        gender: 'غير محدد'
      }
    })
  }

  // إضافة تشخيص لبيانات الجنس
  console.log('🔍 تشخيص بيانات الجنس للمتدربين:', {
    workshopId,
    participantsCount: participants.length,
    participantsWithGender: workshopForModal.selectedTrainees.filter(t => t.gender !== 'غير محدد').length,
    genderBreakdown: {
      males: workshopForModal.selectedTrainees.filter(t => t.gender === 'ذكر').length,
      females: workshopForModal.selectedTrainees.filter(t => t.gender === 'أنثى').length,
      unknown: workshopForModal.selectedTrainees.filter(t => t.gender === 'غير محدد').length
    },
    traineesData: workshopForModal.selectedTrainees.map(t => ({ name: t.name, gender: t.gender }))
  })

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
            إدارة المجموعات
          </h3>
          <p className="text-sm text-gray-500 dark:text-gray-400">
            {groups.length} مجموعة • {participants.length} مشارك
          </p>
        </div>
        <div className="mt-4 sm:mt-0 flex flex-wrap gap-2">
          <Button
            variant="outline"
            onClick={() => generateRandomGroups(false)}
            icon={Shuffle}
            size="sm"
            loading={loading}
            disabled={participants.length === 0}
          >
            توليد مجموعات
          </Button>
          <Button
            variant="outline"
            onClick={() => generateRandomGroups(true)}
            icon={Users}
            size="sm"
            loading={loading}
            disabled={participants.length === 0}
            className="bg-purple-50 hover:bg-purple-100 text-purple-700 border-purple-200"
          >
            توحيد الجنس
          </Button>
          <Button
            variant="outline"
            onClick={handleShowWinner}
            icon={Trophy}
            size="sm"
            disabled={groups.length === 0}
          >
            إعلان الفائز
          </Button>
          <Button
            variant="secondary"
            onClick={() => setShowAdvancedGroupModal(true)}
            icon={Users}
            size="sm"
            className="bg-gradient-to-r from-blue-500 to-pink-500 text-white hover:from-blue-600 hover:to-pink-600"
            disabled={participants.length === 0}
          >
            🚹🚺 إنشاء متقدم
          </Button>
          <Button
            variant="primary"
            onClick={() => setShowCreateModal(true)}
            icon={Plus}
            size="sm"
          >
            إضافة مجموعة
          </Button>
        </div>
      </div>

      {/* Groups Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {groups.map((group) => (
          <GroupCard
            key={group.id}
            group={group}
            onEdit={() => handleEditGroup(group)}
            onDelete={() => handleDeleteGroup(group.id)}
            onAddPoints={(points, reason) => handleAddPoints(group.id, points, reason)}
          />
        ))}
      </div>

      {/* Empty State */}
      {groups.length === 0 && (
        <div className="text-center py-12">
          <Users className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-gray-100">
            لا توجد مجموعات
          </h3>
          <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
            {participants.length === 0
              ? 'لا يوجد متدربين مسجلين في هذه الورشة'
              : 'ابدأ بإنشاء مجموعات للمشاركين'
            }
          </p>
          {participants.length > 0 && (
            <div className="mt-6 flex flex-wrap justify-center gap-2">
              <Button
                variant="primary"
                onClick={() => generateRandomGroups(false)}
                icon={Shuffle}
                loading={loading}
                size="lg"
              >
                🎲 إنشاء مجموعات تلقائياً
              </Button>
              <Button
                variant="outline"
                onClick={() => generateRandomGroups(true)}
                icon={Users}
                loading={loading}
                size="lg"
                className="bg-purple-50 hover:bg-purple-100 text-purple-700 border-purple-200"
              >
                👥 توحيد الجنس
              </Button>
              <Button
                variant="secondary"
                onClick={() => setShowAdvancedGroupModal(true)}
                icon={Users}
                size="lg"
                className="bg-gradient-to-r from-blue-500 to-pink-500 text-white hover:from-blue-600 hover:to-pink-600"
              >
                🚹🚺 إنشاء متقدم مع توازن الجنس
              </Button>
              <Button
                variant="outline"
                onClick={() => setShowCreateModal(true)}
                icon={Plus}
                size="lg"
              >
                ➕ إضافة مجموعة يدوياً
              </Button>
            </div>
          )}
          {participants.length === 0 && (
            <div className="mt-6">
              <p className="text-sm text-gray-500 dark:text-gray-400 mb-4">
                يجب تسجيل متدربين في ورشة العمل أولاً لإنشاء المجموعات
              </p>
            </div>
          )}
        </div>
      )}

      {/* Create/Edit Group Modal */}
      <Modal
        isOpen={showCreateModal}
        onClose={handleCloseModal}
        title={editingGroup ? 'تعديل المجموعة' : 'إنشاء مجموعة جديدة'}
        size="md"
      >
        <div className="p-6 space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              اسم المجموعة
            </label>
            <input
              type="text"
              value={newGroupName}
              onChange={(e) => setNewGroupName(e.target.value)}
              className="input-field"
              placeholder="أدخل اسم المجموعة"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              أعضاء المجموعة
            </label>
            <div className="max-h-48 overflow-y-auto border border-gray-200 dark:border-gray-700 rounded-lg p-2">
              {getAvailableParticipants().map((participant) => (
                <div
                  key={participant.id}
                  className={`flex items-center p-2 rounded cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700 ${
                    selectedMembers.some(member => member.id === participant.id)
                      ? 'bg-primary-50 dark:bg-primary-900/20'
                      : ''
                  }`}
                  onClick={() => toggleMemberSelection(participant)}
                >
                  <input
                    type="checkbox"
                    checked={selectedMembers.some(member => member.id === participant.id)}
                    onChange={() => toggleMemberSelection(participant)}
                    className="mr-2"
                  />
                  <span className="text-sm text-gray-900 dark:text-gray-100">
                    {participant.name}
                  </span>
                </div>
              ))}
            </div>
          </div>

          <div className="flex gap-3 pt-4">
            <Button
              variant="outline"
              onClick={handleCloseModal}
              className="flex-1"
            >
              إلغاء
            </Button>
            <Button
              variant="primary"
              onClick={handleCreateGroup}
              className="flex-1"
            >
              {editingGroup ? 'تحديث' : 'إنشاء'}
            </Button>
          </div>
        </div>
      </Modal>

      {/* Winner Announcement Modal */}
      <WinnerAnnouncement
        isOpen={showWinnerModal}
        onClose={() => setShowWinnerModal(false)}
        groups={groups}
        workshopTitle={workshopTitle}
      />

      {/* Advanced Group Creation Modal */}
      <CreateGroupModal
        isOpen={showAdvancedGroupModal}
        onClose={() => setShowAdvancedGroupModal(false)}
        workshop={workshopForModal}
        onGroupsCreated={handleAdvancedGroupsCreated}
      />
    </div>
  )
}

// Group Card Component
const GroupCard = ({ group, onEdit, onDelete, onAddPoints }) => {
  const [showPointsModal, setShowPointsModal] = useState(false)

  return (
    <>
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className={`p-4 rounded-lg border-2 ${group.color || 'bg-gray-100 text-gray-800 border-gray-200'}`}
      >
        <div className="flex items-center justify-between mb-3">
          <h4 className="font-semibold">{group.name}</h4>
          <div className="flex gap-1">
            <button
              onClick={() => setShowPointsModal(true)}
              className="p-1 hover:bg-black/10 rounded"
              title="إضافة نقاط"
            >
              <Star className="w-4 h-4" />
            </button>
            <button
              onClick={onEdit}
              className="p-1 hover:bg-black/10 rounded"
            >
              <Edit className="w-4 h-4" />
            </button>
            <button
              onClick={onDelete}
              className="p-1 hover:bg-black/10 rounded"
            >
              <Trash2 className="w-4 h-4" />
            </button>
          </div>
        </div>

      <div className="space-y-2">
        <div className="flex items-center gap-2">
          <Users className="w-4 h-4" />
          <span className="text-sm">{group.members.length} أعضاء</span>
        </div>
        
        <div className="flex items-center gap-2">
          <Award className="w-4 h-4" />
          <span className="text-sm">{group.points || 0} نقطة</span>
        </div>

        <div className="mt-3">
          <div className="text-xs font-medium mb-1">الأعضاء:</div>
          <div className="flex flex-wrap gap-1">
            {group.members.map((member, index) => (
              <span
                key={member.id}
                className="text-xs px-2 py-1 bg-black/10 rounded"
              >
                {member.name}
              </span>
            ))}
          </div>
        </div>
      </div>
      </motion.div>

      {/* Quick Points Modal */}
      <QuickPointsManager
        isOpen={showPointsModal}
        onClose={() => setShowPointsModal(false)}
        groupName={group.name}
        onAddPoints={onAddPoints}
      />
    </>
  )
}

export default GroupManager
