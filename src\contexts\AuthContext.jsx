import React, { createContext, useContext, useEffect, useState } from 'react'
import { authService } from '../config/dataConfig'
import LoadingSpinner from '../components/ui/LoadingSpinner'
import { syncColorsWithUser, watchColorChanges } from '../utils/colorUpdater'

const AuthContext = createContext()

export const useAuth = () => {
  const context = useContext(AuthContext)
  if (!context) {
    // في حالة عدم وجود context، إرجاع قيم افتراضية
    console.warn('useAuth called outside AuthProvider, using defaults')
    return {
      user: null,
      login: () => Promise.resolve({ success: false }),
      logout: () => {},
      register: () => Promise.resolve({ success: false }),
      loading: false,
      isAuthenticated: false
    }
  }
  return context
}

export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null)
  const [loading, setLoading] = useState(true)
  const [isAuthenticated, setIsAuthenticated] = useState(false)

  useEffect(() => {
    checkAuthStatus()
  }, [])

  const checkAuthStatus = async () => {
    try {
      // Check for both token types (API uses authToken, local uses token)
      const token = localStorage.getItem('authToken') || localStorage.getItem('token')
      const currentUser = localStorage.getItem('currentUser')
      
      if (token && currentUser) {
        try {
          const userData = JSON.parse(currentUser)
          setUser(userData)
          setIsAuthenticated(true)

          // تحديث ألوان النظام
          syncColorsWithUser(userData)

          // إشعار الثيم بتغيير المستخدم لإعادة تحميل الإعدادات
          window.dispatchEvent(new CustomEvent('userChanged', { detail: userData }))
        } catch (parseError) {
          console.error('Error parsing user data:', parseError)
          // Clear invalid data
          localStorage.removeItem('authToken')
          localStorage.removeItem('token')
          localStorage.removeItem('currentUser')
        }
      } else if (authService.isAuthenticated && authService.isAuthenticated()) {
        // Fallback to service method
        const userData = authService.getCurrentUser()
        if (userData) {
          setUser(userData)
          setIsAuthenticated(true)
          syncColorsWithUser(userData)
          window.dispatchEvent(new CustomEvent('userChanged', { detail: userData }))
        }
      }
    } catch (error) {
      console.error('Auth check failed:', error)
      // Clear all auth data on error
      localStorage.removeItem('authToken')
      localStorage.removeItem('token')
      localStorage.removeItem('currentUser')
    } finally {
      setLoading(false)
    }
  }

  const login = async (emailOrUsername, password) => {
    try {
      console.log('🔐 AuthContext: بدء تسجيل الدخول...', emailOrUsername)
      const response = await authService.login(emailOrUsername, password)
      console.log('📨 AuthContext: استجابة authService:', response)

      // Handle different response formats
      if (response && response.success) {
        const userData = response.user
        
        // Store user data (API service already stores authToken)
        setUser(userData)
        setIsAuthenticated(true)

        // تحديث ألوان النظام
        syncColorsWithUser(userData)

        // إشعار الثيم بتغيير المستخدم لإعادة تحميل الإعدادات
        window.dispatchEvent(new CustomEvent('userChanged', { detail: userData }))

        console.log('✅ AuthContext: تسجيل الدخول نجح')
        return { success: true, user: userData }
      } else if (response && response.token && response.user) {
        // Handle local service format
        const { token, user: userData } = response
        localStorage.setItem('token', token)
        setUser(userData)
        setIsAuthenticated(true)

        syncColorsWithUser(userData)
        window.dispatchEvent(new CustomEvent('userChanged', { detail: userData }))

        return { success: true, user: userData }
      } else {
        console.error('❌ AuthContext: استجابة غير متوقعة:', response)
        return { success: false, error: response?.error || 'فشل في تسجيل الدخول' }
      }
    } catch (error) {
      console.error('💥 AuthContext: خطأ في تسجيل الدخول:', error)
      return { success: false, error: error.message || 'خطأ في الاتصال' }
    }
  }

  const register = async (userData) => {
    try {
      const response = await authService.register(userData)
      const { token, user: newUser } = response

      localStorage.setItem('token', token)
      setUser(newUser)
      setIsAuthenticated(true)

      return { success: true, user: newUser }
    } catch (error) {
      return { success: false, error: error.message }
    }
  }

  const logout = async () => {
    try {
      await authService.logout()
    } catch (error) {
      console.error('Logout error:', error)
    } finally {
      localStorage.removeItem('token')
      setUser(null)
      setIsAuthenticated(false)
    }
  }

  const updateUser = (userData) => {
    try {
      console.log('🔄 تحديث بيانات المستخدم في AuthContext:', {
        userId: userData.id,
        email: userData.email,
        hasProfileImage: !!userData.profileImage
      })

      setUser(userData)

      // تحديث localStorage أيضاً
      const users = JSON.parse(localStorage.getItem('app_users') || '[]')
      const userIndex = users.findIndex(u =>
        (userData.id && u.id === userData.id) ||
        (userData.email && u.email === userData.email)
      )

      if (userIndex !== -1) {
        users[userIndex] = userData
        localStorage.setItem('app_users', JSON.stringify(users))
        console.log('✅ تم تحديث المستخدم في localStorage من AuthContext')
      }
    } catch (error) {
      console.error('❌ خطأ في تحديث المستخدم:', error)
    }
  }

  const hasRole = (role) => {
    return user?.role === role
  }

  const hasAnyRole = (roles) => {
    return roles.includes(user?.role)
  }

  const value = {
    user,
    loading,
    isAuthenticated,
    login,
    register,
    logout,
    updateUser,
    hasRole,
    hasAnyRole,
  }

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900">
        <LoadingSpinner size="lg" />
      </div>
    )
  }

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  )
}
