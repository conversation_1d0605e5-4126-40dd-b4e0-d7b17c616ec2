import React, { useState, useEffect } from 'react'
import { useNavigate } from 'react-router-dom'
import { motion } from 'framer-motion'
import {
  Calendar,
  Clock,
  Users,
  MapPin,
  BookOpen,
  CheckCircle,
  AlertCircle,
  Play,
  Eye,
  Presentation,
  Lightbulb,
  Target,
  Award,
  Star,
  Zap,
  Briefcase,
  GraduationCap,
  Cpu,
  Palette,
  Code,
  Database,
  Globe,
  Smartphone,
  Settings,
  Wrench,
  Timer,
  Filter,
  Search
} from 'lucide-react'
import { useAuth } from '../../contexts/AuthContext'
import { useLanguage } from '../../contexts/LanguageContext'
import TraineeHeader from '../../components/trainee/TraineeHeader'
import StatsCard from '../../components/trainee/StatsCard'
import SearchAndFilter from '../../components/trainee/SearchAndFilter'
import Button from '../../components/ui/Button'
import toast from 'react-hot-toast'

const WorkshopPage = () => {
  const { user } = useAuth()
  const { t } = useLanguage()
  const navigate = useNavigate()
  const [workshops, setWorkshops] = useState([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedTab, setSelectedTab] = useState('all')
  const [stats, setStats] = useState({
    totalWorkshops: 0,
    upcomingWorkshops: 0,
    ongoingWorkshops: 0,
    completedWorkshops: 0
  })

  useEffect(() => {
    loadWorkshops()
  }, [])

  const loadWorkshops = () => {
    try {
      // تحميل ورش العمل من جميع المصادر
      let workshopsData = JSON.parse(localStorage.getItem('workshops_data') || '[]')
      let appWorkshops = JSON.parse(localStorage.getItem('app_workshops') || '[]')

      console.log('📊 تحميل ورش العمل الحقيقية...')

      // دمج البيانات من جميع المصادر
      const allWorkshops = [...workshopsData, ...appWorkshops]
      
      // إزالة التكرار بناءً على الـ ID
      const uniqueWorkshops = allWorkshops.filter((workshop, index, self) =>
        index === self.findIndex(w => w.id === workshop.id)
      )

      // تصفية ورش العمل للمتدرب الحالي
      const userWorkshops = uniqueWorkshops.filter(workshop => {
        // التحقق من المشاركة بالاسم
        if (workshop.participants && workshop.participants.includes(user?.name)) {
          return true
        }

        // التحقق من التعيين المباشر بالـ ID
        if (workshop.selectedTrainees && workshop.selectedTrainees.some(trainee =>
          (typeof trainee === 'object' && (trainee.id === user?.id || trainee.name === user?.name)) ||
          trainee === user?.name
        )) {
          return true
        }

        // التحقق من ورش العمل العامة (بدون تعيين محدد)
        if (!workshop.participants && !workshop.selectedTrainees) {
          return true
        }

        return false
      })

      // حساب الإحصائيات
      const now = new Date()
      const totalWorkshops = userWorkshops.length
      const upcomingWorkshops = userWorkshops.filter(w => {
        const startDate = new Date(w.startDate)
        return startDate > now && w.status !== 'completed'
      }).length
      const ongoingWorkshops = userWorkshops.filter(w => {
        const startDate = new Date(w.startDate)
        const endDate = w.endDate ? new Date(w.endDate) : null
        return startDate <= now && (!endDate || endDate >= now) && w.status !== 'completed'
      }).length
      const completedWorkshops = userWorkshops.filter(w => w.status === 'completed').length

      setStats({
        totalWorkshops,
        upcomingWorkshops,
        ongoingWorkshops,
        completedWorkshops
      })

      console.log('📊 تم تحميل ورش العمل:', userWorkshops.length)
      setWorkshops(userWorkshops)
      setLoading(false)
      
    } catch (error) {
      console.error('❌ خطأ في تحميل ورش العمل:', error)
      setWorkshops([])
      setLoading(false)
    }
  }

  // دالة حساب التقدم
  const calculateProgress = (workshop) => {
    if (!workshop.startDate) return 0
    
    const start = new Date(workshop.startDate)
    const end = workshop.endDate ? new Date(workshop.endDate) : null
    const now = new Date()
    
    if (workshop.status === 'completed') {
      return 100
    } else if (workshop.status === 'scheduled' && now < start) {
      return 0
    } else if (end) {
      const total = end.getTime() - start.getTime()
      const elapsed = now.getTime() - start.getTime()
      return Math.max(0, Math.min(100, Math.round((elapsed / total) * 100)))
    }
    
    return 0
  }

  const getStatusColor = (status) => {
    switch (status) {
      case 'scheduled':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200'
      case 'active':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
      case 'completed':
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200'
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200'
    }
  }

  const getStatusIcon = (status) => {
    switch (status) {
      case 'scheduled':
        return <Clock className="w-4 h-4" />
      case 'active':
        return <Play className="w-4 h-4" />
      case 'completed':
        return <CheckCircle className="w-4 h-4" />
      default:
        return <AlertCircle className="w-4 h-4" />
    }
  }

  const getStatusText = (status) => {
    switch (status) {
      case 'scheduled':
        return 'مجدولة'
      case 'active':
        return 'نشطة'
      case 'completed':
        return 'مكتملة'
      default:
        return 'غير محدد'
    }
  }

  const getWorkshopIcon = (title, category) => {
    const titleLower = (title || '').toLowerCase()
    const categoryLower = (category || '').toLowerCase()

    // أيقونات حسب المحتوى
    if (titleLower.includes('برمجة') || titleLower.includes('كود') || categoryLower.includes('programming')) {
      return <Code className="w-5 h-5" />
    }
    if (titleLower.includes('تصميم') || titleLower.includes('جرافيك') || categoryLower.includes('design')) {
      return <Palette className="w-5 h-5" />
    }
    if (titleLower.includes('بيانات') || titleLower.includes('قاعدة') || categoryLower.includes('database')) {
      return <Database className="w-5 h-5" />
    }
    if (titleLower.includes('ويب') || titleLower.includes('موقع') || categoryLower.includes('web')) {
      return <Globe className="w-5 h-5" />
    }
    if (titleLower.includes('جوال') || titleLower.includes('تطبيق') || categoryLower.includes('mobile')) {
      return <Smartphone className="w-5 h-5" />
    }
    if (titleLower.includes('ذكي') || titleLower.includes('ai') || categoryLower.includes('artificial')) {
      return <Cpu className="w-5 h-5" />
    }
    if (titleLower.includes('إدارة') || titleLower.includes('أعمال') || categoryLower.includes('business')) {
      return <Briefcase className="w-5 h-5" />
    }
    if (titleLower.includes('تدريب') || titleLower.includes('تعليم') || categoryLower.includes('training')) {
      return <GraduationCap className="w-5 h-5" />
    }
    if (titleLower.includes('إبداع') || titleLower.includes('ابتكار') || categoryLower.includes('creative')) {
      return <Lightbulb className="w-5 h-5" />
    }
    if (titleLower.includes('هدف') || titleLower.includes('استراتيجية') || categoryLower.includes('strategy')) {
      return <Target className="w-5 h-5" />
    }
    if (titleLower.includes('تقديم') || titleLower.includes('عرض') || categoryLower.includes('presentation')) {
      return <Presentation className="w-5 h-5" />
    }

    // أيقونة افتراضية للورش
    return <Zap className="w-5 h-5" />
  }

  // دالة الفلترة والبحث
  const getFilteredWorkshops = () => {
    let filtered = workshops

    // فلترة حسب التبويب المحدد
    const now = new Date()
    if (selectedTab === 'upcoming') {
      filtered = filtered.filter(w => {
        const startDate = new Date(w.startDate)
        return startDate > now && w.status !== 'completed'
      })
    } else if (selectedTab === 'ongoing') {
      filtered = filtered.filter(w => {
        const startDate = new Date(w.startDate)
        const endDate = w.endDate ? new Date(w.endDate) : null
        return startDate <= now && (!endDate || endDate >= now) && w.status !== 'completed'
      })
    } else if (selectedTab === 'completed') {
      filtered = filtered.filter(w => w.status === 'completed')
    }

    // فلترة حسب البحث
    if (searchTerm) {
      filtered = filtered.filter(workshop =>
        workshop.title?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        workshop.description?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        workshop.trainer?.toLowerCase().includes(searchTerm.toLowerCase())
      )
    }

    return filtered
  }

  const filteredWorkshops = getFilteredWorkshops()

  // تبويبات الفلترة
  const tabs = [
    { id: 'all', label: 'الكل', count: stats.totalWorkshops },
    { id: 'upcoming', label: 'قادمة', count: stats.upcomingWorkshops },
    { id: 'ongoing', label: 'جارية', count: stats.ongoingWorkshops },
    { id: 'completed', label: 'مكتملة', count: stats.completedWorkshops }
  ]

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 py-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <TraineeHeader
          title={t('workshops.title')}
          subtitle={t('workshops.subtitle')}
          icon={Zap}
          iconBgColor="bg-purple-100 dark:bg-purple-900/30"
          iconColor="text-purple-600 dark:text-purple-400"
        />

        {/* Stats Cards */}
        <div className="grid grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
          <StatsCard
            icon={Zap}
            value={stats.totalWorkshops}
            label={t('workshops.totalWorkshops')}
            subtitle={t('workshops.availableWorkshop')}
            iconBgColor="bg-purple-100 dark:bg-purple-900/30"
            iconColor="text-purple-600 dark:text-purple-400"
            onClick={() => setSelectedTab('all')}
            delay={0.1}
          />
          <StatsCard
            icon={Clock}
            value={stats.upcomingWorkshops}
            label={t('workshops.upcomingWorkshops')}
            subtitle={t('workshops.upcomingWorkshop')}
            iconBgColor="bg-blue-100 dark:bg-blue-900/30"
            iconColor="text-blue-600 dark:text-blue-400"
            onClick={() => setSelectedTab('upcoming')}
            delay={0.2}
          />
          <StatsCard
            icon={Play}
            value={stats.ongoingWorkshops}
            label={t('workshops.ongoingWorkshops')}
            subtitle={t('workshops.ongoingWorkshop')}
            iconBgColor="bg-green-100 dark:bg-green-900/30"
            iconColor="text-green-600 dark:text-green-400"
            onClick={() => setSelectedTab('ongoing')}
            delay={0.3}
          />
          <StatsCard
            icon={CheckCircle}
            value={stats.completedWorkshops}
            label={t('workshops.completedWorkshops')}
            subtitle={t('workshops.completedWorkshop')}
            iconBgColor="bg-gray-100 dark:bg-gray-900/30"
            iconColor="text-gray-600 dark:text-gray-400"
            onClick={() => setSelectedTab('completed')}
            delay={0.4}
          />
        </div>

        {/* Search and Filter */}
        <SearchAndFilter
          searchTerm={searchTerm}
          setSearchTerm={setSearchTerm}
          selectedTab={selectedTab}
          setSelectedTab={setSelectedTab}
          tabs={tabs}
          searchPlaceholder={t('workshops.searchWorkshops')}
        />

        {/* Workshops Grid */}
        {filteredWorkshops.length === 0 ? (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="text-center py-16"
          >
            <div className="bg-white dark:bg-gray-800 rounded-xl p-8 shadow-sm border border-gray-200 dark:border-gray-700 max-w-md mx-auto">
              <Zap className="mx-auto h-16 w-16 text-gray-400 mb-4" />
              <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2">
                {t('workshops.noWorkshops')}
              </h3>
              <p className="text-gray-500 dark:text-gray-400 mb-6">
                {searchTerm
                  ? `لم يتم العثور على ورش عمل تحتوي على "${searchTerm}"`
                  : selectedTab === 'all'
                    ? 'لا توجد ورش عمل متاحة حالياً'
                    : `لا توجد ورش عمل ${tabs.find(t => t.id === selectedTab)?.label || ''}`
                }
              </p>
              {searchTerm && (
                <Button
                  onClick={() => setSearchTerm('')}
                  variant="outline"
                  size="sm"
                >
                  مسح البحث
                </Button>
              )}
            </div>
          </motion.div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredWorkshops.map((workshop, index) => (
              <motion.div
                key={workshop.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
                className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 hover:shadow-md transition-all duration-300 overflow-hidden"
              >
                <div className="p-6">
                  {/* Workshop Header */}
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center space-x-3 rtl:space-x-reverse">
                      <div className="p-2 bg-purple-100 dark:bg-purple-900/30 rounded-lg">
                        <div className="text-purple-600 dark:text-purple-400">
                          {getWorkshopIcon(workshop.title, workshop.category)}
                        </div>
                      </div>
                    </div>
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(workshop.status)}`}>
                      {getStatusIcon(workshop.status)}
                      <span className="mr-1">{getStatusText(workshop.status)}</span>
                    </span>
                  </div>

                  {/* Workshop Title */}
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                    {workshop.title}
                  </h3>

                  {/* Workshop Description */}
                  <p className="text-gray-600 dark:text-gray-400 text-sm mb-4 line-clamp-2">
                    {workshop.description}
                  </p>

                  {/* Workshop Details */}
                  <div className="space-y-3 mb-6">
                    <div className="flex items-center text-sm text-gray-600 dark:text-gray-400">
                      <Calendar className="w-4 h-4 ml-2 text-gray-400" />
                      <span>
                        {workshop.startDate && new Date(workshop.startDate).toLocaleDateString('ar-SA')}
                      </span>
                    </div>
                    <div className="flex items-center text-sm text-gray-600 dark:text-gray-400">
                      <Clock className="w-4 h-4 ml-2 text-gray-400" />
                      <span>{workshop.startTime} - {workshop.endTime}</span>
                    </div>
                    {workshop.trainer && (
                      <div className="flex items-center text-sm text-gray-600 dark:text-gray-400">
                        <Users className="w-4 h-4 ml-2 text-gray-400" />
                        <span>{workshop.trainer}</span>
                      </div>
                    )}
                  </div>

                  {/* Progress Bar */}
                  {workshop.status === 'active' && (
                    <div className="mb-6">
                      <div className="flex justify-between text-sm text-gray-600 dark:text-gray-400 mb-2">
                        <span>التقدم</span>
                        <span>{calculateProgress(workshop)}%</span>
                      </div>
                      <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                        <div
                          className="bg-purple-600 h-2 rounded-full transition-all duration-300"
                          style={{ width: `${calculateProgress(workshop)}%` }}
                        ></div>
                      </div>
                    </div>
                  )}

                  {/* Action Button */}
                  <div className="pt-4 border-t border-gray-100 dark:border-gray-700">
                    <Button
                      onClick={() => {
                        console.log('🔗 الانتقال إلى تفاصيل ورشة العمل:', workshop.id)
                        navigate(`/trainee/workshop/${workshop.id}`)
                      }}
                      variant="outline"
                      size="sm"
                      className="w-full"
                      icon={Eye}
                    >
                      عرض التفاصيل
                    </Button>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        )}
      </div>
    </div>
  )
}

export default WorkshopPage
