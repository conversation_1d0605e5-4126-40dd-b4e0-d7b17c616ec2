import React, { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import toast from 'react-hot-toast'
import {
  Plus,
  Search,
  Filter,
  Download,
  Upload,
  Edit,
  Trash2,
  Eye,
  MoreVertical,
  Users,
  UserCheck,
  UserX,
  Mail,
  Phone,
  Settings,
  Building
} from 'lucide-react'
import Button from '../../components/ui/Button'
import CreateUserModal from '../../components/modals/CreateUserModal'
import EditUserModal from '../../components/modals/EditUserModal'
import ViewUserModal from '../../components/modals/ViewUserModal'
import { useLanguage } from '../../contexts/LanguageContext'
import { useAuth } from '../../contexts/AuthContext'
import { authService } from '../../config/dataConfig'
import NumberDisplay from '../../components/ui/NumberDisplay'
import { exportUsersToExcel } from '../../utils/excelExporter'
import GenderAvatar from '../../components/ui/GenderAvatar'

const UserManagement = () => {
  const { t, language } = useLanguage()
  const { user } = useAuth()
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedRole, setSelectedRole] = useState('all')
  const [showAddModal, setShowAddModal] = useState(false)
  const [showEditModal, setShowEditModal] = useState(false)
  const [showViewModal, setShowViewModal] = useState(false)
  const [selectedUser, setSelectedUser] = useState(null)
  const [selectedUsers, setSelectedUsers] = useState([])
  const [isExporting, setIsExporting] = useState(false)

  // إدارة الأقسام
  const [showDepartmentSettings, setShowDepartmentSettings] = useState(false)
  const [departments, setDepartments] = useState([])
  const [newDepartment, setNewDepartment] = useState('')

  // قائمة المستخدمين (ستجلب من الخادم)
  const [users, setUsers] = useState([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)

  // تحميل الأقسام عند تحميل الصفحة
  useEffect(() => {
    const loadDepartments = () => {
      try {
        const departmentsData = JSON.parse(localStorage.getItem('departments_data') || '[]')
        setDepartments(departmentsData)
      } catch (error) {
        console.error('خطأ في تحميل الأقسام:', error)
        setDepartments([])
      }
    }
    loadDepartments()
  }, [])

  // دالة تحميل المستخدمين
  const loadUsers = async () => {
    try {
      setLoading(true)
      setError(null)
      const allUsers = await authService.getAllUsers()
      console.log('🔄 Loaded users:', allUsers?.length || 0)
      console.log('👥 Users list:', allUsers?.map(u => ({ name: u.name, email: u.email })))

      // Ensure we have an array
      if (Array.isArray(allUsers)) {
        setUsers(allUsers)
      } else {
        console.warn('Users data is not an array:', allUsers)
        setUsers([])
      }
    } catch (error) {
      console.error('Error loading users:', error)
      setError(t('users.loadFailed'))
      setUsers([])
    } finally {
      setLoading(false)
    }
  }

  // دالة لتنظيف localStorage (للاختبار)
  const clearUserData = () => {
    if (window.confirm('هل أنت متأكد من حذف جميع بيانات المستخدمين؟ هذا الإجراء لا يمكن التراجع عنه.')) {
      localStorage.removeItem('standalone_users')
      localStorage.removeItem('app_users')
      localStorage.removeItem('users_data')
      toast.success('تم حذف جميع بيانات المستخدمين')
      loadUsers()
    }
  }

  // دالة لعرض localStorage للتشخيص
  const debugLocalStorage = () => {
    console.log('🔍 localStorage Debug:')
    console.log('standalone_users:', localStorage.getItem('standalone_users'))
    console.log('app_users:', localStorage.getItem('app_users'))
    console.log('users_data:', localStorage.getItem('users_data'))

    const standaloneUsers = JSON.parse(localStorage.getItem('standalone_users') || '{}')
    console.log('📋 Standalone users count:', Object.keys(standaloneUsers).length)
    console.log('📋 Standalone users:', Object.keys(standaloneUsers))
  }

  // تحميل المستخدمين عند تحميل الصفحة
  useEffect(() => {
    loadUsers()
  }, [])

  const filteredUsers = React.useMemo(() => {
    if (!Array.isArray(users)) {
      console.warn('Users is not an array:', users)
      return []
    }
    
    return users.filter(user => {
      if (!user) return false
      const matchesSearch = user.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           user.email?.toLowerCase().includes(searchTerm.toLowerCase())
      const matchesRole = selectedRole === 'all' || user.role === selectedRole
      return matchesSearch && matchesRole
    })
  }, [users, searchTerm, selectedRole])

  const getRoleColor = (role) => {
    switch (role) {
      case 'admin': return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
      case 'trainer': return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200'
      case 'course_manager': return 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200'
      case 'trainee': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200'
    }
  }

  const getStatusColor = (status) => {
    switch (status) {
      case 'active': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
      case 'inactive': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'
      case 'suspended': return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200'
    }
  }

  const getRoleDisplayName = (role) => {
    const roleNames = {
      'admin': t('users.admin'),
      'trainer': t('users.trainer'),
      'course_manager': t('users.courseManager'),
      'trainee': t('users.trainee')
    }
    return roleNames[role] || role
  }

  const getStatusDisplayName = (status) => {
    const statusNames = {
      'active': t('users.active'),
      'inactive': t('users.inactive'),
      'suspended': t('users.suspended')
    }
    return statusNames[status] || status
  }

  // دوال إدارة الأقسام
  const handleAddDepartment = () => {
    if (!newDepartment.trim()) {
      toast.error(t('users.enterDepartmentName'))
      return
    }

    if (departments.find(dept => dept.name === newDepartment.trim())) {
      toast.error(t('users.departmentExists'))
      return
    }

    const newDept = {
      id: Date.now().toString(),
      name: newDepartment.trim(),
      createdAt: new Date().toISOString()
    }

    const updatedDepartments = [...departments, newDept]
    setDepartments(updatedDepartments)
    localStorage.setItem('departments_data', JSON.stringify(updatedDepartments))
    setNewDepartment('')
    toast.success(t('users.departmentAdded'))
  }

  const handleDeleteDepartment = (deptId) => {
    const confirmMessage = language === 'en'
      ? 'Are you sure you want to delete this department?'
      : t('users.confirmDeleteDepartment')

    if (window.confirm(confirmMessage)) {
      const updatedDepartments = departments.filter(dept => dept.id !== deptId)
      setDepartments(updatedDepartments)
      localStorage.setItem('departments_data', JSON.stringify(updatedDepartments))
      toast.success(t('users.departmentDeleted'))
    }
  }



  const handleEditUser = (userId) => {
    const user = users.find(u => u.id === userId)
    if (user) {
      setSelectedUser(user)
      setShowEditModal(true)
    }
  }

  const handleUpdateUser = async (userId, userData) => {
    try {
      // تحديث المستخدم في authService
      await authService.updateUser(userId, userData)

      // إعادة تحميل قائمة المستخدمين
      const allUsers = await authService.getAllUsers()
      setUsers(allUsers || [])

      toast.success(t('toast.userUpdated'))
    } catch (error) {
      toast.error(error.message || t('toast.userUpdateFailed'))
    }
  }

  const handleDeleteUser = async (userId) => {
    const confirmMessage = language === 'en'
      ? 'Are you sure you want to delete this user?'
      : t('users.confirmDeleteUser')

    if (window.confirm(confirmMessage)) {
      try {
        // حذف المستخدم من authService
        await authService.deleteUser(userId)

        // إعادة تحميل قائمة المستخدمين
        const allUsers = await authService.getAllUsers()
        setUsers(allUsers || [])

        toast.success(t('toast.userDeleted'))
      } catch (error) {
        toast.error(error.message || t('toast.userDeleteFailed'))
      }
    }
  }

  const handleCreateUser = async (userData) => {
    try {
      console.log('📝 UserManagement: Creating user with data:', userData)

      // تشخيص localStorage قبل الإنشاء
      debugLocalStorage()

      // إنشاء المستخدم باستخدام authService (سيتم الفحص هناك)
      const result = await authService.createUser(userData)
      console.log('📊 نتيجة إنشاء المستخدم:', result)

      if (result.success) {
        console.log('✅ User created successfully:', result.user)
        toast.success(t('users.createSuccess') || 'تم إنشاء المستخدم بنجاح')

        // تشخيص localStorage بعد الإنشاء
        debugLocalStorage()

        // إعادة تحميل قائمة المستخدمين
        console.log('🔄 إعادة تحميل قائمة المستخدمين...')
        await loadUsers()
        console.log('📋 تم تحميل المستخدمين، العدد الحالي:', users.length)

        setShowAddModal(false)
      } else {
        console.error('❌ فشل في إنشاء المستخدم:', result)
        toast.error(result.error || result.message || 'فشل في إنشاء المستخدم')
      }
    } catch (error) {
      console.error('❌ Error creating user:', error)
      toast.error(error.message || t('users.createFailed') || 'فشل في إنشاء المستخدم')
    }
  }



  const handleViewUser = async (userId) => {
    try {
      // البحث عن المستخدم في القائمة المحلية أولاً
      let userInList = users.find(u => u.id === userId)
      
      if (userInList) {
        // محاولة جلب كلمة المرور من localStorage إذا لم تكن موجودة
        if (!userInList.password) {
          try {
            const allUsers = JSON.parse(localStorage.getItem('app_users') || '[]')
            const userWithPassword = allUsers.find(u => u.id === userId || u.email === userInList.email)
            if (userWithPassword && userWithPassword.password) {
              userInList = { ...userInList, password: userWithPassword.password }
            }
          } catch (error) {
            console.log('Could not retrieve password from localStorage:', error)
          }
        }
        
        setSelectedUser(userInList)
        setShowViewModal(true)
        return
      }

      // إذا لم يوجد، جلب تفاصيل المستخدم من الخدمة
      const userDetails = await authService.getUserDetails(userId)
      if (userDetails) {
        setSelectedUser(userDetails)
        setShowViewModal(true)
      } else {
        toast.error(t('users.viewUserFailed') || 'فشل في تحميل تفاصيل المستخدم')
      }
    } catch (error) {
      console.error('Error viewing user:', error)
      toast.error(t('users.viewUserFailed') || 'فشل في تحميل تفاصيل المستخدم')
    }
  }

  const handleExportUsers = async () => {
    setIsExporting(true)
    try {
      console.log('🎨 تحديث ألوان النظام قبل تصدير المستخدمين...')
      
      // فرض تحديث الألوان من المصادر المختلفة
      const { syncColorsWithUser } = await import('../../utils/colorUpdater')
      const updatedColors = syncColorsWithUser(user)
      console.log('✅ تم تحديث الألوان:', updatedColors)

      // انتظار قصير للتأكد من تطبيق الألوان
      await new Promise(resolve => setTimeout(resolve, 500))

      // تصدير المستخدمين إلى Excel مع الألوان
      await exportUsersToExcel(filteredUsers, language, user)

      const successMessage = language === 'en'
        ? 'Users exported successfully!'
        : t('users.exportSuccess')

      toast.success(successMessage)
    } catch (error) {
      const errorMessage = language === 'en'
        ? 'Failed to export users'
        : t('users.exportFailed')

      toast.error(errorMessage)
      console.error('Export error:', error)
    } finally {
      setIsExporting(false)
    }
  }

  const handleImportUsers = () => {
    const input = document.createElement('input')
    input.type = 'file'
    input.accept = '.xlsx,.xls'
    input.onchange = async (e) => {
      const file = e.target.files[0]
      if (!file) return

      try {
        toast.loading(t('users.importing') || 'جاري معالجة الملف...')

        // استيراد مكتبة xlsx
        const XLSX = await import('xlsx')

        const reader = new FileReader()
        reader.onload = async (event) => {
          try {
            const data = new Uint8Array(event.target.result)
            const workbook = XLSX.read(data, { type: 'array' })
            const sheetName = workbook.SheetNames[0]
            const worksheet = workbook.Sheets[sheetName]
            const jsonData = XLSX.utils.sheet_to_json(worksheet)

            console.log('📊 بيانات Excel المستوردة:', jsonData)

            let successCount = 0
            let errorCount = 0

            for (const row of jsonData) {
              try {
                const userData = {
                  name: row['الاسم'] || row['Name'] || '',
                  email: row['البريد الإلكتروني'] || row['Email'] || `user_${Date.now()}${Math.random().toString(36).substr(2, 5)}@platform.com`,
                  phone: row['الهاتف'] || row['Phone'] || '',
                  role: row['الدور'] || row['Role'] || 'trainee',
                  department: row['القسم'] || row['Department'] || '',
                  gender: row['الجنس'] || row['Gender'] || '',
                  password: row['كلمة المرور'] || row['Password'] || '123456'
                }

                console.log('📝 معالجة صف:', row)
                console.log('👤 بيانات المستخدم المستخرجة:', userData)

                if (userData.name && userData.name.trim()) {
                  console.log('✅ إنشاء مستخدم:', userData.name)
                  const result = await authService.createUser(userData)
                  console.log('📊 نتيجة الإنشاء:', result)

                  if (result.success) {
                    successCount++
                    console.log('✅ تم إنشاء المستخدم بنجاح:', userData.name)
                  } else {
                    console.error('❌ فشل في إنشاء المستخدم:', userData.name, result.error || result.message)
                    errorCount++
                  }
                } else {
                  console.warn('⚠️ اسم المستخدم فارغ، تخطي الصف')
                  errorCount++
                }
              } catch (error) {
                console.error('❌ خطأ في إضافة مستخدم:', error)
                errorCount++
              }
            }

            // إعادة تحميل المستخدمين
            await loadUsers()

            toast.success(t('users.importSuccess', { successCount, errorCount }))
          } catch (error) {
            console.error('خطأ في معالجة ملف Excel:', error)
            toast.error(t('users.importProcessFailed'))
          }
        }
        reader.readAsArrayBuffer(file)
      } catch (error) {
        console.error('خطأ في استيراد المستخدمين:', error)
        toast.error(t('users.importFailed'))
      }
    }
    input.click()
  }

  // تصدير قالب Excel للمستخدمين
  const handleExportTemplate = async () => {
    try {
      const XLSX = await import('xlsx')

      // إنشاء بيانات القالب
      const templateData = [
        ['الاسم', 'البريد الإلكتروني', 'الهاتف', 'الدور', 'القسم', 'الجنس', 'كلمة المرور'],
        ['Name', 'Email', 'Phone', 'Role', 'Department', 'Gender', 'Password'],
        ['الاسم الأول الثاني', '<EMAIL>', '05xxxxxxxx', 'trainee', 'القسم', 'ذكر', '123456'],
        ['الاسم الأول الثاني', '<EMAIL>', '05xxxxxxxx', 'trainee', 'القسم', 'أنثى', '123456'],
        ['الاسم الأول الثاني', '<EMAIL>', '05xxxxxxxx', 'trainer', 'القسم', 'ذكر', '123456'],
        ['الاسم الأول الثاني', '<EMAIL>', '05xxxxxxxx', 'admin', 'القسم', 'أنثى', '123456']
      ]

      // إنشاء workbook
      const wb = XLSX.utils.book_new()
      const ws = XLSX.utils.aoa_to_sheet(templateData)

      // تنسيق الأعمدة
      ws['!cols'] = [
        { width: 25 }, // الاسم
        { width: 30 }, // البريد
        { width: 15 }, // الهاتف
        { width: 15 }, // الدور
        { width: 20 }, // القسم
        { width: 10 }, // الجنس
        { width: 15 }  // كلمة المرور
      ]

      XLSX.utils.book_append_sheet(wb, ws, 'قالب المستخدمين')

      // حفظ الملف
      const fileName = `قالب_المستخدمين_${new Date().toISOString().split('T')[0]}.xlsx`
      XLSX.writeFile(wb, fileName)

      toast.success('تم تصدير قالب Excel بنجاح!')
    } catch (error) {
      console.error('خطأ في تصدير القالب:', error)
      toast.error('فشل في تصدير القالب')
    }
  }

  const stats = [
    { name: t('users.totalUsers'), value: Array.isArray(users) ? users.length : 0, icon: Users, color: 'bg-blue-500' },
    { name: t('users.activeUsers'), value: Array.isArray(users) ? users.filter(u => u.status === 'active').length : 0, icon: UserCheck, color: 'bg-green-500' },
    { name: t('users.inactiveUsers'), value: Array.isArray(users) ? users.filter(u => u.status === 'inactive').length : 0, icon: UserX, color: 'bg-yellow-500' },
    { name: t('users.newThisMonth'), value: 0, icon: Plus, color: 'bg-purple-500' }
  ]

  // عرض حالة التحميل
  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-500 mx-auto"></div>
          <p className="mt-4 text-gray-600 dark:text-gray-400">{t('users.loading')}</p>
        </div>
      </div>
    )
  }

  // عرض حالة الخطأ
  if (error) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="text-red-500 text-6xl mb-4">⚠️</div>
          <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100 mb-2">{t('common.error')}</h2>
          <p className="text-gray-600 dark:text-gray-400 mb-4">{error}</p>
          <Button onClick={() => window.location.reload()}>{t('common.retry')}</Button>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="card-enhanced gradient-bg-soft">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100 flex items-center">
              <div className="w-3 h-8 bg-primary-500 rounded-full mr-4 rtl:mr-0 rtl:ml-4"></div>
              {t('users.title')}
            </h1>
            <p className="mt-2 text-lg text-gray-600 dark:text-gray-400 ml-7 rtl:ml-0 rtl:mr-7">
              {t('users.subtitle')}
            </p>
          </div>
          <div className="mt-6 sm:mt-0 flex flex-wrap gap-3">
            <Button
              variant="secondary"
              onClick={() => setShowDepartmentSettings(true)}
              icon={Building}
              className="hover:scale-105 transition-transform"
            >
              {t('users.departmentSettings')}
            </Button>
            <Button
              variant="outline"
              onClick={handleExportTemplate}
              icon={Download}
              className="hover:scale-105 transition-transform"
            >
              تصدير قالب Excel
            </Button>
            <Button
              variant="secondary"
              onClick={handleImportUsers}
              icon={Upload}
              className="hover:scale-105 transition-transform"
            >
              {t('users.importExcel')}
            </Button>
            <Button
              variant="primary"
              onClick={() => setShowAddModal(true)}
              icon={Plus}
              className="hover:scale-105 transition-transform shadow-lg"
            >
              {t('users.addUser')}
            </Button>

            {/* أزرار التشخيص */}
            <Button
              variant="secondary"
              onClick={debugLocalStorage}
              className="bg-yellow-600 hover:bg-yellow-700 text-white"
              title="تشخيص localStorage"
            >
              🔍 تشخيص
            </Button>

            <Button
              variant="secondary"
              onClick={clearUserData}
              className="bg-red-600 hover:bg-red-700 text-white"
              title="حذف جميع البيانات"
            >
              🗑️ حذف البيانات
            </Button>
          </div>
        </div>
      </div>

      {/* Stats */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
        {stats.map((stat, index) => {
          const Icon = stat.icon
          return (
            <motion.div
              key={stat.name}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
              className="bg-white dark:bg-gray-800 rounded-2xl p-6 shadow-lg border border-gray-200 dark:border-gray-700 hover:shadow-xl transition-all duration-300 hover:scale-105"
            >
              <div className="flex items-center justify-between">
                <div className="flex-1 text-right">
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400 mb-2">
                    {stat.name}
                  </p>
                  <NumberDisplay.Number
                    value={stat.value}
                    className="text-3xl font-bold text-gray-900 dark:text-gray-100"
                  />
                </div>
                <div className={`p-4 rounded-xl ${stat.color} shadow-lg`}>
                  <Icon className="h-8 w-8 text-white" />
                </div>
              </div>
            </motion.div>
          )
        })}
      </div>

      {/* Filters and Search */}
      <div className="bg-white dark:bg-gray-800 rounded-2xl p-6 shadow-lg border border-gray-200 dark:border-gray-700">
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-6 lg:space-y-0">
          <div className="flex flex-col sm:flex-row space-y-4 sm:space-y-0 sm:space-x-4 rtl:space-x-reverse flex-1">
            {/* Search */}
            <div className="relative flex-1 max-w-md">
              <Search className="absolute right-4 rtl:right-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
              <input
                type="text"
                placeholder={t('users.search')}
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pr-12 rtl:pr-12 pl-4 py-3 w-full border-2 border-gray-200 focus:border-blue-500 rounded-xl bg-gray-50 dark:bg-gray-700 dark:border-gray-600 dark:text-white text-right"
              />
            </div>

            {/* Role Filter */}
            <select
              value={selectedRole}
              onChange={(e) => setSelectedRole(e.target.value)}
              className="py-3 px-4 w-full sm:w-48 border-2 border-gray-200 focus:border-blue-500 rounded-xl bg-gray-50 dark:bg-gray-700 dark:border-gray-600 dark:text-white text-right"
            >
              <option value="all">{t('users.allRoles')}</option>
              <option value="admin">{t('users.admin')}</option>
              <option value="trainer">{t('users.trainer')}</option>
              <option value="course_manager">{t('users.courseManager')}</option>
              <option value="trainee">{t('users.trainee')}</option>
            </select>
          </div>

          <div className="flex flex-wrap gap-3">
            <Button
              variant="secondary-outline"
              onClick={() => toast.info('فلاتر إضافية - قريباً!')}
              icon={Filter}
              className="hover:scale-105 transition-transform bg-gray-100 hover:bg-gray-200 text-gray-700 border-gray-300"
            >
              {t('users.moreFilters')}
            </Button>
            <Button
              variant="secondary"
              onClick={handleExportUsers}
              loading={isExporting}
              icon={Download}
              className="hover:scale-105 transition-transform bg-green-500 hover:bg-green-600 text-white"
            >
              {t('users.export')}
            </Button>
          </div>
        </div>
      </div>

      {/* Users Table */}
      <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-lg border border-gray-200 dark:border-gray-700 overflow-hidden">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
            <thead className="bg-gray-50 dark:bg-gray-800">
              <tr>
                <th className="px-4 py-4 text-right text-sm font-semibold text-gray-700 dark:text-gray-300 w-1/4">
                  {t('users.name')}
                </th>
                <th className="px-4 py-4 text-center text-sm font-semibold text-gray-700 dark:text-gray-300 w-1/8">
                  {t('users.role')}
                </th>
                <th className="px-4 py-4 text-center text-sm font-semibold text-gray-700 dark:text-gray-300 w-1/8">
                  {t('common.department')}
                </th>
                <th className="px-4 py-4 text-center text-sm font-semibold text-gray-700 dark:text-gray-300 w-1/8">
                  {t('users.status')}
                </th>
                <th className="px-4 py-4 text-center text-sm font-semibold text-gray-700 dark:text-gray-300 w-1/8">
                  {t('users.joinDate')}
                </th>
                <th className="px-4 py-4 text-center text-sm font-semibold text-gray-700 dark:text-gray-300 w-1/8">
                  {t('users.lastLogin')}
                </th>
                <th className="px-4 py-4 text-center text-sm font-semibold text-gray-700 dark:text-gray-300 w-1/6">
                  {t('common.actions')}
                </th>
              </tr>
            </thead>
            <tbody className="bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-700">
              {filteredUsers.map((user) => (
                <motion.tr
                  key={user.id}
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  className="hover:bg-gray-50 dark:hover:bg-gray-800"
                >
                  <td className="px-4 py-4 whitespace-nowrap">
                    <div className="flex items-center justify-end">
                      <div className="text-right mr-3">
                        <div className="text-sm font-medium text-gray-900 dark:text-gray-100">
                          {user.name}
                        </div>
                        <div className="text-sm text-gray-500 dark:text-gray-400">
                          {user.email}
                        </div>
                      </div>
                      <GenderAvatar user={user} size="md" className="flex-shrink-0" />
                    </div>
                  </td>
                  <td className="px-4 py-4 whitespace-nowrap text-center">
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getRoleColor(user.role)}`}>
                      {getRoleDisplayName(user.role)}
                    </span>
                  </td>
                  <td className="px-4 py-4 whitespace-nowrap text-center">
                    <span className="text-sm text-gray-900 dark:text-gray-100">
                      {user.department || t('reports.notSpecified')}
                    </span>
                  </td>
                  <td className="px-4 py-4 whitespace-nowrap text-center">
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(user.status)}`}>
                      {getStatusDisplayName(user.status)}
                    </span>
                  </td>
                  <NumberDisplay.DateCell
                    value={user.joinDate}
                    className="px-4 py-4 whitespace-nowrap text-center text-sm text-gray-900 dark:text-gray-100"
                  />
                  <NumberDisplay.DateCell
                    value={user.lastLogin}
                    className="px-4 py-4 whitespace-nowrap text-center text-sm text-gray-900 dark:text-gray-100"
                  />
                  <td className="px-4 py-4 whitespace-nowrap text-center">
                    <div className="flex items-center justify-center space-x-1 rtl:space-x-reverse">
                      <button
                        onClick={() => handleViewUser(user.id)}
                        className="text-blue-600 hover:text-blue-800 dark:text-blue-400 p-2 rounded-lg hover:bg-blue-50 dark:hover:bg-blue-900/20 transition-colors"
                        title={t('common.view') || 'View User'}
                      >
                        <Eye className="w-4 h-4" />
                      </button>
                      <button
                        onClick={() => handleEditUser(user.id)}
                        className="text-green-600 hover:text-green-800 dark:text-green-400 p-2 rounded-lg hover:bg-green-50 dark:hover:bg-green-900/20 transition-colors"
                        title={t('common.edit') || 'Edit User'}
                      >
                        <Edit className="w-4 h-4" />
                      </button>
                      <button
                        onClick={() => handleDeleteUser(user.id)}
                        className="text-red-600 hover:text-red-800 dark:text-red-400 p-2 rounded-lg hover:bg-red-50 dark:hover:bg-red-900/20 transition-colors"
                        title={t('common.delete') || 'Delete User'}
                      >
                        <Trash2 className="w-4 h-4" />
                      </button>
                      <button
                        onClick={() => toast.info(t('toast.moreOptions'))}
                        className="text-gray-600 hover:text-gray-800 dark:text-gray-400 p-2 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-900/20 transition-colors"
                        title={t('toast.moreOptions') || 'More Options'}
                      >
                        <MoreVertical className="w-4 h-4" />
                      </button>
                    </div>
                  </td>
                </motion.tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* Create User Modal */}
      <CreateUserModal
        isOpen={showAddModal}
        onClose={() => setShowAddModal(false)}
        onUserCreated={handleCreateUser}
      />

      {/* Edit User Modal */}
      <EditUserModal
        isOpen={showEditModal}
        onClose={() => {
          setShowEditModal(false)
          setSelectedUser(null)
        }}
        onUserUpdated={handleUpdateUser}
        user={selectedUser}
      />



      {/* View User Modal */}
      <ViewUserModal
        isOpen={showViewModal}
        onClose={() => {
          setShowViewModal(false)
          setSelectedUser(null)
        }}
        user={selectedUser}
      />

      {/* Department Settings Modal */}
      {showDepartmentSettings && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <motion.div
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.95 }}
            className="bg-white dark:bg-gray-800 rounded-2xl p-6 w-full max-w-md mx-4 shadow-2xl"
          >
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-xl font-bold text-gray-900 dark:text-gray-100">
                {t('users.departmentSettings')}
              </h3>
              <button
                onClick={() => setShowDepartmentSettings(false)}
                className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
              >
                ✕
              </button>
            </div>

            {/* Add Department */}
            <div className="mb-6">
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                {t('users.addNewDepartment')}
              </label>
              <div className="flex gap-2">
                <input
                  type="text"
                  value={newDepartment}
                  onChange={(e) => setNewDepartment(e.target.value)}
                  placeholder={t('users.departmentName')}
                  className="flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
                  onKeyPress={(e) => e.key === 'Enter' && handleAddDepartment()}
                />
                <Button
                  onClick={handleAddDepartment}
                  variant="primary"
                  size="sm"
                  icon={Plus}
                >
                  {t('common.add')}
                </Button>
              </div>
            </div>

            {/* Departments List */}
            <div>
              <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
                {t('users.currentDepartments')} ({departments.length})
              </h4>
              <div className="space-y-2 max-h-60 overflow-y-auto">
                {departments.length === 0 ? (
                  <p className="text-gray-500 dark:text-gray-400 text-center py-4">
                    {t('users.noDepartments')}
                  </p>
                ) : (
                  departments.map((dept) => (
                    <div
                      key={dept.id}
                      className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg"
                    >
                      <span className="text-gray-900 dark:text-gray-100">
                        {dept.name}
                      </span>
                      <button
                        onClick={() => handleDeleteDepartment(dept.id)}
                        className="text-red-600 hover:text-red-800 dark:text-red-400 p-1 rounded"
                        title={t('users.deleteDepartment')}
                      >
                        <Trash2 className="w-4 h-4" />
                      </button>
                    </div>
                  ))
                )}
              </div>
            </div>

            {/* Close Button */}
            <div className="mt-6 flex justify-end">
              <Button
                onClick={() => setShowDepartmentSettings(false)}
                variant="secondary"
              >
                {t('common.close')}
              </Button>
            </div>
          </motion.div>
        </div>
      )}
    </div>
  )
}

export default UserManagement
