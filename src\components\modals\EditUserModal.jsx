import React, { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import toast from 'react-hot-toast'
import { User, Mail, Phone, Lock, UserCheck, Building } from 'lucide-react'
import Modal from '../ui/Modal'
import Button from '../ui/Button'
import { useLanguage } from '../../contexts/LanguageContext'

const EditUserModal = ({ isOpen, onClose, onUserUpdated, user }) => {
  const { t } = useLanguage()
  const [loading, setLoading] = useState(false)
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    role: 'trainee',
    department: '',
    gender: '',
    password: ''
  })
  const [departments, setDepartments] = useState([])
  const [errors, setErrors] = useState({})

  // تحميل الأقسام
  useEffect(() => {
    const loadDepartments = () => {
      try {
        // تحميل الأقسام من departments_data
        const departmentsData = JSON.parse(localStorage.getItem('departments_data') || '[]')

        console.log('🔍 البحث عن الأقسام في EditUserModal:', {
          raw: localStorage.getItem('departments_data'),
          parsed: departmentsData,
          length: departmentsData.length
        })

        if (departmentsData.length > 0) {
          const departmentNames = departmentsData.map(dept => dept.name || dept)
          setDepartments(departmentNames)
          console.log('✅ تم تحميل الأقسام:', departmentNames)
        } else {
          setDepartments([])
          console.log('⚠️ لا توجد أقسام مضافة - يرجى إضافة أقسام من صفحة إدارة المستخدمين')
        }
      } catch (error) {
        console.error('خطأ في تحميل الأقسام:', error)
        setDepartments([])
      }
    }
    loadDepartments()

    // إعادة تحميل الأقسام عند فتح النافذة
    if (isOpen) {
      loadDepartments()
    }
  }, [isOpen])

  // تحديث البيانات عند تغيير المستخدم
  useEffect(() => {
    if (user) {
      setFormData({
        name: user.name || '',
        email: user.email || '',
        phone: user.phone || '',
        role: user.role || 'trainee',
        department: user.department || '',
        gender: user.gender || '',
        password: user.password || ''
      })
    }
  }, [user])

  const handleInputChange = (e) => {
    const { name, value } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: value
    }))
    
    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }))
    }
  }

  const validateForm = () => {
    const newErrors = {}

    if (!formData.name.trim()) {
      newErrors.name = t('auth.fullName') + ' ' + t('common.required')
    }

    // Password is optional in edit mode
    if (formData.password.trim() && formData.password.length < 6) {
      newErrors.password = t('auth.passwordMinLength')
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = async (e) => {
    e.preventDefault()
    
    if (!validateForm()) {
      return
    }

    setLoading(true)
    try {
      const updatedUser = {
        name: formData.name,
        email: formData.email || undefined, // إرسال undefined إذا كان فارغاً
        phone: formData.phone,
        role: formData.role,
        department: formData.department,
        gender: formData.gender
      }

      // إضافة كلمة المرور فقط إذا تم إدخالها
      if (formData.password.trim()) {
        updatedUser.password = formData.password
      }

      if (onUserUpdated) {
        await onUserUpdated(user.id, updatedUser)
      }

      toast.success(t('toast.userUpdated') || 'تم تحديث المستخدم بنجاح!')
      handleClose()
    } catch (error) {
      toast.error(error.message || 'فشل في تحديث المستخدم')
      console.error('Error updating user:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleClose = () => {
    setFormData({
      name: '',
      email: '',
      phone: '',
      role: 'trainee',
      department: '',
      gender: '',
      password: ''
    })
    setErrors({})
    onClose()
  }

  if (!user) return null

  return (
    <Modal
      isOpen={isOpen}
      onClose={handleClose}
      title={t('users.editUser') || 'تعديل المستخدم'}
      size="md"
    >
      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Name */}
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            {t('auth.fullName') || 'الاسم الكامل'} *
          </label>
          <div className="relative">
            <User className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <input
              type="text"
              name="name"
              value={formData.name}
              onChange={handleInputChange}
              className={`input-field pl-10 ${errors.name ? 'border-red-500' : ''}`}
              placeholder={t('auth.fullName') || 'أدخل الاسم الكامل'}
            />
          </div>
          {errors.name && (
            <p className="mt-1 text-sm text-red-600">{errors.name}</p>
          )}
        </div>

        {/* Email */}
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            {t('auth.email') || 'البريد الإلكتروني'} {t('common.optional') || '(اختياري)'}
          </label>
          <div className="relative">
            <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <input
              type="email"
              name="email"
              value={formData.email}
              onChange={handleInputChange}
              className={`input-field pl-10 ${errors.email ? 'border-red-500' : ''}`}
              placeholder={t('auth.email') || 'أدخل البريد الإلكتروني'}
            />
          </div>
          {errors.email && (
            <p className="mt-1 text-sm text-red-600">{errors.email}</p>
          )}
        </div>

        {/* Phone */}
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            {t('auth.phone') || 'رقم الهاتف'} {t('common.optional') || '(اختياري)'}
          </label>
          <div className="relative">
            <Phone className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <input
              type="tel"
              name="phone"
              value={formData.phone}
              onChange={handleInputChange}
              className="input-field pl-10"
              placeholder={t('auth.phone') || 'أدخل رقم الهاتف'}
            />
          </div>
        </div>

        {/* Role */}
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            {t('auth.role') || 'الدور'} *
          </label>
          <div className="relative">
            <UserCheck className="absolute left-3 rtl:left-auto rtl:right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <select
              name="role"
              value={formData.role}
              onChange={handleInputChange}
              className="input-field pl-10 rtl:pl-4 rtl:pr-10"
            >
              <option value="trainee">{t('users.trainee') || 'متدرب'}</option>
              <option value="trainer">{t('users.trainer') || 'مدرب'}</option>
              <option value="course_manager">{t('users.courseManager') || 'مسؤول الدورة'}</option>
              <option value="admin">{t('users.admin') || 'مدير النظام'}</option>
            </select>
          </div>
        </div>

        {/* Department */}
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            القسم
          </label>
          <div className="relative">
            <Building className="absolute left-3 rtl:left-auto rtl:right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <select
              name="department"
              value={formData.department}
              onChange={handleInputChange}
              className="input-field pl-10 rtl:pl-4 rtl:pr-10"
              disabled={departments.length === 0}
            >
              <option value="">
                {departments.length === 0 ? t('department.noDepartments') : t('department.selectDepartment')}
              </option>
              {departments.map(dept => (
                <option key={dept} value={dept}>{dept}</option>
              ))}
            </select>
          </div>
          {departments.length === 0 && (
            <p className="mt-1 text-sm text-amber-600">
              {t('department.addFromSettings')}
            </p>
          )}
        </div>

        {/* Gender */}
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            {t('auth.gender')} *
          </label>
          <div className="relative">
            <UserCheck className="absolute left-3 rtl:left-auto rtl:right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <select
              name="gender"
              value={formData.gender}
              onChange={handleInputChange}
              className={`input-field pl-10 rtl:pl-4 rtl:pr-10 ${errors.gender ? 'border-red-500' : ''}`}
            >
              <option value="">{t('gender.selectGender')}</option>
              <option value="male">{t('gender.male')}</option>
              <option value="female">{t('gender.female')}</option>
            </select>
          </div>
          {errors.gender && (
            <p className="mt-1 text-sm text-red-600">{errors.gender}</p>
          )}
        </div>

        {/* Password */}
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            {t('auth.passwordOptional')}
          </label>
          <div className="relative">
            <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <input
              type="password"
              name="password"
              value={formData.password}
              onChange={handleInputChange}
              className={`input-field pl-10 ${errors.password ? 'border-red-500' : ''}`}
              placeholder={t('auth.leaveEmptyNoChange')}
            />
          </div>
          {errors.password && (
            <p className="mt-1 text-sm text-red-600">{errors.password}</p>
          )}
          <p className="mt-1 text-xs text-gray-500 dark:text-gray-400">
            {t('auth.leaveEmptyNoChangeCurrent')}
          </p>
        </div>

        {/* Actions */}
        <div className="flex space-x-3 pt-4">
          <Button
            type="button"
            variant="outline"
            onClick={handleClose}
            className="flex-1"
            disabled={loading}
          >
            {t('common.cancel') || 'إلغاء'}
          </Button>
          <Button
            type="submit"
            variant="primary"
            loading={loading}
            className="flex-1"
          >
            {t('common.save') || 'حفظ'}
          </Button>
        </div>
      </form>
    </Modal>
  )
}

export default EditUserModal
