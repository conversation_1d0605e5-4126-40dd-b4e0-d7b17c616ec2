import React, { useState, useEffect } from 'react'
import { useParams, useNavigate } from 'react-router-dom'
import { motion } from 'framer-motion'
import { toast } from 'react-hot-toast'
import {
  ArrowLeft,
  Calendar,
  Clock,
  Users,
  BookOpen,
  Eye,
  MessageSquare,
  Settings,
  HelpCircle,
  UserCheck
} from 'lucide-react'
import Button from '../../components/ui/Button'
import WorkshopChat from '../../components/workshop/WorkshopChat'
import QuizManager from '../../components/workshop/QuizManager'
import GroupManager from '../../components/workshop/GroupManager'
import ContentManager from '../../components/workshop/ContentManager'
import AttendanceManager from '../../components/workshop/AttendanceManager'
import { useAuth } from '../../contexts/AuthContext'
import dataStorage from '../../utils/dataStorage'

const TrainerWorkshopDetails = () => {
  const { workshopId } = useParams()
  const navigate = useNavigate()
  const { user } = useAuth()
  const [workshop, setWorkshop] = useState(null)
  const [activeTab, setActiveTab] = useState('overview')
  const [loading, setLoading] = useState(true)
  const [materials, setMaterials] = useState([])
  const [groups, setGroups] = useState([])
  const [chatEnabled, setChatEnabled] = useState(false)

  useEffect(() => {
    loadWorkshopDetails()
  }, [workshopId])

  const loadWorkshopDetails = () => {
    try {
      setLoading(true)
      const allWorkshops = dataStorage.loadWorkshops()
      const foundWorkshop = allWorkshops.find(w => w.id === workshopId)
      
      if (!foundWorkshop) {
        toast.error('ورشة العمل غير موجودة')
        navigate('/trainer/workshops')
        return
      }

      // التحقق من أن المدرب يملك هذه الورشة أو هو مدير
      if (foundWorkshop.trainer !== user.name && foundWorkshop.trainerId !== user.id && user.role !== 'admin') {
        toast.error('ليس لديك صلاحية للوصول إلى هذه الورشة')
        navigate('/trainer/workshops')
        return
      }

      // تشخيص بيانات ورشة العمل
      console.log('🔍 TrainerWorkshopDetails - تشخيص بيانات ورشة العمل:', {
        workshopId,
        foundWorkshop: {
          id: foundWorkshop.id,
          title: foundWorkshop.title,
          selectedTraineesCount: foundWorkshop.selectedTrainees?.length || 0,
          selectedTrainees: foundWorkshop.selectedTrainees?.slice(0, 3), // عرض أول 3 فقط
          participantsCount: foundWorkshop.participants?.length || 0,
          participants: foundWorkshop.participants?.slice(0, 3) // عرض أول 3 فقط
        }
      })

      setWorkshop(foundWorkshop)

      // تحميل المواد والمجموعات
      const workshopMaterials = JSON.parse(localStorage.getItem(`workshop_materials_${workshopId}`) || '[]')
      const workshopGroups = dataStorage.loadGroups(workshopId)

      // تحميل حالة المحادثة
      const chatSettings = JSON.parse(localStorage.getItem(`workshop_chat_settings_${workshopId}`) || '{}')
      setChatEnabled(chatSettings.enabled || false)

      setMaterials(workshopMaterials)
      setGroups(workshopGroups)
    } catch (error) {
      console.error('Error loading workshop details:', error)
      toast.error('فشل في تحميل تفاصيل ورشة العمل')
    } finally {
      setLoading(false)
    }
  }

  const toggleChat = () => {
    const newChatEnabled = !chatEnabled
    setChatEnabled(newChatEnabled)

    // حفظ إعدادات المحادثة
    const chatSettings = { enabled: newChatEnabled }
    localStorage.setItem(`workshop_chat_settings_${workshopId}`, JSON.stringify(chatSettings))

    toast.success(newChatEnabled ? 'تم تفعيل المحادثة العامة' : 'تم إلغاء تفعيل المحادثة العامة')
  }

  const tabs = [
    { id: 'overview', name: 'نظرة عامة', icon: Eye },
    { id: 'content', name: 'المحتوى والمواد', icon: BookOpen },
    { id: 'groups', name: 'المجموعات والنقاط', icon: Users },
    { id: 'attendance', name: 'إدارة الحضور', icon: UserCheck },
    { id: 'quizzes', name: 'الاختبارات والأسئلة', icon: HelpCircle },
    { id: 'chat', name: 'المحادثة العامة', icon: MessageSquare },
    { id: 'settings', name: 'الإعدادات', icon: Settings }
  ]

  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
        <span className="ml-2 text-gray-600 dark:text-gray-400">جاري تحميل تفاصيل ورشة العمل...</span>
      </div>
    )
  }

  if (!workshop) {
    return (
      <div className="text-center py-12">
        <BookOpen className="mx-auto h-12 w-12 text-gray-400" />
        <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-gray-100">
          ورشة العمل غير موجودة
        </h3>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4 rtl:space-x-reverse">
          <Button
            variant="outline"
            onClick={() => navigate('/trainer/workshops')}
            icon={ArrowLeft}
            size="sm"
          >
            العودة
          </Button>
          <div>
            <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100">
              {workshop.title}
            </h1>
            <p className="text-sm text-gray-500 dark:text-gray-400">
              إدارة ورشة العمل والمحتوى
            </p>
          </div>
        </div>
      </div>

      {/* Workshop Info Card */}
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div className="flex items-center">
            <Calendar className="w-5 h-5 text-blue-600 mr-2" />
            <div>
              <p className="text-sm text-gray-500 dark:text-gray-400">تاريخ البداية</p>
              <p className="font-medium text-gray-900 dark:text-gray-100">{workshop.startDate}</p>
            </div>
          </div>
          <div className="flex items-center">
            <Clock className="w-5 h-5 text-green-600 mr-2" />
            <div>
              <p className="text-sm text-gray-500 dark:text-gray-400">الوقت والمدة</p>
              <p className="font-medium text-gray-900 dark:text-gray-100">{workshop.startTime} - {workshop.duration} دقيقة</p>
            </div>
          </div>
          <div className="flex items-center">
            <Users className="w-5 h-5 text-purple-600 mr-2" />
            <div>
              <p className="text-sm text-gray-500 dark:text-gray-400">المشاركين</p>
              <p className="font-medium text-gray-900 dark:text-gray-100">{workshop.selectedTrainees?.length || 0} متدرب</p>
            </div>
          </div>
          <div className="flex items-center">
            <BookOpen className="w-5 h-5 text-orange-600 mr-2" />
            <div>
              <p className="text-sm text-gray-500 dark:text-gray-400">المجموعات</p>
              <p className="font-medium text-gray-900 dark:text-gray-100">{groups.length} مجموعة</p>
            </div>
          </div>
        </div>
      </div>

      {/* Tabs */}
      <div className="border-b border-gray-200 dark:border-gray-700">
        <nav className="-mb-px flex space-x-8 rtl:space-x-reverse">
          {tabs.map((tab) => {
            const Icon = tab.icon
            return (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`flex items-center py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === tab.id
                    ? 'border-primary-500 text-primary-600 dark:text-primary-400'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300'
                }`}
              >
                <Icon className="w-4 h-4 mr-2" />
                {tab.name}
              </button>
            )
          })}
        </nav>
      </div>

      {/* Tab Content */}
      <div className="mt-6">
        {activeTab === 'overview' && (
          <OverviewTab workshop={workshop} materials={materials} groups={groups} />
        )}
        {activeTab === 'content' && (
          <ContentManager
            workshopId={workshopId}
            workshopTitle={workshop.title}
          />
        )}
        {activeTab === 'groups' && (
          <GroupManager
            workshopId={workshopId}
            workshopTitle={workshop.title}
            participants={workshop.selectedTrainees || []}
          />
        )}
        {activeTab === 'attendance' && (
          <AttendanceManager
            workshopId={workshopId}
            workshopTitle={workshop.title}
            participants={workshop.selectedTrainees || []}
          />
        )}
        {activeTab === 'quizzes' && (
          <QuizManager
            workshopId={workshopId}
            groups={groups}
          />
        )}
        {activeTab === 'chat' && (
          <WorkshopChat
            workshopId={workshopId}
            isEnabled={chatEnabled}
            onToggleChat={toggleChat}
          />
        )}
        {activeTab === 'settings' && (
          <SettingsTab workshop={workshop} />
        )}
      </div>
    </div>
  )
}

// Overview Tab Component
const OverviewTab = ({ workshop, materials, groups }) => {
  const totalPoints = groups.reduce((sum, group) => sum + (group.points || 0), 0)
  const avgPoints = groups.length > 0 ? Math.round(totalPoints / groups.length) : 0

  return (
    <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
      {/* Description */}
      <div className="lg:col-span-2 bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">
          {t('workshops.workshopDescription') || 'وصف ورشة العمل'}
        </h3>
        <p className="text-gray-600 dark:text-gray-400 leading-relaxed">
          {workshop.description || t('workshops.noDescriptionAvailable') || 'لا يوجد وصف متاح لهذه الورشة.'}
        </p>
      </div>

      {/* Quick Stats */}
      <div className="space-y-4">
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">
            إحصائيات سريعة
          </h3>
          <div className="space-y-3">
            <div className="flex justify-between">
              <span className="text-gray-600 dark:text-gray-400">المتدربين</span>
              <span className="font-medium text-gray-900 dark:text-gray-100">{workshop.selectedTrainees?.length || 0}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600 dark:text-gray-400">المجموعات</span>
              <span className="font-medium text-gray-900 dark:text-gray-100">{groups.length}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600 dark:text-gray-400">إجمالي النقاط</span>
              <span className="font-medium text-gray-900 dark:text-gray-100">{totalPoints}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600 dark:text-gray-400">متوسط النقاط</span>
              <span className="font-medium text-gray-900 dark:text-gray-100">{avgPoints}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

// Settings Tab Component
const SettingsTab = ({ workshop }) => {
  return (
    <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
      <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">
        إعدادات ورشة العمل
      </h3>
      <p className="text-gray-600 dark:text-gray-400">
        إعدادات ورشة العمل ستكون متاحة قريباً...
      </p>
    </div>
  )
}

export default TrainerWorkshopDetails
