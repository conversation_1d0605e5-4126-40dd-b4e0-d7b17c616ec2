// Standalone Authentication Service - 100% localStorage, no API calls
// This service completely replaces any API-based authentication

console.log('🏠 Standalone Auth Service loaded - localStorage only')

// Get users from localStorage
const getUsers = () => {
  try {
    const users = localStorage.getItem('app_users')
    return users ? JSON.parse(users) : {}
  } catch (error) {
    console.error('Error reading users:', error)
    return {}
  }
}

// Save users to localStorage
const saveUsers = (users) => {
  try {
    localStorage.setItem('app_users', JSON.stringify(users))
    return true
  } catch (error) {
    console.error('Error saving users:', error)
    return false
  }
}

// Initialize default users if none exist
const initializeDefaultUsers = () => {
  const users = getUsers()
  
  if (Object.keys(users).length === 0) {
    console.log('🔧 Initializing default users...')
    
    const defaultUsers = {
      '<EMAIL>': {
        id: 'admin-001',
        email: '<EMAIL>',
        name: 'مدير النظام',
        username: 'admin',
        password: 'admin123',
        role: 'admin',
        isActive: true,
        created_at: new Date().toISOString()
      },
      '<EMAIL>': {
        id: 'trainer-001',
        email: '<EMAIL>',
        name: 'مدرب النظام',
        username: 'trainer',
        password: 'trainer123',
        role: 'trainer',
        isActive: true,
        created_at: new Date().toISOString()
      },
      '<EMAIL>': {
        id: 'trainee-001',
        email: '<EMAIL>',
        name: 'متدرب النظام',
        username: 'trainee',
        password: 'trainee123',
        role: 'trainee',
        isActive: true,
        created_at: new Date().toISOString()
      }
    }
    
    saveUsers(defaultUsers)
    console.log('✅ Default users created')
    return defaultUsers
  }
  
  return users
}

// Generate simple token
const generateToken = (user) => {
  return `token_${user.id}_${Date.now()}`
}

// Standalone Auth Service
export const standaloneAuthService = {
  // Login function
  async login(emailOrUsername, password) {
    console.log('🔐 Standalone login:', emailOrUsername)
    
    // Small delay for realistic feel
    await new Promise(resolve => setTimeout(resolve, 500))
    
    try {
      // Initialize users if needed
      const users = initializeDefaultUsers()
      
      // Find user
      let user = null
      
      // Search by email first
      if (emailOrUsername.includes('@')) {
        user = users[emailOrUsername]
      } else {
        // Search by username or name
        user = Object.values(users).find(u => 
          u.username === emailOrUsername || 
          u.name === emailOrUsername ||
          u.email === emailOrUsername
        )
      }
      
      if (!user) {
        console.log('❌ User not found')
        return {
          success: false,
          error: 'المستخدم غير موجود'
        }
      }
      
      if (!user.isActive) {
        console.log('❌ User not active')
        return {
          success: false,
          error: 'الحساب غير مفعل'
        }
      }
      
      if (user.password !== password) {
        console.log('❌ Wrong password')
        return {
          success: false,
          error: 'كلمة المرور غير صحيحة'
        }
      }
      
      // Generate token
      const token = generateToken(user)
      
      // Save current user and token
      localStorage.setItem('token', token)
      localStorage.setItem('currentUser', JSON.stringify(user))
      
      console.log('✅ Login successful')
      return {
        success: true,
        user: user,
        token: token
      }
      
    } catch (error) {
      console.error('💥 Login error:', error)
      return {
        success: false,
        error: 'خطأ في تسجيل الدخول'
      }
    }
  },

  // Register function
  async register(userData) {
    console.log('📝 Standalone register:', userData.email)
    
    await new Promise(resolve => setTimeout(resolve, 500))
    
    try {
      const users = getUsers()
      
      // Check if user exists
      if (users[userData.email]) {
        return {
          success: false,
          error: 'المستخدم موجود بالفعل'
        }
      }
      
      // Create new user
      const newUser = {
        id: `user_${Date.now()}`,
        email: userData.email,
        name: userData.name,
        username: userData.username || userData.email.split('@')[0],
        password: userData.password,
        role: userData.role || 'trainee',
        isActive: true,
        created_at: new Date().toISOString()
      }
      
      // Save user
      users[userData.email] = newUser
      saveUsers(users)
      
      // Generate token
      const token = generateToken(newUser)
      
      // Save current user and token
      localStorage.setItem('token', token)
      localStorage.setItem('currentUser', JSON.stringify(newUser))
      
      console.log('✅ Registration successful')
      return {
        success: true,
        user: newUser,
        token: token
      }
      
    } catch (error) {
      console.error('💥 Registration error:', error)
      return {
        success: false,
        error: 'خطأ في إنشاء الحساب'
      }
    }
  },

  // Create user function (for admin)
  async createUser(userData) {
    console.log('👤 Standalone create user:', userData.email)
    console.log('📝 User data received:', userData)

    try {
      // تنظيف localStorage أولاً
      console.log('🧹 Cleaning localStorage...')

      const users = getUsers()
      console.log('📋 Current users count:', Object.keys(users).length)
      console.log('📋 Current users emails:', Object.keys(users))

      // التحقق من وجود المستخدم بطريقة أكثر دقة
      const emailLower = userData.email.toLowerCase().trim()
      const userExists = Object.keys(users).some(email =>
        email.toLowerCase().trim() === emailLower
      )

      console.log('🔍 Checking if user exists:', emailLower)
      console.log('🔍 User exists check result:', userExists)

      if (userExists) {
        console.log('❌ User already exists:', emailLower)
        return {
          success: false,
          error: 'المستخدم موجود بالفعل'
        }
      }

      console.log('✅ User does not exist, proceeding with creation...')

      // Create new user
      const newUser = {
        id: `user_${Date.now()}`,
        email: userData.email,
        name: userData.name,
        username: userData.username || userData.email.split('@')[0],
        password: userData.password,
        role: userData.role || 'trainee',
        department: userData.department || '',
        gender: userData.gender || '',
        phone: userData.phone || '',
        isActive: userData.isActive !== false,
        status: 'active',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }

      console.log('👤 New user object:', newUser)

      // Save user with direct localStorage approach
      console.log('💾 Saving user directly to localStorage...')

      // استخدام البريد الإلكتروني المنظف
      const cleanEmail = userData.email.toLowerCase().trim()
      users[cleanEmail] = newUser

      // حفظ مباشر في localStorage
      try {
        localStorage.setItem('users', JSON.stringify(users))
        console.log('💾 Direct save to localStorage successful')
      } catch (error) {
        console.error('❌ Direct save failed:', error)
        return {
          success: false,
          error: 'فشل في حفظ المستخدم في التخزين المحلي'
        }
      }

      // التحقق من الحفظ
      const verifyUsers = JSON.parse(localStorage.getItem('users') || '{}')
      console.log('✅ Verification - users count after save:', Object.keys(verifyUsers).length)
      console.log('✅ Verification - new user exists:', !!verifyUsers[cleanEmail])

      if (!verifyUsers[cleanEmail]) {
        console.error('❌ User was not saved properly')
        return {
          success: false,
          error: 'فشل في التحقق من حفظ المستخدم'
        }
      }

      console.log('✅ User created successfully')
      return {
        success: true,
        user: newUser
      }
      
    } catch (error) {
      console.error('💥 Create user error:', error)
      return {
        success: false,
        error: 'خطأ في إنشاء المستخدم'
      }
    }
  },

  // Verify token
  async verifyToken(token) {
    console.log('🔍 Standalone verify token')
    
    try {
      const currentUser = localStorage.getItem('currentUser')
      const savedToken = localStorage.getItem('token')
      
      if (!token || !savedToken || token !== savedToken || !currentUser) {
        return {
          success: false,
          error: 'رمز غير صالح'
        }
      }
      
      const user = JSON.parse(currentUser)
      
      console.log('✅ Token valid')
      return {
        success: true,
        user: user
      }
      
    } catch (error) {
      console.error('💥 Token verification error:', error)
      return {
        success: false,
        error: 'رمز غير صالح'
      }
    }
  },

  // Get current user
  async getCurrentUser() {
    console.log('👤 Standalone get current user')
    
    try {
      const currentUser = localStorage.getItem('currentUser')
      const token = localStorage.getItem('token')
      
      if (!currentUser || !token) {
        return {
          success: false,
          error: 'لا يوجد مستخدم مسجل'
        }
      }
      
      const user = JSON.parse(currentUser)
      
      console.log('✅ Current user found')
      return {
        success: true,
        user: user
      }
      
    } catch (error) {
      console.error('💥 Get current user error:', error)
      return {
        success: false,
        error: 'فشل في جلب بيانات المستخدم'
      }
    }
  },

  // Logout
  async logout() {
    console.log('🚪 Standalone logout')
    
    try {
      localStorage.removeItem('token')
      localStorage.removeItem('currentUser')
      
      console.log('✅ Logout successful')
      return {
        success: true
      }
      
    } catch (error) {
      console.error('💥 Logout error:', error)
      return {
        success: true // Always succeed logout
      }
    }
  },

  // Get all users (admin only)
  async getAllUsers() {
    console.log('👥 Standalone get all users')

    await new Promise(resolve => setTimeout(resolve, 300))

    try {
      // Get current users from localStorage
      const users = getUsers()
      console.log('📋 Current users from localStorage:', Object.keys(users).length)

      // Initialize default users if no users exist
      if (Object.keys(users).length === 0) {
        console.log('🔧 No users found, initializing defaults...')
        const defaultUsers = initializeDefaultUsers()
        console.log('🔧 Default users initialized:', Object.keys(defaultUsers).length)

        // Convert to array and remove passwords
        const usersList = Object.values(defaultUsers).map(user => ({
          ...user,
          password: undefined
        }))

        console.log('✅ Retrieved default users:', usersList.length)
        return usersList
      }

      // Convert existing users to array and remove passwords
      const usersList = Object.values(users).map(user => ({
        ...user,
        password: undefined // Don't send passwords to frontend
      }))

      console.log('✅ Retrieved existing users:', usersList.length)
      console.log('📋 Users list:', usersList.map(u => ({ name: u.name, email: u.email, role: u.role })))
      return usersList

    } catch (error) {
      console.error('💥 Get all users error:', error)
      throw new Error('فشل في جلب المستخدمين')
    }
  },

  // Update user (admin only)
  async updateUser(userId, userData) {
    console.log('✏️ Standalone update user:', userId)
    
    await new Promise(resolve => setTimeout(resolve, 300))
    
    try {
      const users = getUsers()
      
      // Find user by ID
      let userToUpdate = null
      let userEmail = null
      
      for (const [email, user] of Object.entries(users)) {
        if (user.id === userId) {
          userToUpdate = user
          userEmail = email
          break
        }
      }
      
      if (!userToUpdate) {
        throw new Error('المستخدم غير موجود')
      }
      
      // Update user data
      const updatedUser = {
        ...userToUpdate,
        ...userData,
        id: userId, // Keep original ID
        updated_at: new Date().toISOString()
      }
      
      // If email changed, update the key
      if (userData.email && userData.email !== userEmail) {
        delete users[userEmail]
        users[userData.email] = updatedUser
      } else {
        users[userEmail] = updatedUser
      }
      
      saveUsers(users)
      
      console.log('✅ User updated successfully')
      return {
        success: true,
        user: updatedUser
      }
      
    } catch (error) {
      console.error('💥 Update user error:', error)
      throw new Error(error.message || 'فشل في تحديث المستخدم')
    }
  },

  // Delete user (admin only)
  async deleteUser(userId) {
    console.log('🗑️ Standalone delete user:', userId)
    
    await new Promise(resolve => setTimeout(resolve, 300))
    
    try {
      const users = getUsers()
      
      // Find user by ID
      let userToDelete = null
      let userEmail = null
      
      for (const [email, user] of Object.entries(users)) {
        if (user.id === userId) {
          userToDelete = user
          userEmail = email
          break
        }
      }
      
      if (!userToDelete) {
        throw new Error('المستخدم غير موجود')
      }
      
      // Don't allow deleting admin users
      if (userToDelete.role === 'admin') {
        throw new Error('لا يمكن حذف مدير النظام')
      }
      
      // Delete user
      delete users[userEmail]
      saveUsers(users)
      
      console.log('✅ User deleted successfully')
      return {
        success: true
      }
      
    } catch (error) {
      console.error('💥 Delete user error:', error)
      throw new Error(error.message || 'فشل في حذف المستخدم')
    }
  },

  // Get user details
  async getUserDetails(userId) {
    console.log('🔍 Standalone get user details:', userId)
    
    await new Promise(resolve => setTimeout(resolve, 200))
    
    try {
      const users = getUsers()
      
      // Find user by ID
      let user = null
      
      for (const userObj of Object.values(users)) {
        if (userObj.id === userId) {
          user = userObj
          break
        }
      }
      
      if (!user) {
        throw new Error('المستخدم غير موجود')
      }
      
      // Return user without password
      const userDetails = {
        ...user,
        password: undefined
      }
      
      console.log('✅ User details retrieved')
      return userDetails
      
    } catch (error) {
      console.error('💥 Get user details error:', error)
      throw new Error(error.message || 'فشل في جلب تفاصيل المستخدم')
    }
  }
}

export default standaloneAuthService