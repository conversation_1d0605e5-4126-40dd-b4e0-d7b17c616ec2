import React, { useState, useEffect } from 'react'
import { useNavigate } from 'react-router-dom'
import { motion } from 'framer-motion'
import toast from 'react-hot-toast'
import { safeToast } from '../../utils/toastManager'
import {
  Plus,
  Search,
  Filter,
  Download,
  Upload,
  Edit,
  Trash2,
  Eye,
  Users,
  Calendar,
  Clock,
  User,
  FileText,
  Award,
  Shuffle,
  MessageSquare,
  PlayCircle,
  Settings,
  Trophy,
  Target
} from 'lucide-react'
import Button from '../../components/ui/Button'
import CreateWorkshopModal from '../../components/modals/CreateWorkshopModal'
import CreateGroupModal from '../../components/modals/CreateGroupModal'
import { useLanguage } from '../../contexts/LanguageContext'
import dataStorage from '../../utils/dataStorage'
// تم حذف جميع سكريبتات الإصلاح الوهمية - لا توجد استيرادات

const WorkshopManagement = () => {
  const { t } = useLanguage()
  const navigate = useNavigate()
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedFilter, setSelectedFilter] = useState('all')
  const [showCreateModal, setShowCreateModal] = useState(false)
  const [showGroupModal, setShowGroupModal] = useState(false)
  const [selectedWorkshop, setSelectedWorkshop] = useState(null)
  const [loading, setLoading] = useState(true)
  const [isExporting, setIsExporting] = useState(false)
  const [workshops, setWorkshops] = useState([])

  // تحميل ورش العمل عند تحميل الصفحة
  useEffect(() => {
    loadWorkshops()

    // تشغيل التنظيف الشامل أولاً
    const hasRunCleanup = localStorage.getItem('timeline_cleanup_done')
    if (!hasRunCleanup) {
      try {
        fullCleanup()
        localStorage.setItem('timeline_cleanup_done', 'true')
        console.log('✅ Comprehensive cleanup executed automatically')
      } catch (error) {
        console.error('❌ Failed to run cleanup:', error)
      }
    }

    // All dummy scripts removed - no need to run anything
    console.log('✅ System is clean from dummy data')
  }, [])

  const loadWorkshops = () => {
    try {
      setLoading(true)

      // تحميل ورش العمل من مصادر متعددة للتوافق
      const workshopsFromDataStorage = dataStorage.loadWorkshops()
      const workshopsFromLocalStorage = JSON.parse(localStorage.getItem('workshops_data') || '[]')

      // دمج ورش العمل من المصادر المختلفة
      const allWorkshops = [...workshopsFromDataStorage, ...workshopsFromLocalStorage]

      // إزالة المكررات بناءً على ID
      const uniqueWorkshops = allWorkshops.reduce((acc, current) => {
        const existingWorkshop = acc.find(workshop => workshop.id === current.id)
        if (!existingWorkshop) {
          acc.push(current)
        }
        return acc
      }, [])

      console.log('✅ Workshops loaded:', uniqueWorkshops.length)
      setWorkshops(uniqueWorkshops)
    } catch (error) {
      console.error('❌ Error loading workshops:', error)
      toast.error(t('workshops.loadFailed'))
    } finally {
      setLoading(false)
    }
  }

  const filteredWorkshops = workshops.filter(workshop => {
    const matchesSearch = workshop.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         workshop.course.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         workshop.trainer.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesFilter = selectedFilter === 'all' || workshop.status === selectedFilter
    return matchesSearch && matchesFilter
  })

  const getStatusColor = (status) => {
    switch (status) {
      case 'ongoing': return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200'
      case 'scheduled': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
      case 'completed': return 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200'
      case 'cancelled': return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
      case 'draft': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200'
    }
  }

  const getDaysUntilStart = (startDate) => {
    const today = new Date()
    const start = new Date(startDate)
    const diffTime = start - today
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
    return diffDays
  }

  const getTotalHours = (startDate, endDate, dailyHours) => {
    if (!startDate || !endDate || !dailyHours) return 0
    const start = new Date(startDate)
    const end = new Date(endDate)
    const diffTime = Math.abs(end - start)
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24)) + 1
    return diffDays * (dailyHours || 8) // افتراضي 8 ساعات يومياً
  }

  // حساب نسبة الإكمال بناءً على التاريخ
  const calculateTimeBasedProgress = (workshop) => {
    if (!workshop.startDate || !workshop.endDate) return 0

    const start = new Date(workshop.startDate)
    const end = new Date(workshop.endDate)
    const now = new Date()

    // إذا لم تبدأ بعد
    if (now < start) return 0

    // إذا انتهت
    if (now > end || workshop.status === 'completed') return 100

    // حساب النسبة بناءً على الوقت المنقضي
    const total = end.getTime() - start.getTime()
    const elapsed = now.getTime() - start.getTime()
    return Math.max(0, Math.min(100, Math.round((elapsed / total) * 100)))
  }

  // Button handlers
  const handleCreateWorkshop = () => {
    setShowCreateModal(true)
  }

  const handleWorkshopCreated = (newWorkshop) => {
    // إعادة تحميل ورش العمل من التخزين لضمان التحديث
    loadWorkshops()
    setShowCreateModal(false)
    toast.success(t('workshops.createSuccess'))
  }



  const handleGenerateGroups = (workshop) => {
    setSelectedWorkshop(workshop)
    setShowGroupModal(true)
  }

  const handleGroupsCreated = (groups) => {
    // تحديث عدد المجموعات في ورشة العمل
    setWorkshops(prev => prev.map(workshop =>
      workshop.id === selectedWorkshop.id
        ? { ...workshop, groupsCount: groups.length }
        : workshop
    ))

    setShowGroupModal(false)
    setSelectedWorkshop(null)

    toast.success(t('workshops.groupsCreateSuccess'))
  }

  const handleExportWorkshops = async () => {
    setIsExporting(true)
    try {
      // استيراد ExcelJS
      const ExcelJS = await import('exceljs')
      const { saveAs } = await import('file-saver')

      // الحصول على ألوان المستخدم المخصصة
      const getUserThemeColors = () => {
        const currentUser = JSON.parse(localStorage.getItem('currentUser') || '{}')
        const userId = currentUser.id || 'default'

        return {
          primary: localStorage.getItem(`primaryColor_${userId}`) || '#4F46E5',
          secondary: localStorage.getItem(`secondaryColor_${userId}`) || '#7C3AED'
        }
      }

      const colors = getUserThemeColors()

      // إنشاء workbook جديد
      const workbook = new ExcelJS.Workbook()
      const worksheet = workbook.addWorksheet(t('workshops.workshopReport'), {
        rightToLeft: true
      })

      // إضافة عنوان رئيسي
      worksheet.mergeCells('A1:M1')
      const titleCell = worksheet.getCell('A1')
      titleCell.value = `📊 ${t('workshops.workshopReport')}`
      titleCell.font = { size: 16, bold: true, color: { argb: 'FFFFFFFF' } }
      titleCell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: colors.primary.replace('#', 'FF') }
      }
      titleCell.alignment = { horizontal: 'center', vertical: 'middle' }

      // إضافة معلومات التقرير
      worksheet.mergeCells('A2:M2')
      const infoCell = worksheet.getCell('A2')
      infoCell.value = `تاريخ التقرير: ${new Date().toLocaleDateString('ar-SA')} | إجمالي ورش العمل: ${filteredWorkshops.length}`
      infoCell.font = { size: 10, italic: true }
      infoCell.alignment = { horizontal: 'center' }

      // رؤوس الأعمدة
      const headers = [
        'اسم ورشة العمل', 'المدرب', 'تاريخ البداية', 'وقت البداية',
        'تاريخ النهاية', 'وقت النهاية', 'المدة (دقيقة)', 'الحالة',
        'عدد المشاركين', 'الحد الأقصى', 'المجموعات', 'الوصف', 'المتدربين المسجلين'
      ]

      // إضافة رؤوس الأعمدة
      headers.forEach((header, index) => {
        const cell = worksheet.getCell(4, index + 1)
        cell.value = header
        cell.font = { bold: true, color: { argb: 'FFFFFFFF' } }
        cell.fill = {
          type: 'pattern',
          pattern: 'solid',
          fgColor: { argb: colors.secondary.replace('#', 'FF') }
        }
        cell.alignment = { horizontal: 'center', vertical: 'middle' }
        cell.border = {
          top: { style: 'thin' },
          left: { style: 'thin' },
          bottom: { style: 'thin' },
          right: { style: 'thin' }
        }
      })

      // إضافة بيانات ورش العمل
      filteredWorkshops.forEach((workshop, index) => {
        const rowNum = index + 5

        // تحضير قائمة المتدربين
        const participantsList = workshop.selectedTrainees?.map(trainee =>
          typeof trainee === 'string' ? trainee : trainee.name
        ).join(', ') || 'لا يوجد متدربين'

        // تحديد حالة الورشة مع رموز
        let statusDisplay = ''
        let statusColor = 'FF000000'
        switch(workshop.status) {
          case 'published':
            statusDisplay = '📅 منشورة'
            statusColor = 'FF0066CC'
            break
          case 'completed':
            statusDisplay = '✅ مكتملة'
            statusColor = 'FF00AA00'
            break
          case 'cancelled':
            statusDisplay = '❌ ملغية'
            statusColor = 'FFCC0000'
            break
          case 'ongoing':
            statusDisplay = '🟢 جارية'
            statusColor = 'FF00AA00'
            break
          default:
            statusDisplay = '❓ غير محددة'
            statusColor = 'FF666666'
        }

        const rowData = [
          workshop.title || 'غير محدد',
          workshop.trainer || 'غير محدد',
          workshop.startDate || 'غير محدد',
          workshop.startTime || 'غير محدد',
          workshop.endDate || workshop.startDate || 'غير محدد',
          workshop.endTime || 'غير محدد',
          workshop.duration || 480,
          statusDisplay,
          workshop.selectedTrainees?.length || 0,
          workshop.maxParticipants || 30,
          workshop.groupsCount || 0,
          workshop.description || 'لا يوجد وصف',
          participantsList
        ]

        rowData.forEach((value, colIndex) => {
          const cell = worksheet.getCell(rowNum, colIndex + 1)
          cell.value = value

          // تنسيق خاص لعمود الحالة
          if (colIndex === 7) {
            cell.font = { bold: true, color: { argb: statusColor } }
          }

          // حدود للخلايا
          cell.border = {
            top: { style: 'thin' },
            left: { style: 'thin' },
            bottom: { style: 'thin' },
            right: { style: 'thin' }
          }

          // تلوين الصفوف بالتناوب
          if (index % 2 === 0) {
            cell.fill = {
              type: 'pattern',
              pattern: 'solid',
              fgColor: { argb: 'FFF8F9FA' }
            }
          }
        })
      })

      // تحديد عرض الأعمدة
      const columnWidths = [30, 20, 15, 12, 15, 12, 15, 15, 12, 12, 12, 40, 50]
      columnWidths.forEach((width, index) => {
        worksheet.getColumn(index + 1).width = width
      })

      // حفظ الملف
      const buffer = await workbook.xlsx.writeBuffer()
      const blob = new Blob([buffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' })
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-').split('T')[0]
      const fileName = `ورش_العمل_${timestamp}.xlsx`

      saveAs(blob, fileName)

      toast.success(`✅ تم تصدير ${filteredWorkshops.length} ورشة عمل بنجاح!`)
      console.log('✅ تم تصدير ورش العمل بتنسيق متقدم')

    } catch (error) {
      console.error('❌ خطأ في التصدير:', error)
      toast.error(`${t('workshops.exportFailed')}: ${error.message}`)
    } finally {
      setIsExporting(false)
    }
  }


  const handleImportFromExcel = async (file) => {
    try {
      toast.info(t('workshops.importInProgress'))

      // محاكاة قراءة الملف
      await new Promise(resolve => setTimeout(resolve, 2000))

      // في التطبيق الحقيقي، ستقوم بقراءة الملف وتحليله
      toast.success(t('workshops.importSuccess'))
      loadWorkshops() // إعادة تحميل ورش العمل
    } catch (error) {
      console.error('Error importing workshops:', error)
      toast.error(t('workshops.importFailed'))
    }
  }

  const handleViewWorkshop = (workshopId) => {
    navigate(`/workshops/${workshopId}`)
  }

  const handleEditWorkshop = (workshop) => {
    setSelectedWorkshop(workshop)
    setShowCreateModal(true)
  }

  const handleDeleteWorkshop = async (workshopId) => {
    if (confirm(t('workshops.confirmDelete'))) {
      try {
        console.log('🗑️ بدء حذف ورشة العمل:', workshopId)

        // حذف ورشة العمل من القائمة الرئيسية
        const updatedWorkshops = workshops.filter(w => w.id !== workshopId)
        setWorkshops(updatedWorkshops)
        dataStorage.saveWorkshops(updatedWorkshops)

        // حذف من app_workshops أيضاً
        const appWorkshops = JSON.parse(localStorage.getItem('app_workshops') || '[]')
        const updatedAppWorkshops = appWorkshops.filter(w => w.id !== workshopId)
        localStorage.setItem('app_workshops', JSON.stringify(updatedAppWorkshops))

        // حذف من workshops_data إذا كانت موجودة
        const workshopsData = JSON.parse(localStorage.getItem('workshops_data') || '[]')
        const updatedWorkshopsData = workshopsData.filter(w => w.id !== workshopId)
        localStorage.setItem('workshops_data', JSON.stringify(updatedWorkshopsData))

        console.log('✅ تم حذف ورشة العمل من جميع المصادر')

        // حذف البيانات المرتبطة
        localStorage.removeItem(`workshop_materials_${workshopId}`)
        localStorage.removeItem(`workshop_groups_${workshopId}`)
        localStorage.removeItem(`workshop_content_${workshopId}`)
        localStorage.removeItem(`workshop_chat_${workshopId}`)
        localStorage.removeItem(`workshop_chat_settings_${workshopId}`)
        localStorage.removeItem(`workshop_quizzes_${workshopId}`)

        // حذف المجموعات من dataStorage
        const allGroups = JSON.parse(localStorage.getItem('workshop_groups') || '{}')
        delete allGroups[workshopId]
        localStorage.setItem('workshop_groups', JSON.stringify(allGroups))

        console.log('✅ تم حذف جميع البيانات المرتبطة')

        // حذف جميع الأحداث المرتبطة بورشة العمل من التايم لاين
        try {
          const { timelineService } = await import('../../services/timelineService')
          timelineService.removeWorkshopEvents(workshopId)
          console.log('✅ تم حذف جميع أحداث ورشة العمل من التايم لاين')
        } catch (timelineError) {
          console.error('خطأ في حذف الحدث من التايم لاين:', timelineError)
        }

        // إعادة تحميل البيانات
        loadWorkshops()

        toast.success(t('workshops.deleteSuccess'))
        console.log('🎉 تم الانتهاء من حذف ورشة العمل بنجاح')

      } catch (error) {
        console.error('❌ خطأ في حذف ورشة العمل:', error)
        toast.error(t('workshops.deleteFailed'))
      }
    }
  }

  const handleManageMaterials = (workshopId) => {
    navigate(`/workshops/${workshopId}/materials`)
  }

  const handleAssignParticipants = (workshopId) => {
    navigate(`/workshops/${workshopId}/assign`)
  }

  const handleManageGroups = (workshopId) => {
    navigate(`/workshops/${workshopId}/groups`)
  }

  const handleGradeWorkshop = (workshopId) => {
    navigate(`/workshops/${workshopId}/grade`)
  }

  const handleMoreFilters = () => {
    toast.info('More filters - Feature coming soon!')
  }

  const handleRunFixScript = () => {
    toast.info('تم حذف جميع السكريبتات الوهمية - النظام نظيف بالفعل!')
  }

  const handleResetFixScript = () => {
    toast.info('تم حذف جميع السكريبتات الوهمية - لا حاجة لإعادة تعيين!')
  }

  const handleEmergencyFix = () => {
    toast.info('تم حذف جميع السكريبتات الوهمية - النظام نظيف بالفعل!')
  }



  const stats = [
    { name: t('workshops.totalWorkshops'), value: workshops.length, icon: Calendar, color: 'bg-blue-500' },
    { name: t('workshops.ongoing'), value: workshops.filter(w => w.status === 'ongoing').length, icon: PlayCircle, color: 'bg-green-500' },
    { name: t('workshops.totalParticipants'), value: workshops.reduce((sum, w) => sum + (w.selectedTrainees?.length || 0), 0), icon: Users, color: 'bg-purple-500' },
    { name: t('workshops.avgGroupScore'), value: workshops.length > 0 ? Math.round(workshops.reduce((sum, w) => sum + (w.avgGroupScore || 0), 0) / workshops.length) + '%' : '0%', icon: Award, color: 'bg-orange-500' }
  ]

  // عرض حالة التحميل
  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary-600"></div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100">
            {t('workshops.title')}
          </h1>
          <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
            {t('workshops.subtitle')}
          </p>
        </div>
        <div className="mt-4 sm:mt-0 flex flex-col sm:flex-row gap-3">
          {/* زر تصدير XLSX */}
          <Button
            variant="outline"
            onClick={handleExportWorkshops}
            loading={isExporting}
            icon={Download}
          >
            تصدير XLSX
          </Button>

          {/* زر استيراد من Excel */}
          <input
            type="file"
            accept=".xlsx,.xls"
            onChange={(e) => {
              if (e.target.files[0]) {
                handleImportFromExcel(e.target.files[0])
              }
            }}
            className="hidden"
            id="excel-import"
          />
          <Button
            variant="outline"
            onClick={() => document.getElementById('excel-import').click()}
            icon={Upload}
          >
            {t('workshops.importFromExcel')}
          </Button>

          {/* زر إنشاء ورشة عمل */}
          <Button
            variant="primary"
            onClick={handleCreateWorkshop}
            icon={Plus}
          >
            {t('workshops.createWorkshop')}
          </Button>
        </div>
      </div>

      {/* Stats */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
        {stats.map((stat, index) => {
          const Icon = stat.icon
          return (
            <motion.div
              key={stat.name}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
              className="card"
            >
              <div className="flex items-center">
                <div className={`p-3 rounded-xl ${stat.color}`}>
                  <Icon className="h-6 w-6 text-white" />
                </div>
                <div className="ml-4 rtl:ml-0 rtl:mr-4">
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                    {stat.name}
                  </p>
                  <p className="text-2xl font-semibold text-gray-900 dark:text-gray-100">
                    {stat.value}
                  </p>
                </div>
              </div>
            </motion.div>
          )
        })}
      </div>

      {/* Filters and Search */}
      <div className="card">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0">
          <div className="flex flex-col sm:flex-row gap-4">
            {/* Search */}
            <div className="relative">
              <Search className="absolute left-3 rtl:left-auto rtl:right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <input
                type="text"
                placeholder={t('workshops.search')}
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 rtl:pl-4 rtl:pr-10 input-field w-full sm:w-64"
              />
            </div>

            {/* Status Filter */}
            <select
              value={selectedFilter}
              onChange={(e) => setSelectedFilter(e.target.value)}
              className="input-field w-full sm:w-auto"
            >
              <option value="all">{t('workshops.allStatus')}</option>
              <option value="scheduled">{t('workshops.scheduled')}</option>
              <option value="ongoing">{t('workshops.ongoing')}</option>
              <option value="completed">{t('workshops.completed')}</option>
              <option value="draft">{t('workshops.draft')}</option>
              <option value="cancelled">{t('workshops.cancelled')}</option>
            </select>
          </div>

          <div className="flex flex-wrap gap-2">
            <Button
              variant="outline"
              onClick={handleMoreFilters}
              icon={Filter}
            >
              {t('workshops.moreFilters')}
            </Button>

          </div>
        </div>
      </div>

      {/* Workshops Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 card-grid">
        {filteredWorkshops.map((workshop, index) => {
          const daysUntilStart = getDaysUntilStart(workshop.startDate)
          const totalHours = getTotalHours(workshop.startDate, workshop.endDate, workshop.dailyHours)
          const enrollmentRate = Math.round(((workshop.selectedTrainees?.length || 0) / (workshop.maxParticipants || 30)) * 100)
          const timeBasedProgress = calculateTimeBasedProgress(workshop)

          // تحديد العدد الأقصى للمشاركين بناءً على البيانات الفعلية
          const maxParticipants = workshop.maxParticipants || workshop.selectedTrainees?.length || 30

          return (
            <motion.div
              key={workshop.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
              className="card h-full flex flex-col hover:shadow-soft-lg transition-all duration-200"
            >
              {/* Workshop Header */}
              <div className="flex items-start justify-between mb-4 flex-grow">
                <div className="flex-1 min-w-0">
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2 truncate">
                    {workshop.title}
                  </h3>
                  <p className="text-sm text-gray-600 dark:text-gray-400 line-clamp-2 mb-3 min-h-[2.5rem]">
                    {workshop.description}
                  </p>
                  <div className="flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-4 text-sm text-gray-500 dark:text-gray-400">
                    <div className="flex items-center">
                      <User className="w-4 h-4 mr-1 rtl:mr-0 rtl:ml-1" />
                      {workshop.trainer}
                    </div>
                    <div className="flex items-center">
                      <FileText className="w-4 h-4 mr-1 rtl:mr-0 rtl:ml-1" />
                      {workshop.course}
                    </div>
                  </div>
                </div>
                <span className={`px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(workshop.status)}`}>
                  {workshop.status.charAt(0).toUpperCase() + workshop.status.slice(1)}
                </span>
              </div>

              {/* Workshop Details */}
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mb-4">
                <div className="space-y-2">
                  <div className="flex items-center text-sm text-gray-500 dark:text-gray-400">
                    <Calendar className="w-4 h-4 mr-2 rtl:mr-0 rtl:ml-2" />
                    <span>
                      {new Date(workshop.startDate).toLocaleDateString()} - {new Date(workshop.endDate).toLocaleDateString()}
                    </span>
                  </div>
                  <div className="flex items-center text-sm text-gray-500 dark:text-gray-400">
                    <Clock className="w-4 h-4 mr-2 rtl:mr-0 rtl:ml-2" />
                    <span>{workshop.dailyHours || 8} ساعات/يوم • {totalHours} ساعة إجمالي</span>
                  </div>
                </div>
                <div className="space-y-2">
                  <div className="flex items-center text-sm text-gray-500 dark:text-gray-400">
                    <Users className="w-4 h-4 mr-2 rtl:mr-0 rtl:ml-2" />
                    <span>{workshop.selectedTrainees?.length || 0}/{maxParticipants} مسجل</span>
                  </div>
                  <div className="flex items-center text-sm text-gray-500 dark:text-gray-400">
                    <Shuffle className="w-4 h-4 mr-2 rtl:mr-0 rtl:ml-2" />
                    <span>{workshop.groupsCount} {t('workshops.groups')}</span>
                  </div>
                </div>
              </div>

              {/* Progress Indicators */}
              <div className="space-y-3 mb-4">
                {/* Time-based Progress */}
                <div>
                  <div className="flex justify-between text-xs text-gray-500 dark:text-gray-400 mb-1">
                    <span>التقدم الزمني</span>
                    <span>{timeBasedProgress}%</span>
                  </div>
                  <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                    <div
                      className="bg-green-500 h-2 rounded-full transition-all duration-300"
                      style={{ width: `${timeBasedProgress}%` }}
                    ></div>
                  </div>
                </div>

                {/* Group Score (if completed) */}
                {workshop.status === 'completed' && workshop.avgGroupScore > 0 && (
                  <div>
                    <div className="flex justify-between text-xs text-gray-500 dark:text-gray-400 mb-1">
                      <span>{t('workshops.avgScore')}</span>
                      <span>{workshop.avgGroupScore}%</span>
                    </div>
                    <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                      <div
                        className="bg-green-500 h-2 rounded-full transition-all duration-300"
                        style={{ width: `${workshop.avgGroupScore}%` }}
                      ></div>
                    </div>
                  </div>
                )}
              </div>

              {/* Status Info */}
              {workshop.status === 'scheduled' && daysUntilStart > 0 && (
                <div className="mb-4 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                  <div className="flex items-center text-blue-800 dark:text-blue-200">
                    <Calendar className="w-4 h-4 mr-2 rtl:mr-0 rtl:ml-2" />
                    <span className="text-sm">
                      {t('workshops.startsIn')} {daysUntilStart} {daysUntilStart !== 1 ? t('workshops.days') : t('workshops.day')}
                    </span>
                  </div>
                </div>
              )}

              {workshop.status === 'ongoing' && (
                <div className="mb-4 p-3 bg-green-50 dark:bg-green-900/20 rounded-lg">
                  <div className="flex items-center text-green-800 dark:text-green-200">
                    <PlayCircle className="w-4 h-4 mr-2 rtl:mr-0 rtl:ml-2" />
                    <span className="text-sm">{t('workshops.currentlyRunning')}</span>
                  </div>
                </div>
              )}

              {workshop.status === 'completed' && (
                <div className="mb-4 p-3 bg-purple-50 dark:bg-purple-900/20 rounded-lg">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center text-purple-800 dark:text-purple-200">
                      <Award className="w-4 h-4 mr-2 rtl:mr-0 rtl:ml-2" />
                      <span className="text-sm">{t('workshops.completedStatus')}</span>
                    </div>
                    {workshop.avgGroupScore > 0 && (
                      <span className="text-sm font-medium text-purple-800 dark:text-purple-200">
                        {t('workshops.avgScoreLabel')}: {workshop.avgGroupScore}%
                      </span>
                    )}
                  </div>
                </div>
              )}

              {/* Action Buttons */}
              <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 pt-4 border-t border-gray-200 dark:border-gray-700 mt-auto">
                <div className="flex flex-wrap gap-2">
                  <Button
                    variant="primary"
                    size="sm"
                    onClick={() => handleViewWorkshop(workshop.id)}
                    icon={Eye}
                  >
                    {t('workshops.viewWorkshop')}
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleEditWorkshop(workshop)}
                    icon={Edit}
                  >
                    {t('common.edit')}
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleDeleteWorkshop(workshop.id)}
                    icon={Trash2}
                    className="text-red-600 hover:text-red-700 hover:bg-red-50 dark:hover:bg-red-900/20"
                  >
                    حذف
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleManageMaterials(workshop.id)}
                    icon={Upload}
                  >
                    {t('workshops.materials')}
                  </Button>
                </div>

                <div className="flex flex-wrap gap-2">
                  {workshop.status === 'scheduled' && (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleAssignParticipants(workshop.id)}
                      icon={Users}
                    >
                      {t('workshops.assign')}
                    </Button>
                  )}
                  {(workshop.status === 'scheduled' || workshop.status === 'published') && (workshop.selectedTrainees?.length || 0) > 0 && (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleGenerateGroups(workshop)}
                      icon={Shuffle}
                    >
                      {t('workshops.generateGroups')}
                    </Button>
                  )}
                  {workshop.groupsCount > 0 && (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleManageGroups(workshop.id)}
                      icon={MessageSquare}
                    >
                      {t('workshops.manageGroups')}
                    </Button>
                  )}
                  {(workshop.status === 'ongoing' || workshop.status === 'completed') && (
                    <Button
                      variant="primary"
                      size="sm"
                      onClick={() => handleGradeWorkshop(workshop.id)}
                      icon={Award}
                    >
                      {t('workshops.grade')}
                    </Button>
                  )}
                </div>
              </div>
            </motion.div>
          )
        })}
      </div>

      {filteredWorkshops.length === 0 && !loading && (
        <div className="text-center py-12">
          <Calendar className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-gray-100">
            {workshops.length === 0 ? t('workshops.noWorkshops') : t('workshops.noResults')}
          </h3>
          <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
            {workshops.length === 0
              ? t('workshops.startCreating')
              : t('workshops.noMatchingWorkshops')
            }
          </p>
          <div className="mt-6">
            <Button
              variant="primary"
              onClick={handleCreateWorkshop}
              icon={Plus}
            >
              {t('workshops.createNewWorkshop')}
            </Button>
          </div>
        </div>
      )}

      {/* Create Workshop Modal */}
      <CreateWorkshopModal
        isOpen={showCreateModal}
        onClose={() => {
          setShowCreateModal(false)
          setSelectedWorkshop(null)
        }}
        onWorkshopCreated={handleWorkshopCreated}
        editWorkshop={selectedWorkshop}
      />

      {/* Create Groups Modal */}
      <CreateGroupModal
        isOpen={showGroupModal}
        onClose={() => setShowGroupModal(false)}
        workshop={selectedWorkshop}
        onGroupsCreated={handleGroupsCreated}
      />
    </div>
  )
}

export default WorkshopManagement
