import React, { useState } from 'react'
import { motion } from 'framer-motion'
import { Plus, Minus, Trophy, Star, Target } from 'lucide-react'
import toast from 'react-hot-toast'
import dataStorage from '../../utils/dataStorage'

const QuickPointsManager = ({ workshopId, groups, onPointsUpdate }) => {
  const [selectedGroup, setSelectedGroup] = useState(null)

  // خيارات النقاط السريعة
  const quickPoints = [
    { value: 500, color: 'bg-green-500', icon: Trophy, label: '+500' },
    { value: 400, color: 'bg-blue-500', icon: Star, label: '+400' },
    { value: 300, color: 'bg-purple-500', icon: Target, label: '+300' },
    { value: 200, color: 'bg-yellow-500', icon: Plus, label: '+200' },
    { value: 100, color: 'bg-gray-500', icon: Plus, label: '+100' },
    { value: -100, color: 'bg-red-300', icon: Minus, label: '-100' },
    { value: -200, color: 'bg-red-400', icon: Minus, label: '-200' },
    { value: -300, color: 'bg-red-500', icon: Minus, label: '-300' },
    { value: -400, color: 'bg-red-600', icon: Minus, label: '-400' },
    { value: -500, color: 'bg-red-700', icon: Minus, label: '-500' }
  ]

  const handlePointsAdd = (groupId, points) => {
    // تحديث النقاط في التخزين
    dataStorage.updateGroupPoints(workshopId, groupId, points)

    // إشعار المستخدم
    const group = groups.find(g => g.id === groupId)
    const action = points > 0 ? 'إضافة' : 'خصم'
    const pointsText = Math.abs(points)

    toast.success(`تم ${action} ${pointsText} نقطة ${points > 0 ? 'إلى' : 'من'} مجموعة ${group?.name}`)

    // تحديث الواجهة
    if (onPointsUpdate) {
      onPointsUpdate()
    }
  }

  const getCurrentPoints = (groupId) => {
    const allPoints = dataStorage.loadPoints(workshopId)
    return allPoints[groupId] || 0
  }

  return (
    <div className="space-y-6">
      {/* عنوان */}
      <div className="text-center">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2">
          إدارة النقاط السريعة
        </h3>
        <p className="text-sm text-gray-600 dark:text-gray-400">
          اضغط على المجموعة ثم اختر النقاط لإضافتها أو خصمها
        </p>
      </div>

      {/* اختيار المجموعة */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {(groups || []).map((group) => {
          const currentPoints = getCurrentPoints(group.id)
          const isSelected = selectedGroup === group.id
          
          return (
            <motion.div
              key={group.id}
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              onClick={() => setSelectedGroup(group.id)}
              className={`p-4 rounded-lg border-2 cursor-pointer transition-all ${
                isSelected
                  ? 'border-primary-500 bg-primary-50 dark:bg-primary-900/20'
                  : 'border-gray-200 dark:border-gray-600 hover:border-gray-300 dark:hover:border-gray-500'
              }`}
            >
              <div className="text-center">
                <h4 className="font-medium text-gray-900 dark:text-gray-100 mb-2">
                  {group.name}
                </h4>
                <div className="text-2xl font-bold text-primary-600 dark:text-primary-400 mb-2">
                  {currentPoints} نقطة
                </div>
                <div className="text-sm text-gray-500 dark:text-gray-400">
                  {group.members?.length || group.trainees?.length || 0} أعضاء
                </div>
                {isSelected && (
                  <motion.div
                    initial={{ scale: 0 }}
                    animate={{ scale: 1 }}
                    className="mt-2 w-3 h-3 bg-primary-500 rounded-full mx-auto"
                  />
                )}
              </div>
            </motion.div>
          )
        })}
      </div>

      {/* أزرار النقاط السريعة */}
      {selectedGroup && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-white dark:bg-gray-800 rounded-lg p-6 border border-gray-200 dark:border-gray-600"
        >
          <div className="text-center mb-4">
            <h4 className="font-medium text-gray-900 dark:text-gray-100">
              إضافة/خصم نقاط لمجموعة: {groups.find(g => g.id === selectedGroup)?.name}
            </h4>
          </div>

          <div className="grid grid-cols-5 gap-3">
            {quickPoints.map((point, index) => {
              const Icon = point.icon
              const isPositive = point.value > 0
              
              return (
                <motion.button
                  key={index}
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  onClick={() => handlePointsAdd(selectedGroup, point.value)}
                  className={`${point.color} text-white p-3 rounded-lg font-semibold text-sm flex flex-col items-center space-y-1 hover:opacity-90 transition-opacity`}
                >
                  <Icon className="w-5 h-5" />
                  <span>{point.label}</span>
                </motion.button>
              )
            })}
          </div>

          {/* نقاط مخصصة */}
          <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-600">
            <div className="flex items-center space-x-3 rtl:space-x-reverse">
              <input
                type="number"
                placeholder="نقاط مخصصة"
                className="flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 dark:bg-gray-700 dark:text-gray-100"
                onKeyPress={(e) => {
                  if (e.key === 'Enter') {
                    const value = parseInt(e.target.value)
                    if (value && !isNaN(value)) {
                      handlePointsAdd(selectedGroup, value)
                      e.target.value = ''
                    }
                  }
                }}
              />
              <button
                onClick={(e) => {
                  const input = e.target.parentElement.querySelector('input')
                  const value = parseInt(input.value)
                  if (value && !isNaN(value)) {
                    handlePointsAdd(selectedGroup, value)
                    input.value = ''
                  }
                }}
                className="px-4 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700 transition-colors"
              >
                إضافة
              </button>
            </div>
          </div>
        </motion.div>
      )}

      {/* ترتيب المجموعات */}
      <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
        <h4 className="font-medium text-gray-900 dark:text-gray-100 mb-3 text-center">
          🏆 ترتيب المجموعات
        </h4>
        <div className="space-y-2">
          {groups
            .map(group => ({
              ...group,
              points: getCurrentPoints(group.id)
            }))
            .sort((a, b) => b.points - a.points)
            .map((group, index) => (
              <div
                key={group.id}
                className={`flex items-center justify-between p-3 rounded-lg ${
                  index === 0
                    ? 'bg-yellow-100 dark:bg-yellow-900/20 border border-yellow-300 dark:border-yellow-600'
                    : 'bg-white dark:bg-gray-800'
                }`}
              >
                <div className="flex items-center space-x-3 rtl:space-x-reverse">
                  <div className={`w-8 h-8 rounded-full flex items-center justify-center font-bold text-sm ${
                    index === 0
                      ? 'bg-yellow-500 text-white'
                      : index === 1
                      ? 'bg-gray-400 text-white'
                      : index === 2
                      ? 'bg-orange-400 text-white'
                      : 'bg-gray-200 dark:bg-gray-600 text-gray-700 dark:text-gray-300'
                  }`}>
                    {index + 1}
                  </div>
                  <div>
                    <div className="font-medium text-gray-900 dark:text-gray-100">
                      {group.name}
                    </div>
                    <div className="text-sm text-gray-500 dark:text-gray-400">
                      {group.members?.length || group.trainees?.length || 0} أعضاء
                    </div>
                  </div>
                </div>
                <div className="text-right">
                  <div className="font-bold text-lg text-primary-600 dark:text-primary-400">
                    {group.points}
                  </div>
                  <div className="text-sm text-gray-500 dark:text-gray-400">
                    نقطة
                  </div>
                </div>
              </div>
            ))}
        </div>
      </div>
    </div>
  )
}

export default QuickPointsManager
