import React, { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import toast from 'react-hot-toast'
import { User, Mail, Phone, Lock, UserCheck, Building } from 'lucide-react'
import Modal from '../ui/Modal'
import Button from '../ui/Button'
import { useLanguage } from '../../contexts/LanguageContext'

const CreateUserModal = ({ isOpen, onClose, onUserCreated }) => {
  const { t, language } = useLanguage()
  const [loading, setLoading] = useState(false)
  
  // دالة مساعدة للترجمة
  const getText = (ar, en) => language === 'ar' ? ar : en
  
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    role: 'trainee',
    department: '',
    gender: '',
    password: '',
    confirmPassword: ''
  })
  const [departments, setDepartments] = useState([])
  const [errors, setErrors] = useState({})

  // تحميل الأقسام من الإعدادات
  useEffect(() => {
    const loadDepartments = () => {
      try {
        // تحميل الأقسام من departments_data فقط
        const departmentsData = JSON.parse(localStorage.getItem('departments_data') || '[]')

        if (departmentsData.length > 0) {
          const departmentNames = departmentsData.map(dept => dept.name)
          setDepartments(departmentNames)
          console.log('📋 Departments loaded:', departmentNames)
        } else {
          // No departments added
          setDepartments([])
          console.log('⚠️ No departments added to the system')
        }
      } catch (error) {
        console.error('Error loading departments:', error)
        setDepartments([])
      }
    }
    loadDepartments()
  }, [])

  const handleInputChange = (e) => {
    const { name, value } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: value
    }))
    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }))
    }
  }

  const validateForm = () => {
    const newErrors = {}

    if (!formData.name.trim()) {
      newErrors.name = getText('الاسم مطلوب', 'Name is required')
    }

    if (!formData.email.trim()) {
      newErrors.email = getText('البريد الإلكتروني مطلوب', 'Email is required')
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = getText('البريد الإلكتروني غير صالح', 'Invalid email format')
    }

    if (!formData.role.trim()) {
      newErrors.role = getText('الدور مطلوب', 'Role is required')
    }

    if (!formData.password.trim()) {
      newErrors.password = getText('كلمة المرور مطلوبة', 'Password is required')
    } else if (formData.password.length < 6) {
      newErrors.password = getText('كلمة المرور يجب أن تكون 6 أحرف على الأقل', 'Password must be at least 6 characters')
    }

    if (formData.password !== formData.confirmPassword) {
      newErrors.confirmPassword = getText('كلمات المرور غير متطابقة', 'Passwords do not match')
    }

    if (!formData.gender.trim()) {
      newErrors.gender = getText('الجنس مطلوب', 'Gender is required')
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = async (e) => {
    e.preventDefault()
    
    if (!validateForm()) {
      return
    }

    setLoading(true)
    try {
      console.log('📝 CreateUserModal: Starting user creation with data:', formData)

      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1500))

      const newUser = {
        name: formData.name,
        email: formData.email,
        phone: formData.phone,
        role: formData.role,
        department: formData.department,
        gender: formData.gender,
        password: formData.password
      }

      console.log('📝 CreateUserModal: Prepared user data:', newUser)

      if (onUserCreated) {
        console.log('📝 CreateUserModal: Calling onUserCreated')
        await onUserCreated(newUser)
      }

      toast.success(getText('تم إنشاء المستخدم بنجاح!', 'User created successfully!'))
      handleClose()
    } catch (error) {
      toast.error(getText('فشل في إنشاء المستخدم', 'Failed to create user'))
      console.error('❌ CreateUserModal: Error creating user:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleClose = () => {
    setFormData({
      name: '',
      email: '',
      phone: '',
      role: 'trainee',
      department: '',
      gender: '',
      password: '',
      confirmPassword: ''
    })
    setErrors({})
    onClose()
  }

  return (
    <Modal
      isOpen={isOpen}
      onClose={handleClose}
      title={getText('إضافة مستخدم جديد', 'Add New User')}
      size="md"
    >
      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Name */}
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            {getText('الاسم الكامل *', 'Full Name *')}
          </label>
          <div className="relative">
            <User className="absolute left-3 rtl:left-auto rtl:right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <input
              type="text"
              name="name"
              value={formData.name}
              onChange={handleInputChange}
              className={`input-field pl-10 rtl:pl-4 rtl:pr-10 ${errors.name ? 'border-red-500' : ''}`}
              placeholder={getText('أدخل الاسم الكامل', 'Enter full name')}
            />
          </div>
          {errors.name && (
            <p className="mt-1 text-sm text-red-600">{errors.name}</p>
          )}
        </div>

        {/* Email */}
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            {getText('البريد الإلكتروني *', 'Email *')}
          </label>
          <div className="relative">
            <Mail className="absolute left-3 rtl:left-auto rtl:right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <input
              type="email"
              name="email"
              value={formData.email}
              onChange={handleInputChange}
              className={`input-field pl-10 rtl:pl-4 rtl:pr-10 ${errors.email ? 'border-red-500' : ''}`}
              placeholder={getText('أدخل البريد الإلكتروني', 'Enter email address')}
            />
          </div>
          {errors.email && (
            <p className="mt-1 text-sm text-red-600">{errors.email}</p>
          )}
        </div>

        {/* Phone */}
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            {getText('رقم الهاتف', 'Phone Number')}
          </label>
          <div className="relative">
            <Phone className="absolute left-3 rtl:left-auto rtl:right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <input
              type="tel"
              name="phone"
              value={formData.phone}
              onChange={handleInputChange}
              className="input-field pl-10 rtl:pl-4 rtl:pr-10"
              placeholder={getText('أدخل رقم الهاتف', 'Enter phone number')}
            />
          </div>
        </div>

        {/* Role */}
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            {getText('الدور *', 'Role *')}
          </label>
          <div className="relative">
            <UserCheck className="absolute left-3 rtl:left-auto rtl:right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <select
              name="role"
              value={formData.role}
              onChange={handleInputChange}
              className={`input-field pl-10 rtl:pl-4 rtl:pr-10 ${errors.role ? 'border-red-500' : ''}`}
            >
              <option value="trainee">{getText('متدرب', 'Trainee')}</option>
              <option value="trainer">{getText('مدرب', 'Trainer')}</option>
              <option value="admin">{getText('مدير', 'Admin')}</option>
            </select>
          </div>
          {errors.role && (
            <p className="mt-1 text-sm text-red-600">{errors.role}</p>
          )}
        </div>

        {/* Department */}
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            {getText('القسم', 'Department')}
          </label>
          <div className="relative">
            <Building className="absolute left-3 rtl:left-auto rtl:right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <select
              name="department"
              value={formData.department}
              onChange={handleInputChange}
              className="input-field pl-10 rtl:pl-4 rtl:pr-10"
            >
              <option value="">{getText('اختر القسم', 'Select Department')}</option>
              {departments.map((dept, index) => (
                <option key={index} value={dept}>{dept}</option>
              ))}
            </select>
          </div>
        </div>

        {/* Gender */}
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            {getText('الجنس *', 'Gender *')}
          </label>
          <div className="relative">
            <User className="absolute left-3 rtl:left-auto rtl:right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <select
              name="gender"
              value={formData.gender}
              onChange={handleInputChange}
              className={`input-field pl-10 rtl:pl-4 rtl:pr-10 ${errors.gender ? 'border-red-500' : ''}`}
            >
              <option value="">{getText('اختر الجنس', 'Select Gender')}</option>
              <option value="male">{getText('ذكر', 'Male')}</option>
              <option value="female">{getText('أنثى', 'Female')}</option>
            </select>
          </div>
          {errors.gender && (
            <p className="mt-1 text-sm text-red-600">{errors.gender}</p>
          )}
        </div>

        {/* Password */}
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            {getText('كلمة المرور *', 'Password *')}
          </label>
          <div className="relative">
            <Lock className="absolute left-3 rtl:left-auto rtl:right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <input
              type="password"
              name="password"
              value={formData.password}
              onChange={handleInputChange}
              className={`input-field pl-10 rtl:pl-4 rtl:pr-10 ${errors.password ? 'border-red-500' : ''}`}
              placeholder={getText('أدخل كلمة المرور', 'Enter password')}
            />
          </div>
          {errors.password && (
            <p className="mt-1 text-sm text-red-600">{errors.password}</p>
          )}
        </div>

        {/* Confirm Password */}
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            {getText('تأكيد كلمة المرور *', 'Confirm Password *')}
          </label>
          <div className="relative">
            <Lock className="absolute left-3 rtl:left-auto rtl:right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <input
              type="password"
              name="confirmPassword"
              value={formData.confirmPassword}
              onChange={handleInputChange}
              className={`input-field pl-10 rtl:pl-4 rtl:pr-10 ${errors.confirmPassword ? 'border-red-500' : ''}`}
              placeholder={getText('أعد إدخال كلمة المرور', 'Re-enter password')}
            />
          </div>
          {errors.confirmPassword && (
            <p className="mt-1 text-sm text-red-600">{errors.confirmPassword}</p>
          )}
        </div>

        {/* Submit Buttons */}
        <div className="flex justify-end space-x-3 rtl:space-x-reverse pt-6">
          <Button
            type="button"
            variant="secondary"
            onClick={handleClose}
            disabled={loading}
          >
            {getText('إلغاء', 'Cancel')}
          </Button>
          <Button
            type="submit"
            variant="primary"
            loading={loading}
            disabled={loading}
          >
            {loading ? getText('جاري الإنشاء...', 'Creating...') : getText('إنشاء المستخدم', 'Create User')}
          </Button>
        </div>
      </form>
    </Modal>
  )
}

export default CreateUserModal