import React, { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import toast from 'react-hot-toast'
import { User, Mail, Phone, Lock, UserCheck, Building } from 'lucide-react'
import Modal from '../ui/Modal'
import Button from '../ui/Button'
import { useLanguage } from '../../contexts/LanguageContext'

const CreateUserModal = ({ isOpen, onClose, onUserCreated }) => {
  const { t, language } = useLanguage()
  const [loading, setLoading] = useState(false)
  
  // دالة مساعدة للترجمة
  const getText = (ar, en) => language === 'ar' ? ar : en
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    role: 'trainee',
    department: '',
    gender: '',
    password: '',
    confirmPassword: ''
  })
  const [departments, setDepartments] = useState([])
  const [errors, setErrors] = useState({})

  // تحميل الأقسام من الإعدادات
  useEffect(() => {
    const loadDepartments = () => {
      try {
        // تحميل الأقسام من departments_data فقط
        const departmentsData = JSON.parse(localStorage.getItem('departments_data') || '[]')

        if (departmentsData.length > 0) {
          const departmentNames = departmentsData.map(dept => dept.name)
          setDepartments(departmentNames)
          console.log('📋 Departments loaded:', departmentNames)
        } else {
          // No departments added
          setDepartments([])
          console.log('⚠️ No departments added to the system')
        }
      } catch (error) {
        console.error('Error loading departments:', error)
        setDepartments([])
      }
    }
    loadDepartments()
  }, [])

  const handleInputChange = (e) => {
    const { name, value } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: value
    }))
    
    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }))
    }
  }

  const validateForm = () => {
    const newErrors = {}

    if (!formData.name.trim()) {
      newErrors.name = 'الاسم مطلوب'
    }

    // Email is now optional, but if provided, it should be valid
    if (formData.email.trim() && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      newErrors.email = 'تنسيق البريد الإلكتروني غير صحيح'
    }

    if (!formData.password) {
      newErrors.password = 'كلمة المرور مطلوبة'
    } else if (formData.password.length < 6) {
      newErrors.password = 'كلمة المرور يجب أن تكون 6 أحرف على الأقل'
    }

    if (formData.password !== formData.confirmPassword) {
      newErrors.confirmPassword = 'كلمات المرور غير متطابقة'
    }

    if (!formData.gender.trim()) {
      newErrors.gender = 'الجنس مطلوب'
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = async (e) => {
    e.preventDefault()
    
    if (!validateForm()) {
      return
    }

    setLoading(true)
    try {
      console.log('📝 CreateUserModal: Starting user creation with data:', formData)

      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1500))

      const newUser = {
        name: formData.name,
        email: formData.email,
        phone: formData.phone,
        role: formData.role,
        department: formData.department,
        gender: formData.gender,
        password: formData.password
      }

      console.log('📝 CreateUserModal: Prepared user data:', newUser)

      if (onUserCreated) {
        console.log('📝 CreateUserModal: Calling onUserCreated')
        await onUserCreated(newUser)
      }

      // لا نعرض toast هنا لأن الصفحة الرئيسية ستعرضه
      handleClose()
    } catch (error) {
      toast.error('فشل في إنشاء المستخدم')
      console.error('❌ CreateUserModal: Error creating user:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleClose = () => {
    setFormData({
      name: '',
      email: '',
      phone: '',
      role: 'trainee',
      department: '',
      gender: '',
      password: '',
      confirmPassword: ''
    })
    setErrors({})
    onClose()
  }

  return (
    <Modal
      isOpen={isOpen}
      onClose={handleClose}
      title={language === 'ar' ? 'إضافة مستخدم جديد' : 'Add New User'}
      size="md"
    >
      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Name */}
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            {language === 'ar' ? 'الاسم الكامل *' : 'Full Name *'}
          </label>
          <div className="relative">
            <User className="absolute left-3 rtl:left-auto rtl:right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <input
              type="text"
              name="name"
              value={formData.name}
              onChange={handleInputChange}
              className={`input-field pl-10 rtl:pl-4 rtl:pr-10 ${errors.name ? 'border-red-500' : ''}`}
              placeholder={getText('أدخل الاسم الكامل', 'Enter full name')}
            />
          </div>
          {errors.name && (
            <p className="mt-1 text-sm text-red-600">{errors.name}</p>
          )}
        </div>

        {/* Email */}
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            {getText('البريد الإلكتروني (اختياري)', 'Email (Optional)')}
          </label>
          <div className="relative">
            <Mail className="absolute left-3 rtl:left-auto rtl:right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <input
              type="email"
              name="email"
              value={formData.email}
              onChange={handleInputChange}
              className={`input-field pl-10 rtl:pl-4 rtl:pr-10 ${errors.email ? 'border-red-500' : ''}`}
              placeholder="أدخل البريد الإلكتروني (اختياري)"
            />
          </div>
          {errors.email && (
            <p className="mt-1 text-sm text-red-600">{errors.email}</p>
          )}
        </div>

        {/* Phone */}
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            رقم الهاتف
          </label>
          <div className="relative">
            <Phone className="absolute left-3 rtl:left-auto rtl:right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <input
              type="tel"
              name="phone"
              value={formData.phone}
              onChange={handleInputChange}
              className="input-field pl-10 rtl:pl-4 rtl:pr-10"
              placeholder="************"
            />
          </div>
        </div>

        {/* Role */}
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            الدور *
          </label>
          <div className="relative">
            <UserCheck className="absolute left-3 rtl:left-auto rtl:right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <select
              name="role"
              value={formData.role}
              onChange={handleInputChange}
              className="input-field pl-10 rtl:pl-4 rtl:pr-10"
            >
              <option value="trainee">متدرب</option>
              <option value="trainer">مدرب</option>
              <option value="course_manager">مدير دورة</option>
              <option value="admin">مدير النظام</option>
            </select>
          </div>
        </div>

        {/* Department */}
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            {t('common.department')}
          </label>
          <div className="relative">
            <Building className="absolute left-3 rtl:left-auto rtl:right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <select
              name="department"
              value={formData.department}
              onChange={handleInputChange}
              className="input-field pl-10 rtl:pl-4 rtl:pr-10"
              disabled={departments.length === 0}
            >
              <option value="">
                {departments.length === 0 ? t('department.noDepartments') : t('department.selectDepartment')}
              </option>
              {departments.map(dept => (
                <option key={dept} value={dept}>{dept}</option>
              ))}
            </select>
          </div>
          {departments.length === 0 && (
            <p className="mt-1 text-sm text-amber-600">
              {t('department.addFromSettings')}
            </p>
          )}
        </div>

        {/* Gender */}
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            {t('auth.gender')} *
          </label>
          <div className="relative">
            <UserCheck className="absolute left-3 rtl:left-auto rtl:right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <select
              name="gender"
              value={formData.gender}
              onChange={handleInputChange}
              className={`input-field pl-10 rtl:pl-4 rtl:pr-10 ${errors.gender ? 'border-red-500' : ''}`}
            >
              <option value="">{t('gender.selectGender')}</option>
              <option value="male">{t('gender.male')}</option>
              <option value="female">{t('gender.female')}</option>
            </select>
          </div>
          {errors.gender && (
            <p className="mt-1 text-sm text-red-600">{errors.gender}</p>
          )}
        </div>

        {/* Password */}
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            كلمة المرور *
          </label>
          <div className="relative">
            <Lock className="absolute left-3 rtl:left-auto rtl:right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <input
              type="password"
              name="password"
              value={formData.password}
              onChange={handleInputChange}
              className={`input-field pl-10 rtl:pl-4 rtl:pr-10 ${errors.password ? 'border-red-500' : ''}`}
              placeholder="أدخل كلمة المرور"
            />
          </div>
          {errors.password && (
            <p className="mt-1 text-sm text-red-600">{errors.password}</p>
          )}
        </div>

        {/* Confirm Password */}
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            تأكيد كلمة المرور *
          </label>
          <div className="relative">
            <Lock className="absolute left-3 rtl:left-auto rtl:right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <input
              type="password"
              name="confirmPassword"
              value={formData.confirmPassword}
              onChange={handleInputChange}
              className={`input-field pl-10 rtl:pl-4 rtl:pr-10 ${errors.confirmPassword ? 'border-red-500' : ''}`}
              placeholder="أكد كلمة المرور"
            />
          </div>
          {errors.confirmPassword && (
            <p className="mt-1 text-sm text-red-600">{errors.confirmPassword}</p>
          )}
        </div>

        {/* Actions */}
        <div className="flex space-x-3 rtl:space-x-reverse pt-4">
          <Button
            type="button"
            variant="outline"
            onClick={handleClose}
            className="flex-1"
            disabled={loading}
          >
            إلغاء
          </Button>
          <Button
            type="submit"
            variant="primary"
            loading={loading}
            className="flex-1"
          >
            إنشاء المستخدم
          </Button>
        </div>
      </form>
    </Modal>
  )
}

export default CreateUserModal
